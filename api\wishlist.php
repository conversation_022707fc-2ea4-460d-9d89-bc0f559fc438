<?php
/**
 * Wishlist API Endpoints
 * Handles AJAX requests for wishlist operations
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/wishlist_functions.php';

// Set JSON response headers
header('Content-Type: application/json');

// CSRF Protection
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
}

$action = $_GET['action'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($action) {
        case 'add':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $itemType = sanitize($_POST['item_type'] ?? '');
            $itemId = sanitize($_POST['item_id'] ?? '');
            
            if (!in_array($itemType, ['service', 'package'])) {
                throw new Exception('Invalid item type');
            }
            
            if (empty($itemId)) {
                throw new Exception('Item ID is required');
            }
            
            // Check if user is logged in
            if (isset($_SESSION['user_id'])) {
                $result = addToWishlist($_SESSION['user_id'], $itemType, $itemId);
            } else {
                $result = addToSessionWishlist($itemType, $itemId);
            }
            
            // Get updated wishlist count
            $count = isset($_SESSION['user_id']) 
                ? getWishlistCount($_SESSION['user_id'])
                : getSessionWishlistCount();
            
            $result['count'] = $count;
            echo json_encode($result);
            break;
            
        case 'remove':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $itemType = sanitize($_POST['item_type'] ?? '');
            $itemId = sanitize($_POST['item_id'] ?? '');
            
            if (!in_array($itemType, ['service', 'package'])) {
                throw new Exception('Invalid item type');
            }
            
            if (empty($itemId)) {
                throw new Exception('Item ID is required');
            }
            
            // Check if user is logged in
            if (isset($_SESSION['user_id'])) {
                $result = removeFromWishlist($_SESSION['user_id'], $itemType, $itemId);
            } else {
                $result = removeFromSessionWishlist($itemType, $itemId);
            }
            
            // Get updated wishlist count
            $count = isset($_SESSION['user_id']) 
                ? getWishlistCount($_SESSION['user_id'])
                : getSessionWishlistCount();
            
            $result['count'] = $count;
            echo json_encode($result);
            break;
            
        case 'toggle':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            $itemType = sanitize($_POST['item_type'] ?? '');
            $itemId = sanitize($_POST['item_id'] ?? '');
            
            if (!in_array($itemType, ['service', 'package'])) {
                throw new Exception('Invalid item type');
            }
            
            if (empty($itemId)) {
                throw new Exception('Item ID is required');
            }
            
            // Check if item is in wishlist
            $isInWishlist = isset($_SESSION['user_id']) 
                ? isInWishlist($_SESSION['user_id'], $itemType, $itemId)
                : isInSessionWishlist($itemType, $itemId);
            
            if ($isInWishlist) {
                // Remove from wishlist
                if (isset($_SESSION['user_id'])) {
                    $result = removeFromWishlist($_SESSION['user_id'], $itemType, $itemId);
                } else {
                    $result = removeFromSessionWishlist($itemType, $itemId);
                }
                $result['action'] = 'removed';
            } else {
                // Add to wishlist
                if (isset($_SESSION['user_id'])) {
                    $result = addToWishlist($_SESSION['user_id'], $itemType, $itemId);
                } else {
                    $result = addToSessionWishlist($itemType, $itemId);
                }
                $result['action'] = 'added';
            }
            
            // Get updated wishlist count
            $count = isset($_SESSION['user_id']) 
                ? getWishlistCount($_SESSION['user_id'])
                : getSessionWishlistCount();
            
            $result['count'] = $count;
            $result['inWishlist'] = !$isInWishlist;
            echo json_encode($result);
            break;
            
        case 'count':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed');
            }
            
            $count = isset($_SESSION['user_id']) 
                ? getWishlistCount($_SESSION['user_id'])
                : getSessionWishlistCount();
            
            echo json_encode(['success' => true, 'count' => $count]);
            break;
            
        case 'check':
            if ($method !== 'GET') {
                throw new Exception('Method not allowed');
            }
            
            $itemType = sanitize($_GET['item_type'] ?? '');
            $itemId = sanitize($_GET['item_id'] ?? '');
            
            if (!in_array($itemType, ['service', 'package'])) {
                throw new Exception('Invalid item type');
            }
            
            if (empty($itemId)) {
                throw new Exception('Item ID is required');
            }
            
            $inWishlist = isset($_SESSION['user_id']) 
                ? isInWishlist($_SESSION['user_id'], $itemType, $itemId)
                : isInSessionWishlist($itemType, $itemId);
            
            echo json_encode(['success' => true, 'inWishlist' => $inWishlist]);
            break;
            
        case 'clear':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            
            if (!isset($_SESSION['user_id'])) {
                throw new Exception('User not logged in');
            }
            
            $result = clearUserWishlist($_SESSION['user_id']);
            $result['count'] = 0;
            echo json_encode($result);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
}
?>
