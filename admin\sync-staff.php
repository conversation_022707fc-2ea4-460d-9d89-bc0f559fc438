<?php
session_start();
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/staff_schedule_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Sync staff members
$result = syncStaffFromUsers();

if ($result['success']) {
    $_SESSION['success'] = "Successfully synced {$result['synced']} staff members with schedule records.";
} else {
    $_SESSION['error'] = $result['error'];
}

// Redirect back to staff management
redirect('/admin/staff');
?>
