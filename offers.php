<?php
/**
 * Offers Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get all active offers
$offers = $database->fetchAll("
    SELECT * FROM offers
    WHERE is_active = 1 AND valid_from <= NOW() AND valid_to >= NOW()
    ORDER BY discount DESC, created_at DESC
");

$pageTitle = "Special Offers";
include __DIR__ . '/includes/header.php';
?>

    <!-- Hero Section -->
    <section class="relative py-32 bg-gradient-to-br from-salon-black via-gray-900 to-salon-black overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/8 via-transparent to-salon-gold/5 animate-gradient"></div>
        <div class="absolute -inset-1/2 bg-[radial-gradient(circle_at_30%_40%,rgba(247,197,66,0.08),transparent)] animate-spin-slow"></div>
        <div class="absolute -inset-1/2 bg-[radial-gradient(circle_at_70%_60%,rgba(247,197,66,0.04),transparent)] animate-spin-reverse"></div>
        
        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-2 h-2 bg-salon-gold/30 rounded-full animate-float-slow"></div>
        <div class="absolute top-40 right-20 w-3 h-3 bg-salon-gold/20 rounded-full animate-float-delayed"></div>
        <div class="absolute bottom-32 left-1/4 w-1 h-1 bg-salon-gold/40 rounded-full animate-float"></div>
        
        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <!-- Enhanced Badge -->
            <div class="inline-flex items-center bg-gradient-to-r from-salon-gold/15 to-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 animate-fade-in-up border border-salon-gold/20 backdrop-blur-sm">
                <i class="fas fa-fire mr-2 animate-pulse"></i>
                Limited Time Deals
                <i class="fas fa-sparkles ml-2 animate-twinkle"></i>
            </div>
            
            <!-- Enhanced Title -->
            <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 animate-fade-in-up leading-tight" style="animation-delay: 200ms;">
                <span class="block">Special</span>
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold animate-shimmer bg-[length:200%_100%]">Offers</span>
            </h1>
            
            <!-- Enhanced Description -->
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-12 animate-fade-in-up" style="animation-delay: 400ms;">
                Discover exclusive deals and promotions designed to pamper you. 
                <span class="text-salon-gold font-semibold">Save up to 50%</span> on premium beauty services and treatments.
            </p>
            
            <!-- Stats Counter -->
            <div class="flex flex-wrap justify-center gap-8 mb-8 animate-fade-in-up" style="animation-delay: 600ms;">
                <div class="text-center">
                    <div class="text-3xl font-bold text-salon-gold mb-1" data-counter="<?= count($offers) ?>">0</div>
                    <div class="text-sm text-gray-400 uppercase tracking-wide">Active Offers</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-salon-gold mb-1" data-counter="50">0</div>
                    <div class="text-sm text-gray-400 uppercase tracking-wide">Max Savings %</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-salon-gold mb-1" data-counter="5000">0</div>
                    <div class="text-sm text-gray-400 uppercase tracking-wide">Happy Customers</div>
                </div>
            </div>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style="animation-delay: 800ms;">
                <a href="#offers-grid" class="group bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-salon-gold/25 inline-flex items-center justify-center">
                    <i class="fas fa-tags mr-2 group-hover:animate-bounce"></i>
                    View All Offers
                    <i class="fas fa-arrow-down ml-2 group-hover:translate-y-1 transition-transform"></i>
                </a>
                <a href="<?= getBasePath() ?>/customer/book" class="group bg-transparent border-2 border-salon-gold text-salon-gold hover:bg-salon-gold hover:text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 inline-flex items-center justify-center backdrop-blur-sm">
                    <i class="fas fa-calendar-plus mr-2 group-hover:animate-pulse"></i>
                    Book Now
                </a>
            </div>
        </div>
    </section>

    <style>
    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    @keyframes shimmer {
        0% { background-position: -200% 50%; }
        100% { background-position: 200% 50%; }
    }
    
    @keyframes twinkle {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.5; transform: scale(1.2); }
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    @keyframes float-slow {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-15px) rotate(180deg); }
    }
    
    @keyframes float-delayed {
        0%, 100% { transform: translateY(0px) scale(1); }
        50% { transform: translateY(-8px) scale(1.1); }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .animate-gradient {
        background-size: 200% 200%;
        animation: gradient 15s ease infinite;
    }
    
    .animate-spin-slow {
        animation: spin 30s linear infinite;
    }
    
    .animate-spin-reverse {
        animation: spin 40s linear infinite reverse;
    }
    
    .animate-shimmer {
        background-size: 200% auto;
        animation: shimmer 3s linear infinite;
    }
    
    .animate-twinkle {
        animation: twinkle 2s ease-in-out infinite;
    }
    
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }
    
    .animate-float-slow {
        animation: float-slow 4s ease-in-out infinite;
    }
    
    .animate-float-delayed {
        animation: float-delayed 3.5s ease-in-out infinite 1s;
    }
    
    .animate-fade-in-up {
        animation: fadeInUp 1s ease forwards;
        opacity: 0;
    }

    html {
        scroll-behavior: smooth;
    }

    .animate-bounce-subtle {
        animation: bounce-subtle 2s infinite;
    }

    @keyframes bounce-subtle {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-3px); }
    }

    .animate-pulse-subtle {
        animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse-subtle {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    .copy-tooltip {
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        transition: all 0.3s ease;
        pointer-events: none;
        white-space: nowrap;
    }

    button:hover .copy-tooltip {
        opacity: 1;
        bottom: -25px;
    }
    </style>

    <!-- Offers Grid -->
    <section class="py-24 bg-salon-black" x-data="{ loaded: false }" x-init="setTimeout(() => loaded = true, 500)">
        <div class="max-w-7xl mx-auto px-6">
            <?php if (empty($offers)): ?>
                <!-- No Active Offers -->
                <div class="text-center py-16 animate-fade-in-up">
                    <i class="fas fa-tags text-6xl text-gray-600 mb-6"></i>
                    <h3 class="text-2xl font-bold text-white mb-4">No Active Offers</h3>
                    <p class="text-gray-400 mb-8">Check back soon for amazing deals and promotions!</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?= getBasePath() ?>/services.php" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold transition-colors">
                            View Services
                        </a>
                        <a href="<?= getBasePath() ?>/packages.php" class="bg-secondary-900 hover:bg-secondary-800 text-white px-8 py-3 rounded-lg font-semibold transition-colors border border-secondary-700">
                            View Packages
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($offers as $index => $offer): ?>
                        <div class="group relative transform transition-all duration-500 hover:translate-y-[-5px]" 
                             x-show="loaded" 
                             x-transition:enter="transition ease-out duration-500"
                             x-transition:enter-start="opacity-0 transform translate-y-8"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             style="animation-delay: <?= $index * 150 ?>ms">
                            <?php if ($offer['discount'] >= 30): ?>
                                <!-- Hot Deal Badge -->
                                <div class="absolute -top-4 left-4 z-10">
                                    <div class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-1 rounded-full text-sm font-bold shadow-lg animate-bounce-subtle">
                                        🔥 Hot Deal
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl overflow-hidden transition-all duration-500 
                                      hover:border-salon-gold/50 hover:shadow-2xl hover:shadow-salon-gold/10 group-hover:scale-[1.02]">
                                <!-- Offer Header -->
                                <div class="relative h-48 bg-gradient-to-br from-salon-gold/20 to-secondary-900 overflow-hidden">
                                    <?php if ($offer['image']): ?>
                                        <img src="<?= htmlspecialchars($offer['image']) ?>" alt="<?= htmlspecialchars($offer['title']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                                    <?php else: ?>
                                        <div class="w-full h-full flex items-center justify-center">
                                            <div class="text-center">
                                                <i class="fas fa-percent text-salon-gold/60 text-4xl mb-2"></i>
                                                <div class="text-gray-300 text-sm">Special Offer</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                                    
                                    <!-- Discount Badge -->
                                    <div class="absolute top-4 right-4">
                                        <div class="bg-salon-gold text-black px-3 py-1 rounded-full text-lg font-bold">
                                            <?= $offer['discount'] ?>% OFF
                                        </div>
                                    </div>
                                </div>

                                <!-- Offer Content -->
                                <div class="p-6">
                                    <h3 class="text-2xl font-bold text-white mb-2 group-hover:text-salon-gold transition-colors duration-300">
                                        <?= htmlspecialchars($offer['title']) ?>
                                    </h3>
                                    
                                    <p class="text-gray-300 mb-4 leading-relaxed">
                                        <?= htmlspecialchars($offer['description']) ?>
                                    </p>
                                    
                                    <!-- Offer Details -->
                                    <div class="space-y-3 mb-4">
                                        <div>
                                            <h4 class="text-sm font-semibold text-salon-gold mb-1">Offer Details:</h4>
                                            <p class="text-sm text-gray-300">Valid for all services. Cannot be combined with other offers.</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Validity Period -->
                                    <div class="flex items-center justify-between mb-4 text-sm">
                                        <div class="flex items-center text-gray-400">
                                            <i class="fas fa-calendar mr-2"></i>
                                            Valid until <?= date('M j, Y', strtotime($offer['valid_to'])) ?>
                                        </div>
                                        <?php
                                        $daysLeft = ceil((strtotime($offer['valid_to']) - time()) / (60 * 60 * 24));
                                        if ($daysLeft <= 7): ?>
                                            <div class="text-red-400 font-semibold">
                                                <i class="fas fa-clock mr-1"></i>
                                                <?= $daysLeft ?> days left!
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Savings Display -->
                                    <div class="bg-gradient-to-r from-salon-gold/10 to-salon-gold/5 border border-salon-gold/20 rounded-lg p-4 mb-4 transform transition-all duration-300 group-hover:scale-105">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-salon-gold mb-1 animate-pulse-subtle">
                                                Save <?= $offer['discount'] ?>%
                                            </div>
                                            <div class="text-sm text-gray-300">
                                                Use code: <span class="font-mono font-bold text-salon-gold bg-salon-gold/10 px-2 py-1 rounded"><?= $offer['code'] ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Features -->
                                    <div class="space-y-2 mb-6">
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2"></i>
                                            Valid for new and existing customers
                                        </div>
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2"></i>
                                            Can be combined with loyalty points
                                        </div>
                                        <div class="flex items-center text-sm text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2"></i>
                                            No hidden fees or charges
                                        </div>
                                    </div>
                                    
                                    <div class="flex gap-3">
                                        <a href="<?= getBasePath() ?>/customer/book" 
                                           class="flex-1 bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black py-3 px-4 rounded-lg font-semibold transition-all duration-300 hover:scale-105 text-center transform hover:shadow-lg">
                                            Book Now
                                        </a>
                                        <button onclick="copyOfferCode('<?= $offer['code'] ?>')" 
                                                class="relative px-4 py-3 border border-salon-gold text-salon-gold rounded-lg hover:bg-salon-gold hover:text-black transition-all duration-300 hover:scale-105 group overflow-hidden">
                                            <i class="fas fa-copy"></i>
                                            <span class="copy-tooltip">Copy Code</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="py-20 bg-salon-black relative overflow-hidden">
        <div class="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(247,197,66,0.1),transparent)]"></div>
        <div class="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(247,197,66,0.05),transparent)]"></div>
        
        <div class="max-w-4xl mx-auto px-6 text-center relative">
            <div class="bg-gradient-to-r from-salon-gold/10 via-salon-gold/5 to-transparent border border-salon-gold/20 rounded-3xl p-12 backdrop-blur-sm
                        transform transition-all duration-500 hover:border-salon-gold/30 hover:shadow-2xl hover:shadow-salon-gold/10">
                <div class="animate-float">
                    <i class="fas fa-bell text-salon-gold text-4xl mb-6"></i>
                </div>
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6 animate-fade-in-up">
                    Never Miss a <span class="text-transparent bg-clip-text bg-gradient-to-r from-salon-gold to-yellow-500 animate-shimmer">Deal</span>
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto animate-fade-in-up" style="animation-delay: 200ms;">
                    Subscribe to our newsletter and be the first to know about exclusive offers, new services, and special promotions.
                </p>
                <form class="max-w-md mx-auto mb-6 group" id="newsletterForm">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1 relative">
                            <input 
                                type="email" 
                                placeholder="Enter your email address"
                                class="w-full px-6 py-4 bg-secondary-900/80 border border-secondary-700 rounded-xl text-white placeholder-gray-400 
                                       focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent transition-all duration-300
                                       hover:border-salon-gold/50"
                                required
                            >
                            <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/10 to-transparent opacity-0 transition-opacity duration-300 rounded-xl pointer-events-none
                                      group-hover:opacity-100"></div>
                        </div>
                        <button 
                            type="submit"
                            class="px-8 py-4 bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black 
                                   font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg whitespace-nowrap
                                   focus:outline-none focus:ring-2 focus:ring-salon-gold focus:ring-offset-2 focus:ring-offset-salon-black"
                        >
                            Subscribe
                        </button>
                    </div>
                </form>
                <p class="text-gray-400 text-sm animate-fade-in-up" style="animation-delay: 400ms;">
                    <i class="fas fa-shield-alt text-salon-gold mr-2"></i>
                    Join 5,000+ subscribers and get exclusive deals delivered to your inbox.
                </p>
            </div>
        </div>
    </section>

    <style>
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0px); }
    }
    </style>

    <!-- How to Use Offers -->
    <section class="py-20 bg-secondary-900">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="inline-block bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                    How It Works
                </div>
                <h2 class="text-4xl md:text-5xl font-bold font-serif text-white mb-6">
                    How to Use Your <span class="text-salon-gold">Offers</span>
                </h2>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Choose Your Offer</h3>
                    <p class="text-gray-300 text-sm">Browse our current offers and select the one that suits your needs</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Book Your Service</h3>
                    <p class="text-gray-300 text-sm">Schedule your appointment online or call us directly</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Apply Offer Code</h3>
                    <p class="text-gray-300 text-sm">Enter the offer code during booking or mention it when you arrive</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <span class="text-salon-gold font-bold text-xl">4</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">Enjoy & Save</h3>
                    <p class="text-gray-300 text-sm">Relax and enjoy your service while saving money</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Toast Container -->
    <div id="toastContainer" class="fixed bottom-4 right-4 z-50"></div>

    <script>
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `flex items-center p-4 mb-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full
                              ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
            
            toast.innerHTML = `
                <div class="flex items-center text-white">
                    <i class="${type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'} mr-2"></i>
                    <span class="font-medium">${message}</span>
                </div>
            `;
            
            const container = document.getElementById('toastContainer');
            container.appendChild(toast);
            
            // Trigger animation
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
                toast.classList.add('translate-x-0');
            }, 10);
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }

        function copyOfferCode(code) {
            navigator.clipboard.writeText(code).then(function() {
                // Show success message with toast
                showToast(`Offer code "${code}" copied to clipboard!`);
                
                // Visual feedback on button
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.add('bg-green-500', 'text-white', 'border-green-500');

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.classList.remove('bg-green-500', 'text-white', 'border-green-500');
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                showToast('Failed to copy code. Please try again.', 'error');
            });
        }

        // Counter Animation for Hero Section
        function animateCounters() {
            const counters = document.querySelectorAll('[data-counter]');
            
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-counter'));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target.toLocaleString();
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString();
                    }
                }, 16);
            });
        }

        // Initialize counter animation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounters();
                        observer.disconnect();
                    }
                });
            });
            
            const statsSection = document.querySelector('[data-counter]');
            if (statsSection) {
                observer.observe(statsSection.closest('section'));
            }
        });
    </script>

<?php include __DIR__ . '/includes/footer.php'; ?>
