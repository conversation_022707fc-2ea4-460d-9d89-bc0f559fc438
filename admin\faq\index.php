<?php
/**
 * FAQ Management - Admin Panel
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Require admin authentication
$auth->requireRole('ADMIN');

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'create':
                $stmt = $database->query(
                    "INSERT INTO faqs (category, question, answer, display_order, is_active) VALUES (?, ?, ?, ?, ?)",
                    [
                        $_POST['category'],
                        $_POST['question'],
                        $_POST['answer'],
                        (int)$_POST['display_order'],
                        isset($_POST['is_active']) ? 1 : 0
                    ]
                );
                echo json_encode(['success' => true, 'message' => 'FAQ created successfully']);
                exit;
                
            case 'update':
                $stmt = $database->query(
                    "UPDATE faqs SET category = ?, question = ?, answer = ?, display_order = ?, is_active = ? WHERE id = ?",
                    [
                        $_POST['category'],
                        $_POST['question'],
                        $_POST['answer'],
                        (int)$_POST['display_order'],
                        isset($_POST['is_active']) ? 1 : 0,
                        (int)$_POST['id']
                    ]
                );
                echo json_encode(['success' => true, 'message' => 'FAQ updated successfully']);
                exit;
                
            case 'delete':
                $stmt = $database->query("DELETE FROM faqs WHERE id = ?", [(int)$_POST['id']]);
                echo json_encode(['success' => true, 'message' => 'FAQ deleted successfully']);
                exit;
                
            case 'get':
                $faq = $database->fetch("SELECT * FROM faqs WHERE id = ?", [(int)$_POST['id']]);
                echo json_encode(['success' => true, 'data' => $faq]);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// Pagination settings
$itemsPerPage = 6;
$currentPage = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$selectedCategory = isset($_GET['category']) ? $_GET['category'] : '';

// Get all categories for navigation
$categories = $database->fetchAll("SELECT DISTINCT category FROM faqs ORDER BY category");

// Get FAQs based on selected category or all FAQs
if ($selectedCategory) {
    // Get FAQs for specific category with pagination
    $offset = ($currentPage - 1) * $itemsPerPage;
    $faqs = $database->fetchAll(
        "SELECT * FROM faqs WHERE category = ? ORDER BY display_order, id LIMIT ? OFFSET ?", 
        [$selectedCategory, $itemsPerPage, $offset]
    );
    
    // Get total count for pagination
    $totalItems = $database->fetch("SELECT COUNT(*) as count FROM faqs WHERE category = ?", [$selectedCategory])['count'];
} else {
    // Get all FAQs with pagination
    $offset = ($currentPage - 1) * $itemsPerPage;
    $faqs = $database->fetchAll(
        "SELECT * FROM faqs ORDER BY category, display_order, id LIMIT ? OFFSET ?", 
        [$itemsPerPage, $offset]
    );
    
    // Get total count for pagination
    $totalItems = $database->fetch("SELECT COUNT(*) as count FROM faqs")['count'];
}

// Calculate pagination info
$totalPages = ceil($totalItems / $itemsPerPage);

// Group FAQs by category for display
$faqsByCategory = [];
foreach ($faqs as $faq) {
    $faqsByCategory[$faq['category']][] = $faq;
}

// Get all FAQs for statistics (not paginated)
$allFaqs = $database->fetchAll("SELECT * FROM faqs");
$allFaqsByCategory = [];
foreach ($allFaqs as $faq) {
    $allFaqsByCategory[$faq['category']][] = $faq;
}

$pageTitle = "FAQ Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Page Header -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 hover-lift">
                        <div class="px-6 sm:px-8 lg:px-10">
                            <div class="py-8 md:flex md:items-center md:justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mr-4">
                                            <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h1 class="text-3xl font-bold leading-7 text-white sm:leading-9 sm:truncate font-serif">
                                                FAQ <span class="text-salon-gold">Management</span>
                                            </h1>
                                            <p class="mt-2 text-gray-300">Manage frequently asked questions and answers</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-6 flex space-x-3 md:mt-0 md:ml-4">
                                    <button onclick="openCreateModal()" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-black bg-salon-gold hover:bg-yellow-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold transition-all hover:scale-105">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add New FAQ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Statistics -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-4 mb-8">
                        <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                            <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-400 truncate">Total FAQs</dt>
                                            <dd class="text-2xl font-bold text-salon-gold"><?= count($allFaqs) ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                            <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-400 truncate">Categories</dt>
                                            <dd class="text-2xl font-bold text-white"><?= count($allFaqsByCategory) ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                            <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-400 truncate">Active</dt>
                                            <dd class="text-2xl font-bold text-green-400"><?= count(array_filter($allFaqs, fn($f) => $f['is_active'])) ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                            <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-400 truncate">Inactive</dt>
                                            <dd class="text-2xl font-bold text-red-400"><?= count(array_filter($allFaqs, fn($f) => !$f['is_active'])) ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Filter Navigation -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl overflow-hidden rounded-2xl hover-lift mb-6">
                        <div class="px-6 py-4 sm:px-8">
                            <h3 class="text-lg leading-6 font-bold text-white mb-4">Filter by Category</h3>
                            <div class="flex flex-wrap gap-3">
                                <a href="?<?= http_build_query(array_merge($_GET, ['category' => '', 'page' => 1])) ?>" 
                                   class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors <?= empty($selectedCategory) ? 'bg-salon-gold text-black' : 'bg-secondary-800 text-gray-300 hover:bg-secondary-700' ?>">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                    </svg>
                                    All Categories
                                    <span class="ml-2 bg-white/20 text-xs px-2 py-1 rounded-full"><?= count($allFaqs) ?></span>
                                </a>
                                
                                <?php foreach ($categories as $category): ?>
                                    <?php $categoryCount = count(array_filter($allFaqs, fn($f) => $f['category'] === $category['category'])); ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['category' => $category['category'], 'page' => 1])) ?>" 
                                       class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors capitalize <?= $selectedCategory === $category['category'] ? 'bg-salon-gold text-black' : 'bg-secondary-800 text-gray-300 hover:bg-secondary-700' ?>">
                                        <?= htmlspecialchars($category['category']) ?>
                                        <span class="ml-2 bg-white/20 text-xs px-2 py-1 rounded-full"><?= $categoryCount ?></span>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ List -->
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl overflow-hidden rounded-2xl hover-lift">
                        <div class="px-6 py-6 sm:px-8">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl leading-6 font-bold text-white">
                                        <?php if ($selectedCategory): ?>
                                            <?= ucfirst(htmlspecialchars($selectedCategory)) ?> FAQs
                                        <?php else: ?>
                                            All FAQs
                                        <?php endif; ?>
                                    </h3>
                                    <p class="mt-2 max-w-2xl text-sm text-gray-300">
                                        <?php if ($selectedCategory): ?>
                                            Showing <?= count($faqs) ?> of <?= $totalItems ?> FAQs in <?= htmlspecialchars($selectedCategory) ?> category
                                        <?php else: ?>
                                            Showing <?= count($faqs) ?> of <?= $totalItems ?> FAQs (Page <?= $currentPage ?> of <?= $totalPages ?>)
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <?php if ($totalPages > 1): ?>
                                    <div class="text-sm text-gray-400">
                                        Page <?= $currentPage ?> of <?= $totalPages ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if (empty($faqs)): ?>
                            <div class="text-center py-16">
                                <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-white mb-4">
                                    <?php if ($selectedCategory): ?>
                                        No FAQs Found in <?= ucfirst(htmlspecialchars($selectedCategory)) ?> Category
                                    <?php else: ?>
                                        No FAQs Found
                                    <?php endif; ?>
                                </h3>
                                <p class="text-gray-300 mb-6">
                                    <?php if ($selectedCategory): ?>
                                        There are no FAQs in the <?= htmlspecialchars($selectedCategory) ?> category yet.
                                    <?php else: ?>
                                        Get started by creating your first FAQ
                                    <?php endif; ?>
                                </p>
                                <button onclick="openCreateModal()" class="bg-salon-gold text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-colors">
                                    Create First FAQ
                                </button>
                            </div>
                        <?php else: ?>
                            <?php foreach ($faqsByCategory as $category => $categoryFaqs): ?>
                                <div class="border-t border-secondary-700">
                                    <div class="bg-secondary-800/50 px-6 py-4 sm:px-8">
                                        <h4 class="text-lg font-semibold text-salon-gold capitalize"><?= htmlspecialchars($category) ?> Questions</h4>
                                    </div>
                                    <ul class="divide-y divide-secondary-700">
                                        <?php foreach ($categoryFaqs as $faq): ?>
                                            <li class="hover:bg-secondary-800/50 transition-colors duration-200">
                                                <div class="px-6 py-5 sm:px-8">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex-1 min-w-0">
                                                            <div class="flex items-center">
                                                                <div class="flex-shrink-0">
                                                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold <?= $faq['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                                        <?= $faq['is_active'] ? 'Active' : 'Inactive' ?>
                                                                    </span>
                                                                </div>
                                                                <div class="ml-4 flex-1">
                                                                    <div class="text-sm font-semibold text-white">
                                                                        <?= htmlspecialchars($faq['question']) ?>
                                                                    </div>
                                                                    <div class="text-sm text-gray-300 mt-1 line-clamp-2">
                                                                        <?= htmlspecialchars(substr($faq['answer'], 0, 150)) ?>...
                                                                    </div>
                                                                    <div class="text-xs text-gray-400 mt-2">
                                                                        Order: <?= $faq['display_order'] ?> | 
                                                                        Updated: <?= date('M j, Y', strtotime($faq['updated_at'])) ?>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="flex items-center space-x-2 ml-4">
                                                            <button onclick="editFAQ(<?= $faq['id'] ?>)" class="text-salon-gold hover:text-yellow-400 transition-colors">
                                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                                </svg>
                                                            </button>
                                                            <button onclick="deleteFAQ(<?= $faq['id'] ?>, '<?= htmlspecialchars($faq['question'], ENT_QUOTES) ?>')" class="text-red-400 hover:text-red-300 transition-colors">
                                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                        <!-- Pagination Controls -->
                        <?php if ($totalPages > 1): ?>
                            <div class="px-6 py-4 border-t border-secondary-700 sm:px-8">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1 flex justify-between sm:hidden">
                                        <?php if ($currentPage > 1): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage - 1])) ?>" 
                                               class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-800 hover:bg-secondary-700">
                                                Previous
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ($currentPage < $totalPages): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage + 1])) ?>" 
                                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-800 hover:bg-secondary-700">
                                                Next
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                        <div>
                                            <p class="text-sm text-gray-400">
                                                Showing <span class="font-medium"><?= ($currentPage - 1) * $itemsPerPage + 1 ?></span> to 
                                                <span class="font-medium"><?= min($currentPage * $itemsPerPage, $totalItems) ?></span> of 
                                                <span class="font-medium"><?= $totalItems ?></span> results
                                            </p>
                                        </div>
                                        <div>
                                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                <?php if ($currentPage > 1): ?>
                                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage - 1])) ?>" 
                                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700">
                                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                        </svg>
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <?php
                                                $startPage = max(1, $currentPage - 2);
                                                $endPage = min($totalPages, $currentPage + 2);
                                                
                                                if ($startPage > 1): ?>
                                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>" 
                                                       class="relative inline-flex items-center px-4 py-2 border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700">1</a>
                                                    <?php if ($startPage > 2): ?>
                                                        <span class="relative inline-flex items-center px-4 py-2 border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-500">...</span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                
                                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                                    <?php if ($i == $currentPage): ?>
                                                        <span class="relative inline-flex items-center px-4 py-2 border border-salon-gold bg-salon-gold text-sm font-medium text-black">
                                                            <?= $i ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>" 
                                                           class="relative inline-flex items-center px-4 py-2 border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700">
                                                            <?= $i ?>
                                                        </a>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                                
                                                <?php if ($endPage < $totalPages): ?>
                                                    <?php if ($endPage < $totalPages - 1): ?>
                                                        <span class="relative inline-flex items-center px-4 py-2 border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-500">...</span>
                                                    <?php endif; ?>
                                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>" 
                                                       class="relative inline-flex items-center px-4 py-2 border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700"><?= $totalPages ?></a>
                                                <?php endif; ?>
                                                
                                                <?php if ($currentPage < $totalPages): ?>
                                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $currentPage + 1])) ?>" 
                                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-600 bg-secondary-800 text-sm font-medium text-gray-300 hover:bg-secondary-700">
                                                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                                        </svg>
                                                    </a>
                                                <?php endif; ?>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit FAQ Modal -->
<div id="faqModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-secondary-900 rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-secondary-700">
            <h3 id="modalTitle" class="text-xl font-bold text-white">Add New FAQ</h3>
        </div>
        
        <form id="faqForm" class="p-6 space-y-6">
            <input type="hidden" id="faqId" name="id">
            <input type="hidden" id="formAction" name="action" value="create">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select id="category" name="category" required class="w-full px-4 py-3 bg-secondary-800 border border-secondary-600 rounded-lg text-white focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                        <option value="">Select Category</option>
                        <option value="general">General</option>
                        <option value="booking">Booking</option>
                        <option value="payment">Payment</option>
                        <option value="safety">Safety</option>
                    </select>
                </div>
                
                <div>
                    <label for="display_order" class="block text-sm font-medium text-gray-300 mb-2">Display Order</label>
                    <input type="number" id="display_order" name="display_order" min="0" value="0" class="w-full px-4 py-3 bg-secondary-800 border border-secondary-600 rounded-lg text-white focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                </div>
            </div>
            
            <div>
                <label for="question" class="block text-sm font-medium text-gray-300 mb-2">Question</label>
                <input type="text" id="question" name="question" required class="w-full px-4 py-3 bg-secondary-800 border border-secondary-600 rounded-lg text-white focus:ring-2 focus:ring-salon-gold focus:border-transparent" placeholder="Enter the question">
            </div>
            
            <div>
                <label for="answer" class="block text-sm font-medium text-gray-300 mb-2">Answer</label>
                <textarea id="answer" name="answer" rows="6" required class="w-full px-4 py-3 bg-secondary-800 border border-secondary-600 rounded-lg text-white focus:ring-2 focus:ring-salon-gold focus:border-transparent resize-none" placeholder="Enter the detailed answer"></textarea>
            </div>
            
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" checked class="h-4 w-4 text-salon-gold focus:ring-salon-gold border-secondary-600 rounded bg-secondary-800">
                <label for="is_active" class="ml-2 block text-sm text-gray-300">Active (visible on website)</label>
            </div>
        </form>
        
        <div class="px-6 py-4 border-t border-secondary-700 flex justify-end space-x-3">
            <button onclick="closeFAQModal()" class="px-6 py-3 border border-secondary-600 rounded-lg text-gray-300 hover:bg-secondary-800 transition-colors">
                Cancel
            </button>
            <button onclick="saveFAQ()" class="px-6 py-3 bg-salon-gold text-black rounded-lg font-semibold hover:bg-yellow-500 transition-colors">
                <span id="saveButtonText">Save FAQ</span>
            </button>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

<script>
// Modal functions
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add New FAQ';
    document.getElementById('formAction').value = 'create';
    document.getElementById('saveButtonText').textContent = 'Save FAQ';
    document.getElementById('faqForm').reset();
    document.getElementById('is_active').checked = true;
    document.getElementById('faqModal').classList.remove('hidden');
    document.getElementById('faqModal').classList.add('flex');
}

function closeFAQModal() {
    document.getElementById('faqModal').classList.add('hidden');
    document.getElementById('faqModal').classList.remove('flex');
}

function editFAQ(id) {
    // Fetch FAQ data
    fetch('', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get&id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const faq = data.data;
            document.getElementById('modalTitle').textContent = 'Edit FAQ';
            document.getElementById('formAction').value = 'update';
            document.getElementById('saveButtonText').textContent = 'Update FAQ';
            document.getElementById('faqId').value = faq.id;
            document.getElementById('category').value = faq.category;
            document.getElementById('question').value = faq.question;
            document.getElementById('answer').value = faq.answer;
            document.getElementById('display_order').value = faq.display_order;
            document.getElementById('is_active').checked = faq.is_active == 1;
            
            document.getElementById('faqModal').classList.remove('hidden');
            document.getElementById('faqModal').classList.add('flex');
        } else {
            showMessage('Error loading FAQ data', 'error');
        }
    })
    .catch(error => {
        showMessage('Error loading FAQ data', 'error');
    });
}

function saveFAQ() {
    const form = document.getElementById('faqForm');
    const formData = new FormData(form);
    
    // Convert FormData to URLSearchParams
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        params.append(key, value);
    }
    
    fetch('', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            closeFAQModal();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showMessage(data.message, 'error');
        }
    })
    .catch(error => {
        showMessage('Error saving FAQ', 'error');
    });
}

function deleteFAQ(id, question) {
    if (confirm(`Are you sure you want to delete this FAQ?\n\n"${question}"`)) {
        fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=delete&id=${id}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('Error deleting FAQ', 'error');
        });
    }
}

function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const messageDiv = document.createElement('div');
    
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    
    messageDiv.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
    messageDiv.textContent = message;
    
    container.appendChild(messageDiv);
    
    // Animate in
    setTimeout(() => {
        messageDiv.classList.remove('translate-x-full');
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        messageDiv.classList.add('translate-x-full');
        setTimeout(() => {
            container.removeChild(messageDiv);
        }, 300);
    }, 5000);
}

// Close modal when clicking outside
document.getElementById('faqModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeFAQModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeFAQModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>