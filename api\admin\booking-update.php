<?php
/**
 * Booking Update API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/booking_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];
$bookingId = $_GET['id'] ?? null;

if (!$bookingId) {
    http_response_code(400);
    echo json_encode(['error' => 'Booking ID is required']);
    exit;
}

try {
    if ($method === 'POST') {
        handleUpdateBooking($bookingId);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleUpdateBooking($bookingId) {
    global $database;
    
    try {
        // Get input data
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data']);
            return;
        }
        
        // Validate required fields
        $requiredFields = ['staff_id', 'date', 'start_time', 'end_time', 'status', 'total_amount'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                http_response_code(400);
                echo json_encode(['error' => "Missing required field: $field"]);
                return;
            }
        }

        // Ensure either service_id or package_id is provided
        if (empty($input['service_id']) && empty($input['package_id'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Either service_id or package_id is required']);
            return;
        }
        
        // Get current booking
        $currentBooking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$currentBooking) {
            http_response_code(404);
            echo json_encode(['error' => 'Booking not found']);
            return;
        }
        
        // Validate status
        $validStatuses = ['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'EXPIRED'];
        if (!in_array($input['status'], $validStatuses)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid status']);
            return;
        }
        
        // Validate service exists (only if service_id is provided)
        if (!empty($input['service_id'])) {
            $service = $database->fetch("SELECT * FROM services WHERE id = ? AND is_active = 1", [$input['service_id']]);
            if (!$service) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid service']);
                return;
            }
        }

        // Validate package exists (only if package_id is provided)
        if (!empty($input['package_id'])) {
            $package = $database->fetch("SELECT * FROM packages WHERE id = ? AND is_active = 1", [$input['package_id']]);
            if (!$package) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid package']);
                return;
            }
        }
        
        // Validate staff exists
        $staff = $database->fetch("SELECT * FROM users WHERE id = ? AND role = 'STAFF' AND is_active = 1", [$input['staff_id']]);
        if (!$staff) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid staff member']);
            return;
        }
        
        // Validate date is not in the past (unless it's already in the past)
        $bookingDate = new DateTime($input['date']);
        $today = new DateTime();
        $currentBookingDate = new DateTime($currentBooking['date']);
        
        if ($bookingDate < $today && $currentBookingDate >= $today) {
            http_response_code(400);
            echo json_encode(['error' => 'Cannot reschedule to a past date']);
            return;
        }
        
        // Check for time slot conflicts (if date/time/staff changed)
        $timeChanged = ($input['date'] !== $currentBooking['date'] || 
                       $input['start_time'] !== $currentBooking['start_time'] || 
                       $input['staff_id'] != $currentBooking['staff_id']);
        
        if ($timeChanged) {
            $conflictCheck = $database->fetch("
                SELECT id FROM bookings 
                WHERE id != ? 
                AND staff_id = ? 
                AND date = ? 
                AND status NOT IN ('CANCELLED', 'NO_SHOW')
                AND (
                    (start_time <= ? AND end_time > ?) OR
                    (start_time < ? AND end_time >= ?) OR
                    (start_time >= ? AND end_time <= ?)
                )
            ", [
                $bookingId,
                $input['staff_id'],
                $input['date'],
                $input['start_time'], $input['start_time'],
                $input['end_time'], $input['end_time'],
                $input['start_time'], $input['end_time']
            ]);
            
            if ($conflictCheck) {
                http_response_code(400);
                echo json_encode(['error' => 'Time slot conflict with another booking']);
                return;
            }
        }
        
        // Calculate points if status changed to COMPLETED
        $pointsEarned = $currentBooking['points_earned'];
        if ($input['status'] === 'COMPLETED' && $currentBooking['status'] !== 'COMPLETED') {
            $pointsEarned = floor($input['total_amount'] / 1000); // 1 point per TSH 1,000 spent
        } elseif ($input['status'] !== 'COMPLETED' && $currentBooking['status'] === 'COMPLETED') {
            $pointsEarned = 0; // Remove points if no longer completed
        }
        
        // Update booking
        $database->query("
            UPDATE bookings SET
                service_id = ?,
                package_id = ?,
                staff_id = ?,
                date = ?,
                start_time = ?,
                end_time = ?,
                status = ?,
                total_amount = ?,
                points_earned = ?,
                notes = ?,
                updated_at = NOW()
            WHERE id = ?
        ", [
            $input['service_id'] ?: null,
            $input['package_id'] ?: null,
            $input['staff_id'],
            $input['date'],
            $input['start_time'],
            $input['end_time'],
            $input['status'],
            $input['total_amount'],
            $pointsEarned,
            $input['notes'] ?? null,
            $bookingId
        ]);
        
        // Update user points if points earned changed
        if ($pointsEarned !== $currentBooking['points_earned']) {
            $pointsDifference = $pointsEarned - $currentBooking['points_earned'];
            $database->query(
                "UPDATE users SET points = points + ? WHERE id = ?",
                [$pointsDifference, $currentBooking['user_id']]
            );
        }
        
        // Get updated booking details
        $updatedBooking = $database->fetch("
            SELECT
                b.*,
                u.name as customer_name,
                s.name as service_name,
                p.name as package_name,
                st.name as staff_name
            FROM bookings b
            LEFT JOIN users u ON b.user_id = u.id
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            WHERE b.id = ?
        ", [$bookingId]);

        // Create staff notifications for admin-triggered changes
        require_once __DIR__ . '/../../includes/notification_triggers.php';

        $adminId = $_SESSION['user_id'];
        $changes = [];

        // Track what changed for detailed notifications
        if ($currentBooking['staff_id'] !== $input['staff_id']) {
            $oldStaffName = $currentBooking['staff_id'] ?
                $database->fetch("SELECT name FROM users WHERE id = ?", [$currentBooking['staff_id']])['name'] ?? 'Unassigned' : 'Unassigned';
            $newStaffName = $updatedBooking['staff_name'] ?? 'Unassigned';
            $changes['staff'] = ['old' => $oldStaffName, 'new' => $newStaffName];

            // Notify old staff member if they were unassigned
            if ($currentBooking['staff_id'] && $currentBooking['staff_id'] !== $input['staff_id']) {
                createAdminBookingNotificationForStaff($bookingId, 'ADMIN_STAFF_UNASSIGNED', $adminId, [
                    'changes' => $changes,
                    'old_staff_id' => $currentBooking['staff_id']
                ]);
            }

            // Notify new staff member if they were assigned
            if ($input['staff_id']) {
                createAdminBookingNotificationForStaff($bookingId, 'ADMIN_STAFF_ASSIGNED', $adminId, [
                    'changes' => $changes
                ]);
            }
        }

        if ($currentBooking['date'] !== $input['date']) {
            $changes['date'] = ['old' => $currentBooking['date'], 'new' => $input['date']];
        }

        if ($currentBooking['start_time'] !== $input['start_time']) {
            $changes['start_time'] = ['old' => $currentBooking['start_time'], 'new' => $input['start_time']];
        }

        if ($currentBooking['status'] !== $input['status']) {
            $changes['status'] = ['old' => $currentBooking['status'], 'new' => $input['status']];
        }

        // Check if service/package changed
        $oldServiceName = $currentBooking['service_id'] ?
            $database->fetch("SELECT name FROM services WHERE id = ?", [$currentBooking['service_id']])['name'] ?? '' :
            ($currentBooking['package_id'] ? $database->fetch("SELECT name FROM packages WHERE id = ?", [$currentBooking['package_id']])['name'] ?? '' : '');
        $newServiceName = $updatedBooking['service_name'] ?: $updatedBooking['package_name'];

        if ($oldServiceName !== $newServiceName) {
            $changes['service'] = ['old' => $oldServiceName, 'new' => $newServiceName];
        }

        // Send appropriate notification based on changes
        if (!empty($changes) && $updatedBooking['staff_id']) {
            if (isset($changes['date']) || isset($changes['start_time'])) {
                createAdminBookingNotificationForStaff($bookingId, 'ADMIN_BOOKING_RESCHEDULED', $adminId, [
                    'changes' => $changes
                ]);
            } elseif (isset($changes['status'])) {
                createAdminBookingNotificationForStaff($bookingId, 'ADMIN_STATUS_CHANGED', $adminId, [
                    'changes' => $changes
                ]);
            } elseif (!isset($changes['staff'])) { // Don't send update notification if staff assignment changed (already handled above)
                createAdminBookingNotificationForStaff($bookingId, 'ADMIN_BOOKING_UPDATED', $adminId, [
                    'changes' => $changes
                ]);
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Booking updated successfully',
            'booking' => $updatedBooking
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update booking: ' . $e->getMessage()]);
    }
}
?>
