<?php
/**
 * Newsletter Subscription API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/functions.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Fallback to form data if JSO<PERSON> is not provided
    if (!$input) {
        $input = $_POST;
    }
    
    $email = sanitize($input['email'] ?? '');
    
    // Validate email
    if (empty($email)) {
        echo json_encode([
            'success' => false,
            'error' => 'Email is required'
        ]);
        exit();
    }
    
    if (!isValidEmail($email)) {
        echo json_encode([
            'success' => false,
            'error' => 'Please enter a valid email address'
        ]);
        exit();
    }
    
    // Check if email already exists
    $existing = $database->fetch(
        "SELECT id FROM newsletter WHERE email = ?",
        [$email]
    );
    
    if ($existing) {
        echo json_encode([
            'success' => false,
            'error' => 'This email is already subscribed to our newsletter'
        ]);
        exit();
    }
    
    // Insert new subscription with UUID
    $id = generateUUID();
    $database->query(
        "INSERT INTO newsletter (id, email) VALUES (?, ?)",
        [$id, $email]
    );
    
    // Send welcome email (optional)
    $subject = "Welcome to Flix Salonce Newsletter!";
    $body = "
        <h2>Welcome to Flix Salonce!</h2>
        <p>Thank you for subscribing to our newsletter. You'll be the first to know about:</p>
        <ul>
            <li>Exclusive offers and discounts</li>
            <li>New services and treatments</li>
            <li>Beauty tips and trends</li>
            <li>Special events and promotions</li>
        </ul>
        <p>We're excited to have you as part of our community!</p>
        <p>Best regards,<br>The Flix Salonce Team</p>
    ";
    
    // Uncomment to send welcome email
    // sendEmail($email, $subject, $body, true);
    
    echo json_encode([
        'success' => true,
        'message' => 'Thank you for subscribing to our newsletter!'
    ]);
    
} catch (Exception $e) {
    // Log the error for debugging
    error_log("Newsletter subscription error: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to subscribe. Please try again later.',
        'debug' => (defined('DEBUG') && DEBUG) ? $e->getMessage() : null
    ]);
}
?>
