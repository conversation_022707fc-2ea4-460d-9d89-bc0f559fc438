# PHP
*.log
*.tmp
*.cache
vendor/
composer.phar

# Environment and Configuration
.env
.env.local
.env.production

# Uploads and User Generated Content
uploads/*
!uploads/.gitkeep

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp

# Database files (exclude sensitive data, but allow schema/migration files)
*.db
*.sqlite
*.sqlite3

# Exclude database dumps and backups
*_backup.sql
*_dump.sql
backup_*.sql
dump_*.sql

# Allow essential SQL files for project setup
!database/*.sql
!database/migrations/*.sql
!flix_salonce*.sql

# Logs
logs/
*.log
error.log
access.log
test_booking_errors.log

# Cache
cache/
*.cache

# Session files
sessions/

# Backup files
*.bak
*.backup
*.old

# XAMPP specific
.htaccess.bak

# Node modules (if any)
node_modules/

# Package managers
composer.lock
package-lock.json
yarn.lock

# Build files
dist/
build/

# Test files
test_*.php
*_test.php
phpunit.xml
.phpunit.result.cache

# Documentation build
docs/_build/

# Local development
local/
dev/

# Sensitive configuration files (create templates instead)
config/database.php
config/app.php
