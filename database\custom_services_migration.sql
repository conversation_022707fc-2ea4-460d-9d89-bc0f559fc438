-- Custom Services Migration for Package Management
-- This adds support for custom services that can be manually created by admins

USE flix_salonce2;

-- Create custom_services table
CREATE TABLE IF NOT EXISTS custom_services (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL COMMENT 'Duration in minutes',
    category VARCHAR(100) DEFAULT 'Custom',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_custom_services_active (is_active),
    INDEX idx_custom_services_category (category),
    INDEX idx_custom_services_name (name)
);

-- Create package_custom_services relationship table
CREATE TABLE IF NOT EXISTS package_custom_services (
    id VARCHAR(36) PRIMARY KEY,
    package_id VARCHAR(36) NOT NULL,
    custom_service_id VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE,
    FOREIGN KEY (custom_service_id) REFERENCES custom_services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_package_custom_service (package_id, custom_service_id),
    INDEX idx_package_custom_services_package (package_id),
    INDEX idx_package_custom_services_service (custom_service_id)
);

-- Add package_type column to packages table to distinguish between regular and custom packages
ALTER TABLE packages ADD COLUMN IF NOT EXISTS package_type ENUM('REGULAR', 'CUSTOM', 'MIXED') DEFAULT 'REGULAR';

-- Add index for package_type
CREATE INDEX IF NOT EXISTS idx_packages_type ON packages(package_type);

-- Insert some sample custom services
INSERT IGNORE INTO custom_services (id, name, description, price, duration, category, is_active) VALUES
('cs-001', 'VIP Consultation', 'Personalized beauty consultation with senior stylist', 15000, 60, 'Consultation', 1),
('cs-002', 'Express Touch-up', 'Quick styling touch-up service', 8000, 30, 'Styling', 1),
('cs-003', 'Premium Hair Treatment', 'Luxury hair treatment with imported products', 25000, 90, 'Hair Care', 1),
('cs-004', 'Bridal Makeup Trial', 'Complete bridal makeup trial session', 20000, 120, 'Bridal', 1),
('cs-005', 'Skin Analysis', 'Professional skin analysis and recommendation', 12000, 45, 'Skincare', 1);