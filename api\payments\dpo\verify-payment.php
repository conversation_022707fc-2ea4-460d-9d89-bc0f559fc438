<?php
/**
 * DPO Pay Verify Payment API
 * Verifies payment status with DPO Pay
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../../../config/app.php';

// Check if DPO is enabled
if (!DPO_ENABLED) {
    http_response_code(403);
    echo json_encode(['error' => 'DPO Pay is disabled']);
    exit;
}

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $paymentId = $input['payment_id'] ?? '';
    $transactionToken = $input['transaction_token'] ?? '';
    
    error_log("DPO verify-payment called with: paymentId={$paymentId}, transactionToken={$transactionToken}");

    if (empty($paymentId)) {
        throw new Exception('Payment ID is required');
    }

    if (empty($transactionToken)) {
        throw new Exception('Transaction token is required');
    }
    
    global $database;
    
    // Verify payment belongs to user
    $payment = $database->fetch("
        SELECT p.*, b.user_id, b.id as booking_id
        FROM payments p
        INNER JOIN bookings b ON p.booking_id = b.id
        WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'DPO'
    ", [$paymentId, $_SESSION['user_id']]);

    if (!$payment) {
        throw new Exception('Payment not found or invalid');
    }

    // Verify the transaction token with DPO
    $verificationResult = verifyDpoToken($transactionToken);

    if ($verificationResult['success']) {
        $paymentData = json_decode($payment['payment_data'] ?? '{}', true);
        
        if ($verificationResult['verified']) {
            // Payment is successful
            if ($payment['status'] !== 'COMPLETED') {
                // Update payment status
                $updatedPaymentData = array_merge($paymentData, [
                    'dpo_verification' => $verificationResult,
                    'verified_at' => date('Y-m-d H:i:s')
                ]);

                updatePaymentStatus($payment['id'], 'COMPLETED', $updatedPaymentData);

                // Log payment event
                if (function_exists('logPaymentEvent')) {
                    logPaymentEvent($payment['id'], 'COMPLETED', 'DPO', [
                        'transaction_token' => $transactionToken,
                        'verification_result' => $verificationResult
                    ]);
                }
            }

            echo json_encode([
                'success' => true,
                'status' => 'completed',
                'verified' => true,
                'message' => 'Payment completed successfully',
                'verification_details' => [
                    'customer_name' => $verificationResult['customerName'] ?? null,
                    'transaction_amount' => $verificationResult['transactionAmount'] ?? null,
                    'transaction_currency' => $verificationResult['transactionCurrency'] ?? null,
                    'transaction_approval' => $verificationResult['transactionApproval'] ?? null
                ]
            ]);

        } else if ($verificationResult['result'] === '900') {
            // Payment is pending
            echo json_encode([
                'success' => true,
                'status' => 'pending',
                'verified' => false,
                'message' => 'Payment is being processed'
            ]);

        } else {
            // Payment failed or other status
            if ($payment['status'] === 'PENDING') {
                // Update payment status to failed
                $updatedPaymentData = array_merge($paymentData, [
                    'dpo_verification' => $verificationResult,
                    'failed_at' => date('Y-m-d H:i:s')
                ]);

                updatePaymentStatus($payment['id'], 'FAILED', $updatedPaymentData);

                // Log payment event
                if (function_exists('logPaymentEvent')) {
                    logPaymentEvent($payment['id'], 'FAILED', 'DPO', [
                        'transaction_token' => $transactionToken,
                        'verification_result' => $verificationResult
                    ]);
                }
            }

            echo json_encode([
                'success' => false,
                'status' => 'failed',
                'verified' => false,
                'message' => $verificationResult['resultExplanation'] ?? 'Payment verification failed',
                'error_code' => $verificationResult['result'] ?? 'unknown'
            ]);
        }

    } else {
        throw new Exception('Failed to verify payment: ' . ($verificationResult['resultExplanation'] ?? 'Unknown error'));
    }

} catch (Exception $e) {
    error_log("DPO Verify Payment Error: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage(),
        'details' => [
            'payment_id' => $paymentId ?? null,
            'transaction_token' => $transactionToken ?? null
        ]
    ]);
}
