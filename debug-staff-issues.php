<?php
/**
 * Staff Issues Diagnostic Script
 * Use this to debug staff-related issues in production
 * Remove this file after fixing the issues
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Staff Issues Diagnostic</h1>";

echo "<h2>Environment Information</h2>";
echo "<p>Is Production: " . (isProduction() ? 'YES' : 'NO') . "</p>";
echo "<p>HTTP Host: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p>Database Name: " . DB_NAME . "</p>";

echo "<h2>Database Connection Test</h2>";
try {
    $testQuery = $database->fetch("SELECT 1 as test");
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>Staff Data Analysis</h2>";

// Check total staff count
try {
    $totalStaff = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF'")['count'];
    echo "<p>Total Staff Members: " . $totalStaff . "</p>";
    
    $activeStaff = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF' AND is_active = 1")['count'];
    echo "<p>Active Staff Members: " . $activeStaff . "</p>";
    
    $inactiveStaff = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF' AND is_active = 0")['count'];
    echo "<p>Inactive Staff Members: " . $inactiveStaff . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error fetching staff counts: " . $e->getMessage() . "</p>";
}

// Check staff table structure
echo "<h3>Staff Table Structure Check</h3>";
try {
    $columns = $database->fetchAll("DESCRIBE users");
    $hasIsActive = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'is_active') {
            $hasIsActive = true;
            echo "<p style='color: green;'>✓ is_active column exists: " . $column['Type'] . " (Default: " . ($column['Default'] ?? 'NULL') . ")</p>";
            break;
        }
    }
    if (!$hasIsActive) {
        echo "<p style='color: red;'>✗ is_active column missing from users table!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking table structure: " . $e->getMessage() . "</p>";
}

// Sample staff data
echo "<h3>Sample Staff Data</h3>";
try {
    $sampleStaff = $database->fetchAll("SELECT id, name, email, role, is_active, created_at FROM users WHERE role = 'STAFF' LIMIT 5");
    if (!empty($sampleStaff)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Is Active</th><th>Created At</th></tr>";
        foreach ($sampleStaff as $staff) {
            $activeStatus = $staff['is_active'] ? 'Yes' : 'No';
            $statusColor = $staff['is_active'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($staff['id']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['name']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['email']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['role']) . "</td>";
            echo "<td style='color: $statusColor;'>" . $activeStatus . "</td>";
            echo "<td>" . htmlspecialchars($staff['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No staff members found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error fetching sample staff data: " . $e->getMessage() . "</p>";
}

// Check staff specialties data
echo "<h2>Staff Specialties Analysis</h2>";
try {
    $totalSpecialties = $database->fetch("SELECT COUNT(*) as count FROM staff_specialties")['count'];
    echo "<p>Total Staff Specialties Records: " . $totalSpecialties . "</p>";

    if ($totalSpecialties > 0) {
        $specialtiesByStaff = $database->fetchAll("
            SELECT u.name, COUNT(ss.id) as specialty_count
            FROM users u
            LEFT JOIN staff_specialties ss ON u.id = ss.user_id
            WHERE u.role = 'STAFF'
            GROUP BY u.id, u.name
            ORDER BY specialty_count DESC
        ");

        echo "<h3>Specialties per Staff Member</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Staff Name</th><th>Number of Specialties</th></tr>";
        foreach ($specialtiesByStaff as $staff) {
            $color = $staff['specialty_count'] > 0 ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($staff['name']) . "</td>";
            echo "<td style='color: $color;'>" . $staff['specialty_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>⚠️ No staff specialties found! This could be why staff suggestions are failing.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking staff specialties: " . $e->getMessage() . "</p>";
}

// Test staff suggestions API
echo "<h2>Staff Suggestions API Test</h2>";
try {
    // Get a sample service ID
    $sampleService = $database->fetch("SELECT id, name FROM services WHERE is_active = 1 LIMIT 1");
    if ($sampleService) {
        echo "<p>Testing with Service ID: " . $sampleService['id'] . " (" . htmlspecialchars($sampleService['name']) . ")</p>";

        // Simulate the API query
        $staff = $database->fetchAll("
            SELECT DISTINCT
                u.id, u.name, u.is_active,
                ss.proficiency_level,
                CASE
                    WHEN ss.proficiency_level = 'EXPERT' THEN 1
                    WHEN ss.proficiency_level = 'ADVANCED' THEN 2
                    WHEN ss.proficiency_level = 'INTERMEDIATE' THEN 3
                    WHEN ss.proficiency_level = 'BEGINNER' THEN 4
                    ELSE 5
                END as ranking
            FROM users u
            LEFT JOIN staff_specialties ss ON u.id = ss.user_id AND ss.service_id = ?
            WHERE u.role = 'STAFF' AND u.is_active = 1
            GROUP BY u.id
            ORDER BY ranking ASC, u.name ASC
        ", [$sampleService['id']]);

        echo "<p>Found " . count($staff) . " active staff members for this service</p>";

        if (!empty($staff)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Is Active</th><th>Proficiency</th><th>Ranking</th></tr>";
            foreach ($staff as $member) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($member['id']) . "</td>";
                echo "<td>" . htmlspecialchars($member['name']) . "</td>";
                echo "<td style='color: green;'>Yes</td>";
                echo "<td>" . htmlspecialchars($member['proficiency_level'] ?? 'None') . "</td>";
                echo "<td>" . htmlspecialchars($member['ranking']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>No staff found for this service. This could indicate missing staff specialties data.</p>";
        }
    } else {
        echo "<p style='color: orange;'>No active services found to test with</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing staff suggestions API: " . $e->getMessage() . "</p>";
}

// Test admin staff query
echo "<h2>Admin Staff Query Test</h2>";
try {
    $adminStaff = $database->fetchAll(
        "SELECT u.id, u.name, u.email, u.is_active, u.created_at, u.updated_at,
                ss.role as staff_role, ss.hourly_rate
         FROM users u
         LEFT JOIN (
             SELECT user_id, role, hourly_rate
             FROM staff_schedules
             GROUP BY user_id, role, hourly_rate
         ) ss ON u.id = ss.user_id
         WHERE u.role = 'STAFF'
         ORDER BY u.name ASC
         LIMIT 5"
    );
    
    echo "<p>Admin query returned " . count($adminStaff) . " staff members</p>";
    
    if (!empty($adminStaff)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Is Active</th><th>Staff Role</th><th>Hourly Rate</th></tr>";
        foreach ($adminStaff as $staff) {
            $activeStatus = $staff['is_active'] ? 'Yes' : 'No';
            $statusColor = $staff['is_active'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($staff['id']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['name']) . "</td>";
            echo "<td>" . htmlspecialchars($staff['email']) . "</td>";
            echo "<td style='color: $statusColor;'>" . $activeStatus . "</td>";
            echo "<td>" . htmlspecialchars($staff['staff_role'] ?? 'None') . "</td>";
            echo "<td>" . htmlspecialchars($staff['hourly_rate'] ?? 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing admin staff query: " . $e->getMessage() . "</p>";
}

echo "<h2>Recommendations</h2>";
echo "<ul>";
echo "<li>If staff are showing as inactive, check the is_active column values in the database</li>";
echo "<li>If staff suggestions are not working, check the staff_specialties table for proper data</li>";
echo "<li>Check the error logs at logs/error.log for detailed error messages</li>";
echo "<li>Ensure the database connection is working properly in production</li>";
echo "</ul>";

echo "<p><strong>Note:</strong> Remove this diagnostic file after fixing the issues for security.</p>";
?>
