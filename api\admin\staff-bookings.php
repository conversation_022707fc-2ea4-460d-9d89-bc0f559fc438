<?php
/**
 * Staff Bookings API
 * Handle staff booking-related API requests for calendar view
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];
$pathParts = explode('/', trim($requestUri, '/'));

// Extract staff ID from URL
$staffId = $_GET['staff_id'] ?? null;
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month

if (!$staffId) {
    http_response_code(400);
    echo json_encode(['error' => 'Staff ID is required']);
    exit;
}

try {
    switch ($method) {
        case 'GET':
            handleGetStaffBookings($staffId, $startDate, $endDate);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetStaffBookings($staffId, $startDate, $endDate) {
    global $database;

    try {
        // Verify staff member exists
        $allStaff = $database->fetchAll("SELECT id, name, email FROM users WHERE role = 'STAFF'");

        $staff = $database->fetch(
            "SELECT id, name FROM users WHERE id = ? AND role = 'STAFF'",
            [$staffId]
        );

        if (!$staff) {

            // If no staff found, return empty data instead of error for better UX
            echo json_encode([
                'success' => true,
                'data' => [
                    'staff' => ['id' => $staffId, 'name' => 'Unknown Staff'],
                    'events' => [],
                    'stats' => [
                        'total_bookings' => 0,
                        'confirmed_bookings' => 0,
                        'completed_bookings' => 0,
                        'pending_bookings' => 0,
                        'cancelled_bookings' => 0,
                        'total_revenue' => 0
                    ],
                    'date_range' => [
                        'start' => $startDate,
                        'end' => $endDate
                    ]
                ],
                'message' => 'No staff member found with this ID',
                'available_staff' => $allStaff
            ]);
            return;
        }
        
        // Get bookings for the staff member in the date range
        $bookings = $database->fetchAll(
            "SELECT
                b.id,
                b.service_id,
                b.package_id,
                b.date,
                b.start_time,
                b.end_time,
                b.status,
                b.total_amount,
                b.notes,
                u.name as customer_name,
                u.email as customer_email,
                u.phone as customer_phone,
                s.name as service_name,
                s.duration as service_duration,
                s.price as service_price,
                p.name as package_name,
                p.price as package_price
             FROM bookings b
             LEFT JOIN users u ON b.user_id = u.id
             LEFT JOIN services s ON b.service_id = s.id
             LEFT JOIN packages p ON b.package_id = p.id
             WHERE b.staff_id = ?
             AND b.date BETWEEN ? AND ?
             ORDER BY b.date ASC, b.start_time ASC",
            [$staffId, $startDate, $endDate]
        );
        
        // Format bookings for calendar display
        $calendarEvents = [];
        foreach ($bookings as $booking) {
            // Determine if this is a service or package booking
            $isPackageBooking = !empty($booking['package_id']) && !empty($booking['package_name']);
            $serviceName = $isPackageBooking ? $booking['package_name'] : ($booking['service_name'] ?? 'Unknown Service');
            $serviceType = $isPackageBooking ? 'Package' : 'Service';

            // Create safe title
            $title = $serviceName . ' - ' . ($booking['customer_name'] ?? 'Unknown Customer');

            $calendarEvents[] = [
                'id' => $booking['id'],
                'title' => $title,
                'start' => $booking['date'] . 'T' . $booking['start_time'],
                'end' => $booking['date'] . 'T' . $booking['end_time'],
                'backgroundColor' => getStatusColor($booking['status']),
                'borderColor' => getStatusColor($booking['status']),
                'textColor' => '#ffffff',
                'extendedProps' => [
                    'booking_id' => $booking['id'],
                    'customer_name' => $booking['customer_name'] ?? 'Unknown Customer',
                    'customer_email' => $booking['customer_email'] ?? '',
                    'customer_phone' => $booking['customer_phone'] ?? '',
                    'service_name' => $serviceName,
                    'service_type' => $serviceType,
                    'service_duration' => $booking['service_duration'] ?? 0,
                    'service_price' => $isPackageBooking ? $booking['package_price'] : $booking['service_price'],
                    'package_name' => $booking['package_name'] ?? null,
                    'is_package' => $isPackageBooking,
                    'status' => $booking['status'],
                    'total_amount' => $booking['total_amount'],
                    'notes' => $booking['notes'] ?? '',
                    'date' => $booking['date'],
                    'start_time' => $booking['start_time'],
                    'end_time' => $booking['end_time']
                ]
            ];
        }
        
        // Get summary statistics
        $stats = [
            'total_bookings' => count($bookings),
            'confirmed_bookings' => count(array_filter($bookings, function($b) { return $b['status'] === 'CONFIRMED'; })),
            'completed_bookings' => count(array_filter($bookings, function($b) { return $b['status'] === 'COMPLETED'; })),
            'pending_bookings' => count(array_filter($bookings, function($b) { return $b['status'] === 'PENDING'; })),
            'cancelled_bookings' => count(array_filter($bookings, function($b) { return $b['status'] === 'CANCELLED'; })),
            'total_revenue' => array_sum(array_map(function($b) { 
                return $b['status'] === 'COMPLETED' ? $b['total_amount'] : 0; 
            }, $bookings))
        ];
        
        $response = [
            'success' => true,
            'data' => [
                'staff' => $staff,
                'events' => $calendarEvents,
                'stats' => $stats,
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate
                ]
            ]
        ];

        echo json_encode($response);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch staff bookings: ' . $e->getMessage()]);
    }
}

function getStatusColor($status) {
    switch (strtoupper($status)) {
        case 'PENDING':
            return '#f59e0b'; // Yellow
        case 'CONFIRMED':
            return '#3b82f6'; // Blue
        case 'IN_PROGRESS':
            return '#8b5cf6'; // Purple
        case 'COMPLETED':
            return '#10b981'; // Green
        case 'CANCELLED':
            return '#ef4444'; // Red
        case 'NO_SHOW':
            return '#6b7280'; // Gray
        default:
            return '#6b7280'; // Gray
    }
}
?>
