<?php
/**
 * SUPER FAST notification count endpoint
 * Minimal overhead for instant response
 */

// Set headers immediately
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Start session quickly
session_start();

// Quick auth check
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode(['success' => false, 'count' => 0, 'error' => 'auth']);
    exit;
}

$staffId = $_SESSION['user_id'];

// Use the app's database connection
try {
    require_once __DIR__ . '/../../config/app.php';
    require_once __DIR__ . '/../../includes/staff_panel_functions.php';

    // Use the simple function we created
    $count = getUnreadNotificationCount($staffId);

    // Return minimal JSON
    echo json_encode([
        'success' => true,
        'count' => $count,
        'staff_id' => $staffId,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    error_log("Quick count API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'count' => 0,
        'error' => 'database_error',
        'message' => $e->getMessage()
    ]);
}
?>
