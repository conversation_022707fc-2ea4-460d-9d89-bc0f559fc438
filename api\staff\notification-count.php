<?php
/**
 * Simple API endpoint to get unread notification count
 * Returns just the count number for the notification badge
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is logged in as staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access',
        'count' => 0
    ]);
    exit;
}

$staffId = $_SESSION['user_id'];

try {
    // Direct database query for unread notifications
    $unreadCount = getUnreadNotificationCount($staffId);
    
    // Log for debugging
    error_log("Notification count for staff {$staffId}: {$unreadCount}");
    
    echo json_encode([
        'success' => true,
        'count' => $unreadCount,
        'staff_id' => $staffId,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    error_log("Error in notification-count.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error',
        'count' => 0
    ]);
}
?>
