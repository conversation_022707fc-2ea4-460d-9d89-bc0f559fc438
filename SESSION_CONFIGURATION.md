# Session Configuration - Persistent Sessions

## Overview

The Flix Salonce application has been configured to use persistent sessions that remain active even when users close their browser. This improves user experience by eliminating unnecessary re-authentication while maintaining proper security measures.

## Configuration Details

### Session Lifetime
- **Duration**: 2 weeks (1,209,600 seconds)
- **Applies to**: All user types (Ad<PERSON>, Staff, Customers)
- **Expiration**: Sessions only expire after 2 weeks of inactivity or explicit logout

### Security Features
- **HTTPOnly Cookies**: Prevents XSS attacks by making cookies inaccessible to JavaScript
- **SameSite Protection**: Set to 'Lax' for CSRF protection
- **Session Regeneration**: Session IDs are regenerated on login to prevent session fixation
- **Strict Mode**: Prevents uninitialized session IDs
- **Cookie-Only Sessions**: Sessions only use cookies, not URL parameters

### Files Modified

#### 1. `config/app.php`
- Added comprehensive session configuration before `session_start()`
- Set persistent cookie parameters
- Configured security settings

#### 2. `includes/auth.php`
- Updated session expiration from 30 days to 2 weeks
- Added session regeneration on login
- Enhanced logout to properly clear session cookies
- Added sliding session expiration (extends on activity)
- Added session cleanup functionality
- Added session debugging methods

## How It Works

### Login Process
1. User logs in with valid credentials
2. Session ID is regenerated for security
3. Session token is created and stored in database
4. Session expires in 2 weeks from login
5. Persistent cookie is set in browser

### Session Validation
1. Check if session variables exist
2. Verify session token in database
3. Check if session hasn't expired
4. Update last activity timestamp
5. Extend session expiration (sliding window)

### Logout Process
1. Remove session from database
2. Clear all session variables
3. Delete session cookie from browser
4. Destroy PHP session

## Testing

### Manual Testing Steps
1. Login to the application
2. Note your login status
3. Close browser completely
4. Reopen browser and return to the application
5. Verify you're still logged in

### Test Script
Use `test_session_persistence.php` to:
- View current session configuration
- Check session status and details
- Test session persistence
- Cleanup expired sessions

### Expected Behavior
- ✅ Sessions persist across browser restarts
- ✅ Sessions work for all user roles (Admin, Staff, Customer)
- ✅ Sessions expire after 2 weeks of inactivity
- ✅ Sessions are destroyed on explicit logout
- ✅ Security measures are maintained

## Security Considerations

### What's Protected
- **Session Fixation**: Prevented by session regeneration
- **XSS Attacks**: Prevented by HTTPOnly cookies
- **CSRF Attacks**: Mitigated by SameSite cookie attribute
- **Session Hijacking**: Reduced by secure token system

### Production Recommendations
1. **Enable HTTPS**: Set `secure` cookie parameter to `true`
2. **Remove Test Script**: Delete `test_session_persistence.php`
3. **Monitor Sessions**: Regularly check session table for anomalies
4. **Log Security Events**: Monitor authentication attempts

## Configuration Options

### For HTTPS Environments
In `config/app.php`, change:
```php
ini_set('session.cookie_secure', 1);     // Enable for HTTPS
// and
'secure' => true,                        // In session_set_cookie_params
```

### Adjust Session Lifetime
To change session duration, modify these values in `config/app.php`:
```php
ini_set('session.cookie_lifetime', NEW_DURATION_SECONDS);
ini_set('session.gc_maxlifetime', NEW_DURATION_SECONDS);
// and in session_set_cookie_params:
'lifetime' => NEW_DURATION_SECONDS,
```

And update the expiration in `includes/auth.php`:
```php
$expires = date('Y-m-d H:i:s', strtotime('+NEW_DURATION'));
```

## Troubleshooting

### Sessions Not Persisting
1. Check browser cookie settings
2. Verify session configuration in `config/app.php`
3. Check server PHP session settings
4. Ensure database sessions table is accessible

### Performance Issues
1. Monitor session cleanup frequency
2. Check database session table size
3. Adjust cleanup probability if needed

### Security Concerns
1. Review session logs regularly
2. Monitor for unusual session patterns
3. Implement additional security measures if needed

## Maintenance

### Regular Tasks
- Monitor session table size
- Review expired session cleanup
- Check for security anomalies
- Update session configuration as needed

### Database Cleanup
The system automatically cleans up expired sessions with a 5% probability on each request. Manual cleanup can be triggered via the test script or by calling:
```php
$auth->cleanupExpiredSessions();
```

## Support

For issues or questions regarding session configuration:
1. Check this documentation
2. Review the test script output
3. Examine server logs for session-related errors
4. Verify database connectivity and session table structure
