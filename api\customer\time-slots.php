<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check authentication and customer role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON input']);
            exit;
        }
        
        $date = $input['date'] ?? '';
        $serviceId = $input['service_id'] ?? '';
        $packageId = $input['package_id'] ?? '';
        $staffId = $input['staff_id'] ?? null;

        if (empty($date) || (empty($serviceId) && empty($packageId))) {
            http_response_code(400);
            echo json_encode(['error' => 'Date and either service_id or package_id are required']);
            exit;
        }
        
        // Validate date format
        if (!DateTime::createFromFormat('Y-m-d', $date)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid date format']);
            exit;
        }
        
        // Check if date is not in the past
        if ($date < date('Y-m-d')) {
            http_response_code(400);
            echo json_encode(['error' => 'Cannot book appointments in the past']);
            exit;
        }
        
        // Get time slots based on service or package
        if (!empty($serviceId)) {
            $timeSlots = getCustomerAvailableTimeSlots($date, $serviceId, $staffId);
            $responseData = [
                'success' => true,
                'slots' => $timeSlots,
                'date' => $date,
                'service_id' => $serviceId,
                'staff_id' => $staffId
            ];
        } else {
            $timeSlots = getCustomerAvailableTimeSlotsForPackage($date, $packageId, $staffId);
            $responseData = [
                'success' => true,
                'slots' => $timeSlots,
                'date' => $date,
                'package_id' => $packageId,
                'staff_id' => $staffId
            ];
        }

        echo json_encode($responseData);
        
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
