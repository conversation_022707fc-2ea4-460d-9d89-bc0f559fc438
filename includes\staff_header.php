<?php
// Get staff profile for header
$staffProfile = [
    'name' => $_SESSION['user_name'] ?? 'Staff Member',
    'email' => $_SESSION['user_email'] ?? '',
    'role' => $_SESSION['user_role'] ?? 'STAFF'
];

$basePath = getBasePath();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?? 'Staff Panel' ?> - Flix Salon & SPA</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#1a1a1a',
                            800: '#141414',
                            900: '#0a0a0a',
                        },
                        'salon-black': '#000000',
                        'salon-gold': '#f59e0b',
                        'salon-white': '#ffffff',
                        'gold-light': '#fbbf24',
                        'gold-dark': '#d97706',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        :root {
            --background: #000000;
            --foreground: #ffffff;
            --gold: #f59e0b;
            --gold-light: #fbbf24;
            --gold-dark: #d97706;
            --black-secondary: #0a0a0a;
            --black-tertiary: #141414;
            --black-quaternary: #1a1a1a;
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--black-tertiary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gold);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gold-dark);
        }

        /* Selection */
        ::selection {
            background: var(--gold);
            color: var(--background);
        }

        /* Notification filter buttons */
        .notification-filter-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            border-radius: 9999px;
            border: 1px solid #475569;
            color: #9ca3af;
            white-space: nowrap;
            transition: all 0.2s;
        }

        .notification-filter-btn:hover {
            color: white;
            border-color: var(--gold);
        }

        .notification-filter-btn.active {
            background-color: var(--gold);
            color: black;
            border-color: var(--gold);
        }
    </style>
</head>
<body class="antialiased min-h-screen bg-salon-black">
    <!-- Staff Header -->
    <header class="bg-salon-black/98 backdrop-blur-md border-b border-secondary-700 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo and Mobile Menu Toggle -->
                <div class="flex items-center">
                    <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-md text-gray-400 hover:text-salon-gold hover:bg-salon-gold/10 focus:outline-none focus:ring-2 focus:ring-salon-gold transition-all duration-300">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <div class="ml-4 lg:ml-0">
                        <h1 class="text-xl font-bold text-salon-gold">Flix Salon & SPA</h1>
                        <p class="text-xs text-gray-400">Staff Panel</p>
                    </div>
                </div>

                <!-- Right Side - User Menu -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notificationButton" class="p-2 rounded-md text-gray-400 hover:text-white hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-salon-gold relative transition-all duration-200">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <!-- NOTIFICATION BADGE - ALWAYS VISIBLE -->
                            <div id="notificationCounter" style="
                                position: absolute;
                                top: -8px;
                                right: -8px;
                                background-color: #ef4444;
                                color: white;
                                border-radius: 50%;
                                width: 22px;
                                height: 22px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 11px;
                                font-weight: bold;
                                z-index: 1000;
                                border: 2px solid #000;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                                visibility: visible;
                            ">...</div>
                        </button>

                        <!-- Quick access to notifications page -->
                        <a href="<?= $basePath ?>/staff/notifications/" class="hidden md:block ml-2 text-xs text-gray-400 hover:text-salon-gold transition-colors">
                            View All
                        </a>

                        <!-- Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-96 bg-secondary-800 rounded-lg shadow-xl border border-secondary-700 z-50 max-h-96 overflow-hidden">
                            <div class="p-4 border-b border-secondary-700">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-white">Notifications</h3>
                                    <div class="flex space-x-2">
                                        <button id="markAllReadBtn" class="text-xs text-salon-gold hover:text-gold-light">Mark all read</button>
                                        <button id="clearAllBtn" class="text-xs text-red-400 hover:text-red-300">Clear all</button>
                                    </div>
                                </div>

                                <!-- Category Filter -->
                                <div class="mt-3 flex space-x-2 overflow-x-auto">
                                    <button class="notification-filter-btn active" data-category="all">All</button>
                                    <button class="notification-filter-btn" data-category="BOOKING">Bookings</button>
                                    <button class="notification-filter-btn" data-category="STAFF">Admin Updates</button>
                                    <button class="notification-filter-btn" data-category="SYSTEM">System</button>
                                </div>
                            </div>

                            <div id="notificationsList" class="max-h-64 overflow-y-auto">
                                <div class="p-4 text-center text-gray-400">
                                    <svg class="mx-auto h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                    <p>Loading notifications...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Time -->
                    <div class="hidden md:block text-right">
                        <p class="text-sm text-gray-400"><?= date('l, F j, Y') ?></p>
                        <p class="text-sm font-semibold text-salon-gold"><?= date('g:i A') ?></p>
                    </div>

                    <!-- User Profile Dropdown -->
                    <div class="relative">
                        <button id="user-menu-toggle" class="flex items-center space-x-3 p-2 rounded-md text-gray-300 hover:text-white hover:bg-salon-gold/10 focus:outline-none focus:ring-2 focus:ring-salon-gold transition-all duration-300">
                            <div class="h-8 w-8 rounded-full bg-salon-gold flex items-center justify-center">
                                <span class="text-sm font-medium text-black">
                                    <?= strtoupper(substr($staffProfile['name'], 0, 2)) ?>
                                </span>
                            </div>
                            <div class="hidden md:block text-left">
                                <p class="text-sm font-medium"><?= htmlspecialchars($staffProfile['name']) ?></p>
                                <p class="text-xs text-gray-400">Staff Member</p>
                            </div>
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- Dropdown Menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-secondary-900/90 backdrop-blur-md border border-secondary-700 rounded-lg shadow-xl py-2 z-50">
                            <a href="<?= $basePath ?>/staff" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7zm0 0V5a2 2 0 012-2h6l2 2h6a2 2 0 012 2v2M7 13h10M7 17h4" />
                                </svg>
                                Dashboard
                            </a>
                            <a href="<?= $basePath ?>/staff/schedule" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                My Schedule
                            </a>
                            <div class="border-t border-secondary-700 my-1"></div>
                            <a href="<?= $basePath ?>/" class="block px-4 py-2 text-sm text-gray-300 hover:bg-secondary-700 hover:text-salon-gold transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                </svg>
                                View Website
                            </a>
                            <a href="<?= $basePath ?>/auth/logout.php" class="block px-4 py-2 text-sm text-red-400 hover:bg-secondary-700 hover:text-red-300 transition-colors duration-200">
                                <svg class="inline h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Sidebar Overlay -->
    <div id="mobile-sidebar-overlay" class="hidden fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-black opacity-50"></div>
    </div>

    <!-- Mobile Sidebar -->
    <div id="mobile-sidebar" class="hidden fixed inset-y-0 left-0 z-50 w-64 bg-secondary-900/95 backdrop-blur-md border-r border-secondary-700 lg:hidden">
        <div class="flex items-center justify-between p-4 border-b border-secondary-700">
            <h2 class="text-lg font-semibold text-salon-gold">Navigation</h2>
            <button id="mobile-sidebar-close" class="p-2 rounded-md text-gray-400 hover:text-salon-gold hover:bg-salon-gold/10 transition-all duration-300">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <nav class="mt-4">
            <?php include __DIR__ . '/staff_sidebar_nav.php'; ?>
        </nav>
    </div>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.remove('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.remove('hidden');
        });

        document.getElementById('mobile-sidebar-close').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        document.getElementById('mobile-sidebar-overlay').addEventListener('click', function() {
            document.getElementById('mobile-sidebar').classList.add('hidden');
            document.getElementById('mobile-sidebar-overlay').classList.add('hidden');
        });

        // User menu toggle
        document.getElementById('user-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('user-menu');
            menu.classList.toggle('hidden');
        });

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');

            if (!userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // Notification system
        document.getElementById('notificationButton').addEventListener('click', function() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.toggle('hidden');

            if (!dropdown.classList.contains('hidden')) {
                loadNotifications();
            }
        });

        // Close notification dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const notificationButton = document.getElementById('notificationButton');
            const notificationDropdown = document.getElementById('notificationDropdown');

            if (!notificationButton.contains(event.target) && !notificationDropdown.contains(event.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });

        // Helper functions for badge classes
        function getCategoryBadgeClass(category) {
            switch (category) {
                case 'BOOKING':
                    return 'bg-blue-500/20 text-blue-400';
                case 'STAFF':
                    return 'bg-purple-500/20 text-purple-400';
                case 'SYSTEM':
                    return 'bg-gray-500/20 text-gray-400';
                default:
                    return 'bg-gray-500/20 text-gray-400';
            }
        }

        function getPriorityBadgeClass(priority) {
            switch (priority) {
                case 'HIGH':
                    return 'bg-red-500/20 text-red-400';
                case 'MEDIUM':
                    return 'bg-yellow-500/20 text-yellow-400';
                case 'LOW':
                    return 'bg-green-500/20 text-green-400';
                default:
                    return 'bg-gray-500/20 text-gray-400';
            }
        }

        // Load notifications
        function loadNotifications() {
            const notificationsList = document.getElementById('notificationsList');
            notificationsList.innerHTML = '<div class="p-4 text-center text-gray-400"><svg class="mx-auto h-8 w-8 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-3.5-3.5a1.5 1.5 0 010-2.12L20 8h-5V6a3 3 0 00-6 0v2H4l3.5 3.38a1.5 1.5 0 010 2.12L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg><p>Loading notifications...</p></div>';

            fetch('<?= getBasePath() ?>/api/staff/notifications.php')
                .then(response => {
                    console.log('📡 Notifications API response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Notifications API data:', data);

                    if (data.error) {
                        console.error('❌ API Error:', data.error);
                        notificationsList.innerHTML = `<div class="p-4 text-center text-red-400">${data.error}</div>`;
                        return;
                    }

                    const notifications = data.data.notifications || [];

                    if (notifications.length === 0) {
                        notificationsList.innerHTML = '<div class="p-4 text-center text-gray-400"><p>No notifications</p></div>';
                        return;
                    }

                    notificationsList.innerHTML = notifications.map(notification => `
                        <div class="p-4 border-b border-secondary-700 hover:bg-secondary-700/50 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryBadgeClass(notification.category)}">
                                            ${notification.category}
                                        </span>
                                        ${notification.priority !== 'MEDIUM' ? `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityBadgeClass(notification.priority)}">
                                                ${notification.priority}
                                            </span>
                                        ` : ''}
                                    </div>
                                    <p class="mt-1 text-sm font-medium text-white">${notification.title}</p>
                                    <p class="mt-1 text-sm text-gray-300">${notification.message}</p>
                                    ${notification.action_url ? `
                                        <a href="${notification.action_url}" class="mt-2 text-sm text-salon-gold hover:text-gold-light transition-colors">
                                            View Details →
                                        </a>
                                    ` : ''}
                                </div>
                                <div class="ml-4 flex-shrink-0 flex items-center space-x-2">
                                    <span class="text-xs text-gray-400">${notification.time_ago}</span>
                                    ${!notification.is_read ? `
                                        <button onclick="markAsRead('${notification.id}')" class="p-1 text-gray-400 hover:text-white transition-colors" title="Mark as read">
                                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </button>
                                    ` : ''}
                                    <button onclick="deleteNotification('${notification.id}')" class="p-1 text-gray-400 hover:text-red-500 transition-colors" title="Delete">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('');
                })
                .catch(error => {
                    console.error('❌ Error loading notifications:', error);
                    notificationsList.innerHTML = '<div class="p-4 text-center text-red-400">Failed to load notifications</div>';
                });
        }

        // Store previous count for comparison
        let previousNotificationCount = 0;

        // INSTANT notification counter update
        function updateNotificationCounter() {
            console.log('🔄 INSTANT: Updating notification counter...');

            const counter = document.getElementById('notificationCounter');
            if (!counter) {
                console.error('❌ INSTANT: Counter element not found');
                return;
            }

            // Create AbortController for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

            // Use the SUPER FAST count API endpoint with timeout
            fetch('<?= getBasePath() ?>/api/staff/quick-count.php', {
                signal: controller.signal,
                cache: 'no-cache',
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    clearTimeout(timeoutId);
                    console.log('📡 FAST: API responded in time');
                    return response.json();
                })
                .then(data => {
                    console.log('📊 FAST: API response:', data);

                    if (!data.success) {
                        console.error('❌ FAST: API Error:', data.error);
                        // Show error state
                        counter.textContent = '!';
                        counter.style.backgroundColor = '#ff8800';
                        counter.style.visibility = 'visible';
                        return;
                    }

                    const unreadCount = parseInt(data.count) || 0;
                    console.log('📬 FAST: Unread count:', unreadCount);

                    // Check for new notifications
                    if (unreadCount > previousNotificationCount && previousNotificationCount >= 0) {
                        console.log('🔔 FAST: New notifications detected!');
                        const button = document.getElementById('notificationButton');
                        if (button) {
                            button.style.animation = 'bounce 1s';
                            setTimeout(() => button.style.animation = '', 1000);
                        }
                    }

                    previousNotificationCount = unreadCount;

                    // UPDATE BADGE: Show immediately, hide only if 0
                    const displayText = unreadCount > 99 ? '99+' : unreadCount.toString();

                    // Save to localStorage for next page load
                    localStorage.setItem('notificationCount', displayText);

                    if (unreadCount > 0) {
                        counter.textContent = displayText;
                        counter.style.visibility = 'visible';
                        counter.style.display = 'flex';
                        counter.style.backgroundColor = '#ef4444';
                        console.log('✅ FAST: SHOWING badge:', unreadCount);
                    } else {
                        counter.style.visibility = 'hidden';
                        counter.style.display = 'none';
                        localStorage.setItem('notificationCount', '0');
                        console.log('✅ FAST: HIDING badge: no notifications');
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('❌ INSTANT: API failed:', error);

                    // Don't show error - use cached count instead
                    const cachedCount = localStorage.getItem('notificationCount');
                    if (cachedCount && cachedCount !== '0') {
                        counter.textContent = cachedCount;
                        counter.style.backgroundColor = '#ef4444';
                        counter.style.visibility = 'visible';
                        counter.style.display = 'flex';
                        console.log('⚡ INSTANT: Using cached count due to API error');
                    } else {
                        // Only show error if no cached count
                        counter.textContent = '0';
                        counter.style.visibility = 'hidden';
                        console.log('⚡ INSTANT: No cached count, hiding badge');
                    }
                });
        }

        // Mark notification as read
        function markAsRead(notificationId) {
            console.log('📖 REAL-TIME: Marking notification as read:', notificationId);

            // Update counter immediately (optimistic update)
            const counter = document.getElementById('notificationCounter');
            if (counter && counter.textContent !== '!' && counter.textContent !== '...') {
                const currentCount = parseInt(counter.textContent) || 0;
                if (currentCount > 0) {
                    const newCount = currentCount - 1;
                    counter.textContent = newCount > 0 ? (newCount > 99 ? '99+' : newCount.toString()) : '0';
                    if (newCount === 0) {
                        counter.style.visibility = 'hidden';
                    }
                    localStorage.setItem('notificationCount', newCount.toString());
                    console.log('⚡ REAL-TIME: Counter updated immediately to:', newCount);
                }
            }

            fetch('<?= getBasePath() ?>/api/staff/notifications.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    id: notificationId,
                    is_read: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('❌ Error marking as read:', data.error);
                    // Revert optimistic update
                    updateNotificationCounter();
                    return;
                }
                console.log('✅ REAL-TIME: Notification marked as read, syncing...');
                // Refresh notifications and counter to sync
                if (typeof loadNotifications === 'function') loadNotifications();
                setTimeout(updateNotificationCounter, 500); // Sync after 500ms
            })
            .catch(error => {
                console.error('❌ Error marking notification as read:', error);
                // Revert optimistic update
                updateNotificationCounter();
            });
        }

        // Delete notification
        function deleteNotification(notificationId) {
            console.log('🗑️ REAL-TIME: Deleting notification:', notificationId);

            // Update counter immediately (optimistic update)
            const counter = document.getElementById('notificationCounter');
            if (counter && counter.textContent !== '!' && counter.textContent !== '...') {
                const currentCount = parseInt(counter.textContent) || 0;
                if (currentCount > 0) {
                    const newCount = currentCount - 1;
                    counter.textContent = newCount > 0 ? (newCount > 99 ? '99+' : newCount.toString()) : '0';
                    if (newCount === 0) {
                        counter.style.visibility = 'hidden';
                    }
                    localStorage.setItem('notificationCount', newCount.toString());
                    console.log('⚡ REAL-TIME: Counter updated immediately to:', newCount);
                }
            }

            fetch(`<?= getBasePath() ?>/api/staff/notifications.php?id=${notificationId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('❌ Error deleting notification:', data.error);
                    // Revert optimistic update
                    updateNotificationCounter();
                    return;
                }
                console.log('✅ REAL-TIME: Notification deleted, syncing...');
                // Refresh notifications and counter to sync
                if (typeof loadNotifications === 'function') loadNotifications();
                setTimeout(updateNotificationCounter, 500); // Sync after 500ms
            })
            .catch(error => {
                console.error('❌ Error deleting notification:', error);
                // Revert optimistic update
                updateNotificationCounter();
            });
        }

        // Request notification permission on page load
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Global functions for real-time updates
        window.refreshNotificationCounter = function() {
            console.log('🔄 Manual refresh triggered');
            updateNotificationCounter();
        };

        // Decrease counter immediately (for real-time updates)
        window.decreaseNotificationCounter = function() {
            const counter = document.getElementById('notificationCounter');
            if (counter && counter.textContent !== '!' && counter.textContent !== '...') {
                const currentCount = parseInt(counter.textContent) || 0;
                if (currentCount > 0) {
                    const newCount = currentCount - 1;
                    counter.textContent = newCount > 0 ? (newCount > 99 ? '99+' : newCount.toString()) : '0';
                    if (newCount === 0) {
                        counter.style.visibility = 'hidden';
                    }
                    localStorage.setItem('notificationCount', newCount.toString());
                    console.log('⚡ REAL-TIME: Counter decreased to:', newCount);
                }
            }
        };

        // Set counter to specific value
        window.setNotificationCounter = function(count) {
            const counter = document.getElementById('notificationCounter');
            if (counter) {
                const displayText = count > 99 ? '99+' : count.toString();
                counter.textContent = displayText;
                if (count > 0) {
                    counter.style.visibility = 'visible';
                    counter.style.display = 'flex';
                    counter.style.backgroundColor = '#ef4444';
                } else {
                    counter.style.visibility = 'hidden';
                }
                localStorage.setItem('notificationCount', displayText);
                console.log('⚡ REAL-TIME: Counter set to:', count);
            }
        };



        // Initialize counter when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const counter = document.getElementById('notificationCounter');
            if (!counter) return;

            // Try to get cached count from localStorage
            const cachedCount = localStorage.getItem('notificationCount');
            if (cachedCount && cachedCount !== '0') {
                counter.textContent = cachedCount;
                counter.style.visibility = 'visible';
                counter.style.display = 'flex';
                counter.style.backgroundColor = '#ef4444';
                console.log('✅ FAST: Badge shown immediately with cached count:', cachedCount);
            } else {
                // Show loading state
                counter.textContent = '...';
                counter.style.visibility = 'visible';
                counter.style.display = 'flex';
                counter.style.backgroundColor = '#666';
                console.log('✅ FAST: Badge shown with loading state');
            }

            // Update with real data IMMEDIATELY (no delay)
            console.log('🚀 INSTANT: Starting immediate API call...');

            // Set a backup timer - if API doesn't respond in 2 seconds, keep cached value
            const backupTimer = setTimeout(() => {
                if (counter.textContent === '...') {
                    const cachedCount = localStorage.getItem('notificationCount');
                    if (cachedCount && cachedCount !== '0') {
                        counter.textContent = cachedCount;
                        counter.style.backgroundColor = '#ef4444';
                        console.log('⚡ INSTANT: Using cached count due to slow API');
                    }
                }
            }, 2000);

            updateNotificationCounter();
        });

        // Update counter every 10 seconds (faster sync)
        setInterval(updateNotificationCounter, 10000);
    </script>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/staff_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
