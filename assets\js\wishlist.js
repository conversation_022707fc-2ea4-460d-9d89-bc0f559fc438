/**
 * Global Wishlist JavaScript Functions
 * Handles wishlist operations across all pages
 */

// Global variables
let csrfToken = '';
let basePath = '';

// Initialize wishlist functionality
function initializeWishlist(token, path) {
    csrfToken = token;
    basePath = path;
    
    // Initialize wishlist states for all heart buttons
    initializeWishlistStates();
    
    // Update wishlist badge count
    updateWishlistBadgeCount();
}

// Initialize wishlist heart states
function initializeWishlistStates() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');
    
    wishlistBtns.forEach(btn => {
        const itemType = btn.dataset.itemType;
        const itemId = btn.dataset.itemId;
        
        // Check if item is in wishlist
        fetch(`${basePath}/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.inWishlist) {
                    updateHeartIcon(btn, true);
                }
            })
            .catch(error => console.error('Error checking wishlist:', error));
    });
}

// Toggle wishlist item
function toggleWishlist(itemType, itemId, button) {
    // Prevent event bubbling
    if (event) {
        event.stopPropagation();
    }
    
    // Add loading state
    const icon = button.querySelector('i');
    const originalClass = icon.className;
    icon.className = 'fas fa-spinner fa-spin text-white';
    
    fetch(`${basePath}/api/wishlist.php?action=toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateHeartIcon(button, data.inWishlist);
            showToast(data.message, 'success');
            updateWishlistBadges(data.count);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Restore original icon if there was an error
        if (icon.className.includes('fa-spinner')) {
            icon.className = originalClass;
        }
    });
}

// Update heart icon appearance
function updateHeartIcon(button, inWishlist) {
    const icon = button.querySelector('i');
    
    if (inWishlist) {
        icon.className = 'fas fa-heart text-red-400';
        button.classList.add('animate-pulse');
        setTimeout(() => button.classList.remove('animate-pulse'), 600);
    } else {
        icon.className = 'far fa-heart text-white hover:text-red-400 transition-colors duration-300';
    }
}

// Update all wishlist badge counts
function updateWishlistBadges(count) {
    // Update header badge
    const headerBadge = document.getElementById('header-wishlist-badge');
    if (headerBadge) {
        if (count > 0) {
            headerBadge.textContent = count;
            headerBadge.style.display = 'inline-block';
        } else {
            headerBadge.style.display = 'none';
        }
    }
    
    // Update sidebar badge
    const sidebarBadge = document.getElementById('wishlist-badge');
    if (sidebarBadge) {
        if (count > 0) {
            sidebarBadge.textContent = count;
            sidebarBadge.style.display = 'inline-block';
        } else {
            sidebarBadge.style.display = 'none';
        }
    }
}

// Update wishlist badge count from server
function updateWishlistBadgeCount() {
    fetch(`${basePath}/api/wishlist.php?action=count`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateWishlistBadges(data.count);
            }
        })
        .catch(error => console.error('Error fetching wishlist count:', error));
}

// Show toast notification
function showToast(message, type = 'success') {
    // Create toast if it doesn't exist
    let toast = document.getElementById('wishlist-toast');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'wishlist-toast';
        toast.className = 'fixed top-4 right-4 z-50 bg-secondary-800 border text-white px-6 py-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
        document.body.appendChild(toast);
    }
    
    // Set content and style based on type
    if (type === 'success') {
        toast.className = 'fixed top-4 right-4 z-50 bg-secondary-800 border border-green-400 text-white px-6 py-4 rounded-lg shadow-lg transform transition-transform duration-300';
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle text-green-400 mr-3"></i>
                <span>${message}</span>
            </div>
        `;
    } else {
        toast.className = 'fixed top-4 right-4 z-50 bg-secondary-800 border border-red-400 text-white px-6 py-4 rounded-lg shadow-lg transform transition-transform duration-300';
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle text-red-400 mr-3"></i>
                <span>${message}</span>
            </div>
        `;
    }
    
    // Show toast
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    // Hide toast after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
    }, 3000);
}

// Add item to wishlist (direct function)
function addToWishlist(itemType, itemId) {
    return fetch(`${basePath}/api/wishlist.php?action=add`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateWishlistBadges(data.count);
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
        return data;
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
        return { success: false, message: 'Network error' };
    });
}

// Remove item from wishlist (direct function)
function removeFromWishlist(itemType, itemId) {
    return fetch(`${basePath}/api/wishlist.php?action=remove`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `item_type=${itemType}&item_id=${itemId}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateWishlistBadges(data.count);
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
        return data;
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
        return { success: false, message: 'Network error' };
    });
}

// Check if item is in wishlist
function isInWishlist(itemType, itemId) {
    return fetch(`${basePath}/api/wishlist.php?action=check&item_type=${itemType}&item_id=${itemId}`)
        .then(response => response.json())
        .then(data => data.success ? data.inWishlist : false)
        .catch(error => {
            console.error('Error checking wishlist:', error);
            return false;
        });
}

// Clear entire wishlist
function clearWishlist() {
    if (!confirm('Are you sure you want to clear your entire wishlist? This action cannot be undone.')) {
        return Promise.resolve({ success: false, message: 'Cancelled' });
    }
    
    return fetch(`${basePath}/api/wishlist.php?action=clear`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateWishlistBadges(0);
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
        return data;
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'error');
        return { success: false, message: 'Network error' };
    });
}

// Utility function to create wishlist heart button
function createWishlistButton(itemType, itemId, className = '') {
    const button = document.createElement('button');
    button.className = `wishlist-btn ${className}`;
    button.setAttribute('data-item-type', itemType);
    button.setAttribute('data-item-id', itemId);
    button.onclick = function() { toggleWishlist(itemType, itemId, this); };
    
    button.innerHTML = '<i class="far fa-heart text-white hover:text-red-400 transition-colors duration-300"></i>';
    
    return button;
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have the required global variables
    if (typeof window.csrfToken !== 'undefined' && typeof window.basePath !== 'undefined') {
        initializeWishlist(window.csrfToken, window.basePath);
    }
});
