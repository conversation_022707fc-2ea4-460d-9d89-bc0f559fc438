# Flix Salon & SPA - Agent Guidelines

## Build/Test Commands
- **Setup**: Visit `http://localhost/flix-php/setup.php` for initial database setup
- **Dependencies**: Run `composer install` to install PHP dependencies (TCPDF, JWT, Stripe)
- **Database**: Import `flix_salonce2.sql` or use setup.php for fresh installation
- **No formal test suite**: Manual testing via web interface required

## Architecture & Structure
- **Tech Stack**: Pure PHP 8.0+, MySQL 8.0+, Tailwind CSS, Vanilla JS
- **Database**: MySQL with PDO, global `$database` instance available in all files
- **Authentication**: Session-based with role-based access (ADMIN, STAFF, CUSTOMER)
- **Main Panels**: `/admin/` (admin), `/staff/` (staff), `/customer/` (customer), root (public site)
- **API**: RESTful endpoints in `/api/` directory with JSON responses
- **Config**: `config/app.php` (app settings), `config/database.php` (DB connection)

## Code Style & Conventions
- **PHP**: Use PDO prepared statements, `sanitize()` function for input validation
- **File Structure**: Includes go in `/includes/`, follow existing directory patterns
- **Headers**: Include appropriate header files - `includes/header.php` for public, `includes/admin_header.php` for admin
- **Database**: Use global `$database` object, methods: `fetchAll()`, `fetch()`, `query()`, `execute()`
- **Functions**: Common utilities in `includes/functions.php` (formatCurrency, generateUUID, sendEmail, etc.)
- **Error Handling**: Use try-catch blocks, log errors with `error_log()`
- **Security**: Always use CSRF tokens (`generateCsrfToken()`, `verifyCsrfToken()`), sanitize input
- **URLs**: Use `getBasePath()` for relative URLs, file linking format for documentation
