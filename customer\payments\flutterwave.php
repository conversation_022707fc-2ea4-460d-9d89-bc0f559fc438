<?php
require_once __DIR__ . '/../../config/app.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Check if Flutterwave is enabled
if (!FLUTTERWAVE_ENABLED) {
    redirect('/customer/payments?error=flutterwave_disabled');
}

$paymentId = $_GET['payment_id'] ?? '';
if (empty($paymentId)) {
    redirect('/customer/payments?error=invalid_payment');
}

global $database;

// Get payment details and verify ownership
$payment = $database->fetch("
    SELECT p.*, b.user_id, b.id as booking_id, b.date, b.start_time,
           s.name as service_name, pkg.name as package_name,
           st.name as staff_name, u.name as customer_name, u.email as customer_email
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    INNER JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages pkg ON b.package_id = pkg.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'FLUTTERWAVE'
", [$paymentId, $_SESSION['user_id']]);

if (!$payment) {
    redirect('/customer/payments?error=payment_not_found');
}

// Check if payment is already completed
if ($payment['status'] === 'COMPLETED') {
    redirect('/customer/payments?success=payment_completed');
}

$serviceName = $payment['service_name'] ?: $payment['package_name'] ?: 'Unknown Service';
$pageTitle = "Flutterwave Payment - " . $serviceName;

// Update payment with Flutterwave transaction reference if not set
if (empty($payment['flutterwave_tx_ref'])) {
    $txRef = $payment['payment_reference'];
    $database->execute("
        UPDATE payments 
        SET flutterwave_tx_ref = ?, updated_at = NOW()
        WHERE id = ?
    ", [$txRef, $paymentId]);
    $payment['flutterwave_tx_ref'] = $txRef;
}

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Flutterwave Payment Page -->
<div class="min-h-screen bg-secondary-900 py-8">
    <div class="max-w-md mx-auto px-4">
        <!-- Payment Header -->
        <div class="bg-secondary-800 rounded-lg p-6 mb-6">
            <div class="text-center mb-4">
                <div class="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8.64 5.23L7.2 6.67l4.8 4.8-4.8 4.8 1.44 1.44L14.08 12 8.64 5.23z"/>
                    </svg>
                </div>
                <h2 class="text-xl font-semibold text-white">Secure Payment with Flutterwave</h2>
                <p class="text-gray-400 text-sm mt-2">Pay with cards, bank transfer, or mobile money</p>
            </div>
            
            <!-- Payment Details -->
            <div class="bg-secondary-700 rounded-lg p-4">
                <h3 class="text-white font-medium mb-2"><?= htmlspecialchars($serviceName) ?></h3>
                <p class="text-gray-400 text-sm mb-1">Staff: <?= htmlspecialchars($payment['staff_name']) ?></p>
                <p class="text-gray-400 text-sm mb-1">Date: <?= date('M j, Y g:i A', strtotime($payment['date'] . ' ' . $payment['start_time'])) ?></p>
                <p class="text-gray-400 text-sm mb-3">Reference: <?= htmlspecialchars($payment['payment_reference']) ?></p>
                <div class="flex justify-between items-center">
                    <span class="text-gray-400">Amount:</span>
                    <span class="text-salon-gold text-xl font-semibold"><?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></span>
                </div>
            </div>
        </div>

        <!-- Payment Button -->
        <div class="bg-secondary-800 rounded-lg p-6">
            <button id="pay-button" class="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
                <i class="fas fa-credit-card mr-2"></i>
                Pay <?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?>
            </button>
            
            <div class="mt-4 text-center">
                <p class="text-gray-400 text-sm">
                    <i class="fas fa-shield-alt mr-1"></i>
                    Secured by Flutterwave
                </p>
                <?php if (strpos(FLUTTERWAVE_PUBLIC_KEY, 'FLWPUBK_TEST') === 0): ?>
                <div class="mt-4 p-3 bg-orange-900/30 border border-orange-600 rounded-lg">
                    <p class="text-orange-400 text-xs font-semibold mb-2">
                        <i class="fas fa-flask mr-1"></i>
                        Test Mode - No real money will be charged
                    </p>
                    <div class="text-orange-300 text-xs">
                        <p class="font-medium mb-1">Test Card Details:</p>
                        <p>Card: ****************</p>
                        <p>CVV: 828 | Expiry: 09/32</p>
                        <p>PIN: 3310 | OTP: 12345</p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Supported Payment Methods -->
        <div class="mt-6 bg-secondary-800 rounded-lg p-4">
            <h4 class="text-white font-medium mb-3">Supported Payment Methods</h4>
            <div class="grid grid-cols-2 gap-3 text-sm">
                <div class="flex items-center text-gray-400">
                    <i class="fas fa-credit-card mr-2"></i>
                    Credit/Debit Cards
                </div>
                <div class="flex items-center text-gray-400">
                    <i class="fas fa-university mr-2"></i>
                    Bank Transfer
                </div>
                <div class="flex items-center text-gray-400">
                    <i class="fas fa-mobile-alt mr-2"></i>
                    Mobile Money
                </div>
                <div class="flex items-center text-gray-400">
                    <i class="fas fa-wallet mr-2"></i>
                    Digital Wallets
                </div>
            </div>
        </div>

        <!-- Cancel Button -->
        <div class="mt-6 text-center">
            <a href="<?= getBasePath() ?>/customer/payments" class="text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Cancel and go back
            </a>
        </div>
    </div>
</div>

<script src="https://checkout.flutterwave.com/v3.js"></script>
<script>
// Initialize Flutterwave Inline
function makePayment() {
    console.log('Initializing Flutterwave Inline payment...');

    // Show loading state
    const payButton = document.getElementById('pay-button');
    const originalContent = payButton.innerHTML;
    payButton.innerHTML = `
        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>
        <span class="ml-2">Initializing payment...</span>
    `;
    payButton.disabled = true;

    FlutterwaveCheckout({
        public_key: "<?= FLUTTERWAVE_PUBLIC_KEY ?>",
        tx_ref: "<?= $payment['flutterwave_tx_ref'] ?>",
        amount: <?= $payment['amount'] ?>,
        currency: "<?= CURRENCY_CODE ?>",
        payment_options: "card,mobilemoney,ussd,banktransfer",
        redirect_url: "<?= getBaseUrl() ?>/customer/payments/flutterwave-return.php?payment_id=<?= $paymentId ?>",
        customer: {
            email: "<?= htmlspecialchars($payment['customer_email']) ?>",
            name: "<?= htmlspecialchars($payment['customer_name']) ?>",
            phone_number: "<?= htmlspecialchars($payment['customer_phone'] ?? '') ?>",
        },
        customizations: {
            title: "<?= htmlspecialchars(APP_NAME) ?>",
            description: "Payment for <?= htmlspecialchars($serviceName) ?>",
            logo: "<?= getBaseUrl() ?>/assets/images/logo.png",
        },
        callback: function (data) {
            console.log("Flutterwave callback received:", data);
            console.log("Payment status:", data.status);
            console.log("Transaction ID:", data.transaction_id);
            console.log("Transaction Reference:", data.tx_ref);

            // Reset button first
            payButton.innerHTML = originalContent;
            payButton.disabled = false;

            if (data.status === 'successful') {
                console.log('Payment successful, verifying...');
                // Verify payment on server
                verifyFlutterwavePayment(data.transaction_id, data.tx_ref);
            } else if (data.status === 'cancelled') {
                console.log('Payment was cancelled by user');
                alert('Payment was cancelled. You can try again when ready.');
            } else {
                console.error('Payment was not successful:', data);
                alert('Payment was not successful. Status: ' + data.status + '. Please try again.');
            }
        },
        onclose: function() {
            console.log("Flutterwave modal closed");
            // Reset button state
            payButton.innerHTML = originalContent;
            payButton.disabled = false;
        }
    });
}

// Attach event listener
document.getElementById('pay-button').addEventListener('click', makePayment);

async function verifyFlutterwavePayment(transactionId, txRef) {
    try {
        console.log('Starting payment verification...');
        console.log('Transaction ID:', transactionId);
        console.log('Transaction Reference:', txRef);
        console.log('Payment ID:', '<?= $paymentId ?>');

        // Show loading
        document.getElementById('pay-button').innerHTML = `
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>
            <span class="ml-2">Verifying payment...</span>
        `;
        document.getElementById('pay-button').disabled = true;

        const verificationData = {
            payment_id: '<?= $paymentId ?>',
            transaction_id: transactionId,
            tx_ref: txRef
        };

        console.log('Sending verification data:', verificationData);

        const response = await fetch('<?= getBasePath() ?>/api/payments/flutterwave/verify-simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(verificationData)
        });

        console.log('Verification response status:', response.status);

        const result = await response.json();
        console.log('Verification result:', result);

        if (result.success) {
            // Show success message briefly before redirect
            document.getElementById('pay-button').innerHTML = `
                <i class="fas fa-check mr-2"></i>
                Payment Verified!
            `;

            setTimeout(() => {
                window.location.href = '<?= getBasePath() ?>/customer/payments?success=payment_completed';
            }, 1500);
        } else {
            console.error('Verification failed:', result.error);

            // Show more detailed error message
            let errorMessage = 'Payment verification failed: ' + result.error;

            <?php if (strpos(FLUTTERWAVE_PUBLIC_KEY, 'FLWPUBK_TEST') === 0): ?>
            errorMessage += '\n\nNote: You are in test mode. This error might be expected for test transactions.';
            <?php endif; ?>

            alert(errorMessage);

            // Reset button
            document.getElementById('pay-button').innerHTML = `
                <i class="fas fa-credit-card mr-2"></i>
                Pay <?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?>
            `;
            document.getElementById('pay-button').disabled = false;
        }

    } catch (error) {
        console.error('Verification error:', error);

        let errorMessage = 'Payment verification failed. Please contact support.';

        <?php if (strpos(FLUTTERWAVE_PUBLIC_KEY, 'FLWPUBK_TEST') === 0): ?>
        errorMessage += '\n\nNote: You are in test mode. Check the browser console for more details.';
        <?php endif; ?>

        alert(errorMessage);

        // Reset button
        document.getElementById('pay-button').innerHTML = `
            <i class="fas fa-credit-card mr-2"></i>
            Pay <?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?>
        `;
        document.getElementById('pay-button').disabled = false;
    }
}
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
