<?php
/**
 * Service Variations Database Migration Script
 * This script applies the necessary database changes for the service variations system
 */

require_once 'config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('This migration script requires admin access. Please log in as an admin.');
}

echo "<h1>Service Variations Database Migration</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>";

try {
    echo "<h2>Step 1: Creating service_variations table</h2>";
    
    // Check if service_variations table already exists
    $tableExists = $database->fetch("SHOW TABLES LIKE 'service_variations'");
    
    if ($tableExists) {
        echo "<p class='warning'>⚠️ service_variations table already exists. Skipping creation.</p>";
    } else {
        $createVariationsTable = "
        CREATE TABLE IF NOT EXISTS service_variations (
            id VARCHAR(36) PRIMARY KEY,
            service_id VARCHAR(36) NOT NULL,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            duration INT NOT NULL COMMENT 'Duration in minutes',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
            INDEX idx_service_variations_service_id (service_id),
            INDEX idx_service_variations_active (is_active),
            INDEX idx_service_variations_sort (sort_order)
        )";
        
        $database->query($createVariationsTable);
        echo "<p class='success'>✓ service_variations table created successfully</p>";
    }
    
    echo "<h2>Step 2: Adding service_variation_id to bookings table</h2>";
    
    // Check if service_variation_id column exists in bookings table
    $columns = $database->fetchAll("DESCRIBE bookings");
    $columnNames = array_column($columns, 'Field');
    
    if (in_array('service_variation_id', $columnNames)) {
        echo "<p class='warning'>⚠️ service_variation_id column already exists in bookings table. Skipping addition.</p>";
    } else {
        // Add service_variation_id column to bookings table
        $addColumnSql = "ALTER TABLE bookings ADD COLUMN service_variation_id VARCHAR(36) NULL";
        $database->query($addColumnSql);
        echo "<p class='success'>✓ service_variation_id column added to bookings table</p>";
        
        // Add foreign key constraint
        $addForeignKeySql = "ALTER TABLE bookings ADD FOREIGN KEY (service_variation_id) REFERENCES service_variations(id) ON DELETE SET NULL";
        $database->query($addForeignKeySql);
        echo "<p class='success'>✓ Foreign key constraint added for service_variation_id</p>";
    }
    
    echo "<h2>Step 3: Verification</h2>";
    
    // Verify service_variations table
    $variationsTableCheck = $database->fetch("SHOW TABLES LIKE 'service_variations'");
    if ($variationsTableCheck) {
        echo "<p class='success'>✓ service_variations table verified</p>";
        
        // Show table structure
        $variationsColumns = $database->fetchAll("DESCRIBE service_variations");
        echo "<p class='info'>service_variations table structure:</p>";
        echo "<pre>";
        foreach ($variationsColumns as $column) {
            echo "{$column['Field']} - {$column['Type']} - {$column['Null']} - {$column['Key']}\n";
        }
        echo "</pre>";
    } else {
        echo "<p class='error'>✗ service_variations table verification failed</p>";
    }
    
    // Verify bookings table update
    $bookingsColumns = $database->fetchAll("DESCRIBE bookings");
    $bookingsColumnNames = array_column($bookingsColumns, 'Field');
    
    if (in_array('service_variation_id', $bookingsColumnNames)) {
        echo "<p class='success'>✓ bookings table service_variation_id column verified</p>";
    } else {
        echo "<p class='error'>✗ bookings table service_variation_id column verification failed</p>";
    }
    
    echo "<h2>Migration Summary</h2>";
    echo "<p class='success'>✅ Database migration completed successfully!</p>";
    echo "<p class='info'>You can now:</p>";
    echo "<ul>";
    echo "<li>Use the service variations system in the admin panel</li>";
    echo "<li>Run the test script: <a href='test_service_variations.php'>test_service_variations.php</a></li>";
    echo "<li>Add sample data: <a href='sample_service_variations.php'>sample_service_variations.php</a></li>";
    echo "<li>Go to <a href='admin/services/'>Admin Services</a> to manage variations</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>Migration Error</h2>";
    echo "<p class='error'>✗ Migration failed: " . $e->getMessage() . "</p>";
    echo "<p class='info'>Please check your database connection and permissions.</p>";
    
    // Show detailed error information
    echo "<h3>Error Details:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='admin/services/'>← Go to Services Management</a></p>";
?>
