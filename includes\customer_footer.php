                </div>
            </main>
        </div>
    </div>
</div>

<script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const mobileSidebarOverlay = document.getElementById('mobile-sidebar-overlay');
        const mobileSidebarClose = document.getElementById('mobile-sidebar-close');
        const userMenuToggle = document.getElementById('user-menu-toggle');
        const userMenu = document.getElementById('user-menu');

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', function() {
                mobileSidebar.classList.remove('hidden');
                mobileSidebarOverlay.classList.remove('hidden');
            });
        }

        if (mobileSidebarClose) {
            mobileSidebarClose.addEventListener('click', function() {
                mobileSidebar.classList.add('hidden');
                mobileSidebarOverlay.classList.add('hidden');
            });
        }

        if (mobileSidebarOverlay) {
            mobileSidebarOverlay.addEventListener('click', function() {
                mobileSidebar.classList.add('hidden');
                mobileSidebarOverlay.classList.add('hidden');
            });
        }

        // User menu toggle
        if (userMenuToggle) {
            userMenuToggle.addEventListener('click', function() {
                userMenu.classList.toggle('hidden');
            });
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            if (userMenuToggle && userMenu && !userMenuToggle.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
    });

    // Auto-refresh page every 5 minutes for dashboard
    if (window.location.pathname.includes('/customer/index.php') || window.location.pathname.endsWith('/customer/')) {
        setTimeout(() => {
            location.reload();
        }, 300000);
    }
</script>
</body>
</html>
