<?php
/**
 * Fix Email and OTP Issues
 * Comprehensive diagnosis and fix for production email and OTP problems
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>🔧 Email & OTP System Diagnosis</h1>";

try {
    echo "<h2>1. Email Configuration Check</h2>";
    
    // Check email constants
    $emailConfigs = [
        'SMTP_HOST' => defined('SMTP_HOST') ? SMTP_HOST : 'NOT DEFINED',
        'SMTP_PORT' => defined('SMTP_PORT') ? SMTP_PORT : 'NOT DEFINED',
        'SMTP_USERNAME' => defined('SMTP_USERNAME') ? SMTP_USERNAME : 'NOT DEFINED',
        'SMTP_PASSWORD' => defined('SMTP_PASSWORD') ? (strlen(SMTP_PASSWORD) > 0 ? 'SET (' . strlen(SMTP_PASSWORD) . ' chars)' : 'EMPTY') : 'NOT DEFINED',
        'SMTP_SECURE' => defined('SMTP_SECURE') ? SMTP_SECURE : 'NOT DEFINED',
        'SMTP_FROM_EMAIL' => defined('SMTP_FROM_EMAIL') ? SMTP_FROM_EMAIL : 'NOT DEFINED',
        'SMTP_FROM_NAME' => defined('SMTP_FROM_NAME') ? SMTP_FROM_NAME : 'NOT DEFINED'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Configuration</th><th>Value</th><th>Status</th></tr>";
    
    foreach ($emailConfigs as $key => $value) {
        $status = ($value !== 'NOT DEFINED' && $value !== 'EMPTY') ? '✅' : '❌';
        echo "<tr><td>$key</td><td>$value</td><td>$status</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Database Structure Check</h2>";
    
    // Check for AUTO_INCREMENT conflicts
    $tables = ['users', 'bookings', 'point_transactions', 'system_settings', 'email_logs'];
    
    foreach ($tables as $table) {
        try {
            $columns = $database->fetchAll("DESCRIBE $table");
            echo "<h3>Table: $table</h3>";
            
            $hasAutoIncrement = false;
            $hasUUID = false;
            
            foreach ($columns as $column) {
                if ($column['Field'] === 'id') {
                    if (strpos($column['Extra'], 'auto_increment') !== false) {
                        $hasAutoIncrement = true;
                        echo "<p style='color: red;'>❌ $table.id has AUTO_INCREMENT: {$column['Type']} {$column['Extra']}</p>";
                    } elseif (strpos($column['Type'], 'varchar(36)') !== false) {
                        $hasUUID = true;
                        echo "<p style='color: green;'>✅ $table.id uses UUID: {$column['Type']}</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ $table.id: {$column['Type']} {$column['Extra']}</p>";
                    }
                    break;
                }
            }
            
            if (!$hasAutoIncrement && !$hasUUID) {
                echo "<p style='color: orange;'>⚠️ $table: No 'id' column found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error checking $table: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>3. Email Sending Test</h2>";
    
    if (isset($_POST['test_email'])) {
        $testEmail = sanitize($_POST['test_email']);
        
        if (filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            echo "<h3>Testing Email to: $testEmail</h3>";
            
            // Test basic SMTP email
            echo "<h4>Basic SMTP Test:</h4>";
            $basicResult = sendSMTPEmail(
                $testEmail,
                'Test Email from Flix Salon',
                '<h1>Test Email</h1><p>This is a test email from Flix Salon & SPA system.</p><p>If you receive this, email sending is working correctly.</p>'
            );
            
            if ($basicResult) {
                echo "<p style='color: green;'>✅ Basic email sent successfully</p>";
            } else {
                echo "<p style='color: red;'>❌ Basic email failed</p>";
            }
            
            // Test OTP email
            echo "<h4>OTP Email Test:</h4>";
            $testOTP = '123456';
            
            // Create a test user record for OTP
            $testUserId = generateUUID();
            try {
                $database->execute(
                    "INSERT INTO users (id, name, email, role, created_at) VALUES (?, ?, ?, 'CUSTOMER', NOW()) 
                     ON DUPLICATE KEY UPDATE name = VALUES(name)",
                    [$testUserId, 'Test User', $testEmail]
                );
                
                $otpResult = sendOTPEmail($testUserId, $testOTP);
                
                if ($otpResult) {
                    echo "<p style='color: green;'>✅ OTP email sent successfully</p>";
                    echo "<p><strong>Test OTP Code:</strong> $testOTP</p>";
                } else {
                    echo "<p style='color: red;'>❌ OTP email failed</p>";
                }
                
                // Clean up test user
                $database->execute("DELETE FROM users WHERE id = ?", [$testUserId]);
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ OTP test error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            // Test customer message
            echo "<h4>Customer Message Test:</h4>";
            try {
                // Find a real customer for testing
                $customer = $database->fetch("SELECT id FROM users WHERE role = 'CUSTOMER' LIMIT 1");
                
                if ($customer) {
                    $messageData = [
                        'message_type' => 'email',
                        'subject' => 'Test Message from Admin',
                        'message' => 'This is a test message to verify the customer messaging system is working correctly.'
                    ];
                    
                    $messageResult = sendCustomerMessage($customer['id'], $messageData);
                    
                    if ($messageResult['success']) {
                        echo "<p style='color: green;'>✅ Customer message function returned success</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Customer message function failed: " . htmlspecialchars($messageResult['error'] ?? 'Unknown error') . "</p>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠️ No customers found for testing</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Customer message test error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Invalid email address</p>";
        }
    }
    
    echo "<h2>4. Database Fixes</h2>";
    
    if (isset($_POST['fix_database'])) {
        echo "<h3>Applying Database Fixes...</h3>";
        
        try {
            $database->beginTransaction();
            
            // Fix system_settings table AUTO_INCREMENT issue
            $systemSettingsColumns = $database->fetchAll("DESCRIBE system_settings");
            $hasAutoIncrement = false;
            
            foreach ($systemSettingsColumns as $column) {
                if ($column['Field'] === 'id' && strpos($column['Extra'], 'auto_increment') !== false) {
                    $hasAutoIncrement = true;
                    break;
                }
            }
            
            if ($hasAutoIncrement) {
                echo "<p>Converting system_settings.id from AUTO_INCREMENT to UUID...</p>";
                
                // Create new table with UUID
                $database->execute("
                    CREATE TABLE system_settings_new (
                        id VARCHAR(36) PRIMARY KEY,
                        setting_key VARCHAR(100) NOT NULL,
                        setting_value TEXT DEFAULT NULL,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_setting_key (setting_key)
                    )
                ");
                
                // Copy data with new UUIDs
                $oldSettings = $database->fetchAll("SELECT * FROM system_settings");
                foreach ($oldSettings as $setting) {
                    $newId = generateUUID();
                    $database->execute(
                        "INSERT INTO system_settings_new (id, setting_key, setting_value, updated_at) VALUES (?, ?, ?, ?)",
                        [$newId, $setting['setting_key'], $setting['setting_value'], $setting['updated_at']]
                    );
                }
                
                // Replace old table
                $database->execute("DROP TABLE system_settings");
                $database->execute("RENAME TABLE system_settings_new TO system_settings");
                
                echo "<p style='color: green;'>✅ Fixed system_settings table</p>";
            } else {
                echo "<p style='color: green;'>✅ system_settings table already uses UUID</p>";
            }
            
            // Ensure email_logs table exists
            try {
                $database->execute("
                    CREATE TABLE IF NOT EXISTS email_logs (
                        id VARCHAR(36) PRIMARY KEY,
                        recipient VARCHAR(255) NOT NULL,
                        subject VARCHAR(255) NOT NULL,
                        status ENUM('SENT', 'FAILED', 'PENDING') NOT NULL,
                        details TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ");
                echo "<p style='color: green;'>✅ Email logs table ensured</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Email logs table: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            $database->commit();
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 Database fixes applied successfully!</p>";
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ Database fix error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Critical error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🧪 Test Forms</h2>";
?>

<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>Email Test</h3>
    <form method="post">
        <label>Test Email Address:</label><br>
        <input type="email" name="test_email" required style="width: 300px; padding: 8px; margin: 5px 0;">
        <br><br>
        <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Test Email Sending</button>
    </form>
</div>

<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>Database Fix</h3>
    <form method="post">
        <input type="hidden" name="fix_database" value="1">
        <p><strong>Warning:</strong> This will fix AUTO_INCREMENT conflicts in the database.</p>
        <button type="submit" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Apply Database Fixes</button>
    </form>
</div>

<div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>📋 Next Steps</h3>
    <ol>
        <li><strong>Test Email:</strong> Use the form above to test email sending</li>
        <li><strong>Fix Database:</strong> Apply database fixes if needed</li>
        <li><strong>Test OTP Flow:</strong> <a href="<?= getBasePath() ?>/auth/forgot-password.php" target="_blank">Test password reset</a></li>
        <li><strong>Test Customer Messages:</strong> <a href="<?= getBasePath() ?>/admin/customers/" target="_blank">Send test message</a></li>
    </ol>
</div>
