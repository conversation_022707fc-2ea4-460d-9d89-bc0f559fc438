<?php
/**
 * Service Subcategory Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new service subcategory
 */
function createServiceSubcategory($data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Subcategory name is required'];
        }
        
        if (empty($data['category_id'])) {
            return ['success' => false, 'error' => 'Main category is required'];
        }
        
        $name = trim($data['name']);
        $categoryId = $data['category_id'];
        
        // Check if main category exists
        $category = $database->fetch("SELECT * FROM service_categories WHERE id = ?", [$categoryId]);
        if (!$category) {
            return ['success' => false, 'error' => 'Main category not found'];
        }
        
        // Check if subcategory already exists in this category
        $existingSubcategory = $database->fetch(
            "SELECT id FROM service_subcategories WHERE name = ? AND category_id = ?",
            [$name, $categoryId]
        );
        
        if ($existingSubcategory) {
            return ['success' => false, 'error' => 'Subcategory with this name already exists in this category'];
        }
        
        // Get next sort order
        $sortOrder = getNextSubcategorySortOrder($categoryId);
        
        $subcategoryId = generateUUID();
        
        $database->query(
            "INSERT INTO service_subcategories (id, name, description, category_id, sort_order, is_active, created_at, updated_at) 
             VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $subcategoryId,
                sanitize($name),
                sanitize($data['description'] ?? ''),
                $categoryId,
                $sortOrder,
                isset($data['is_active']) ? 1 : 0
            ]
        );
        
        return ['success' => true, 'id' => $subcategoryId];
        
    } catch (Exception $e) {
        error_log("Subcategory creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create subcategory'];
    }
}

/**
 * Get subcategory by ID
 */
function getServiceSubcategoryById($subcategoryId) {
    global $database;
    
    return $database->fetch("SELECT * FROM service_subcategories WHERE id = ?", [$subcategoryId]);
}

/**
 * Update a service subcategory
 */
function updateServiceSubcategory($subcategoryId, $data) {
    global $database;
    
    try {
        // Check if subcategory exists
        $subcategory = getServiceSubcategoryById($subcategoryId);
        if (!$subcategory) {
            return ['success' => false, 'error' => 'Subcategory not found'];
        }
        
        // Validate required fields
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Subcategory name is required'];
        }
        
        $name = trim($data['name']);
        $categoryId = $data['category_id'] ?? $subcategory['category_id'];
        
        // Check if name is available (excluding current subcategory)
        $existingSubcategory = $database->fetch(
            "SELECT id FROM service_subcategories WHERE name = ? AND category_id = ? AND id != ?",
            [$name, $categoryId, $subcategoryId]
        );
        
        if ($existingSubcategory) {
            return ['success' => false, 'error' => 'Subcategory with this name already exists in this category'];
        }
        
        $database->query(
            "UPDATE service_subcategories 
             SET name = ?, description = ?, category_id = ?, is_active = ?, updated_at = NOW() 
             WHERE id = ?",
            [
                sanitize($name),
                sanitize($data['description'] ?? ''),
                $categoryId,
                isset($data['is_active']) ? 1 : 0,
                $subcategoryId
            ]
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Subcategory update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update subcategory'];
    }
}

/**
 * Delete a service subcategory
 */
function deleteServiceSubcategory($subcategoryId) {
    global $database;
    
    try {
        // Check if subcategory exists
        $subcategory = getServiceSubcategoryById($subcategoryId);
        if (!$subcategory) {
            return ['success' => false, 'error' => 'Subcategory not found'];
        }
        
        // Check if subcategory has services
        $serviceCount = $database->fetch("SELECT COUNT(*) as count FROM services WHERE subcategory_id = ?", [$subcategoryId]);
        
        if ($serviceCount['count'] > 0) {
            return [
                'success' => false,
                'error' => 'Cannot delete subcategory',
                'details' => 'This subcategory has ' . $serviceCount['count'] . ' service(s) associated with it. Please move or delete the services first.',
                'service_count' => $serviceCount['count']
            ];
        }
        
        // Delete the subcategory
        $result = $database->query("DELETE FROM service_subcategories WHERE id = ?", [$subcategoryId]);
        
        if ($result === false) {
            return ['success' => false, 'error' => 'Failed to delete subcategory from database'];
        }
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Subcategory deletion error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to delete subcategory'];
    }
}

/**
 * Toggle subcategory status
 */
function toggleServiceSubcategoryStatus($subcategoryId) {
    global $database;
    
    try {
        $subcategory = getServiceSubcategoryById($subcategoryId);
        if (!$subcategory) {
            return ['success' => false, 'error' => 'Subcategory not found'];
        }
        
        $newStatus = $subcategory['is_active'] ? 0 : 1;
        
        $database->query(
            "UPDATE service_subcategories SET is_active = ?, updated_at = NOW() WHERE id = ?",
            [$newStatus, $subcategoryId]
        );
        
        return ['success' => true, 'new_status' => $newStatus];
        
    } catch (Exception $e) {
        error_log("Subcategory status toggle error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to toggle subcategory status'];
    }
}

/**
 * Get all subcategories for a category
 */
function getSubcategoriesByCategory($categoryId, $activeOnly = false) {
    global $database;
    
    $whereClause = "WHERE category_id = ?";
    $params = [$categoryId];
    
    if ($activeOnly) {
        $whereClause .= " AND is_active = 1";
    }
    
    return $database->fetchAll(
        "SELECT * FROM service_subcategories $whereClause ORDER BY sort_order, name",
        $params
    );
}

/**
 * Get all subcategories with category information
 */
function getAllSubcategoriesWithCategory($activeOnly = false) {
    global $database;
    
    $whereClause = $activeOnly ? "WHERE ss.is_active = 1 AND sc.is_active = 1" : "";
    
    return $database->fetchAll("
        SELECT 
            ss.*,
            sc.name as category_name,
            sc.description as category_description
        FROM service_subcategories ss
        JOIN service_categories sc ON ss.category_id = sc.id
        $whereClause
        ORDER BY sc.name, ss.sort_order, ss.name
    ");
}

/**
 * Get subcategories with service count
 */
function getSubcategoriesWithCount($categoryId = null) {
    global $database;
    
    $whereClause = "";
    $params = [];
    
    if ($categoryId) {
        $whereClause = "WHERE ss.category_id = ?";
        $params = [$categoryId];
    }
    
    return $database->fetchAll("
        SELECT 
            ss.*,
            sc.name as category_name,
            COUNT(s.id) as service_count
        FROM service_subcategories ss
        JOIN service_categories sc ON ss.category_id = sc.id
        LEFT JOIN services s ON ss.id = s.subcategory_id
        $whereClause
        GROUP BY ss.id, ss.name, ss.description, ss.category_id, ss.sort_order, ss.is_active, ss.created_at, ss.updated_at, sc.name
        ORDER BY sc.name, ss.sort_order, ss.name
    ", $params);
}

/**
 * Get next sort order for subcategory in a category
 */
function getNextSubcategorySortOrder($categoryId) {
    global $database;
    
    $result = $database->fetch(
        "SELECT MAX(sort_order) as max_order FROM service_subcategories WHERE category_id = ?",
        [$categoryId]
    );
    
    return ($result['max_order'] ?? 0) + 1;
}

/**
 * Update subcategory sort order
 */
function updateSubcategorySortOrder($subcategoryId, $newSortOrder) {
    global $database;
    
    try {
        $database->query(
            "UPDATE service_subcategories SET sort_order = ?, updated_at = NOW() WHERE id = ?",
            [$newSortOrder, $subcategoryId]
        );
        
        return ['success' => true];
    } catch (Exception $e) {
        error_log("Subcategory sort order update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update sort order'];
    }
}

/**
 * Get categories with their subcategories
 */
function getCategoriesWithSubcategories($activeOnly = false) {
    global $database;
    
    // Get main categories
    $whereClause = $activeOnly ? "WHERE is_active = 1" : "";
    $categories = $database->fetchAll("SELECT * FROM service_categories $whereClause ORDER BY name");
    
    // Get subcategories for each category
    foreach ($categories as &$category) {
        $category['subcategories'] = getSubcategoriesByCategory($category['id'], $activeOnly);
    }
    
    return $categories;
}

/**
 * Check if subcategory name is available
 */
function isSubcategoryNameAvailable($name, $categoryId, $excludeId = null) {
    global $database;
    
    $params = [trim($name), $categoryId];
    $whereClause = "WHERE name = ? AND category_id = ?";
    
    if ($excludeId) {
        $whereClause .= " AND id != ?";
        $params[] = $excludeId;
    }
    
    $existing = $database->fetch("SELECT id FROM service_subcategories $whereClause", $params);
    return !$existing;
}
?>
