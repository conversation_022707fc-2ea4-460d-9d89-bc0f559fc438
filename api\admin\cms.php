<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/cms_functions.php';

// Check authentication and admin role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetCMS();
            break;
            
        case 'POST':
            handleCMSAction();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetCMS() {
    $action = $_GET['action'] ?? 'dashboard';
    
    switch ($action) {
        case 'dashboard':
            $data = getCMSData();
            break;
            
        case 'content':
            $section = $_GET['section'] ?? null;
            $data = getContentSections($section);
            break;
            
        case 'gallery':
            $category = $_GET['category'] ?? null;
            $isActive = isset($_GET['active']) ? ($_GET['active'] === 'true') : null;
            $data = getGalleryImages($category, $isActive);
            break;
            
        case 'hero':
            $data = getHeroContent();
            break;
            
        case 'statistics':
            $data = getCMSStatistics();
            break;
            
        case 'public-gallery':
            $category = $_GET['category'] ?? null;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
            $data = getPublicGallery($category, $limit);
            break;
            
        case 'public-content':
            $section = $_GET['section'] ?? null;
            $data = getPublicContent($section);
            break;
            
        default:
            $data = getCMSData();
            break;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
}

function handleCMSAction() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'save_content':
            handleSaveContent($input);
            break;
            
        case 'delete_content':
            handleDeleteContent($input);
            break;
            
        case 'add_gallery_image':
            handleAddGalleryImage($input);
            break;
            
        case 'update_gallery_image':
            handleUpdateGalleryImage($input);
            break;
            
        case 'delete_gallery_image':
            handleDeleteGalleryImage($input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handleSaveContent($input) {
    $requiredFields = ['section', 'title', 'content'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $data = [
        'section' => trim($input['section']),
        'title' => trim($input['title']),
        'content' => trim($input['content']),
        'image' => !empty($input['image']) ? trim($input['image']) : null,
        'order_index' => isset($input['orderIndex']) ? intval($input['orderIndex']) : 1,
        'is_active' => isset($input['isActive']) ? ($input['isActive'] ? 1 : 0) : 1
    ];
    
    if (!empty($input['id'])) {
        $data['id'] = $input['id'];
    }
    
    $content = saveContentSection($data);
    
    echo json_encode([
        'success' => true,
        'data' => $content,
        'message' => 'Content saved successfully'
    ]);
}

function handleDeleteContent($input) {
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Content ID is required']);
        return;
    }
    
    deleteContentSection($input['id']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Content deleted successfully'
    ]);
}

function handleAddGalleryImage($input) {
    $requiredFields = ['title', 'imageUrl', 'category'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $data = [
        'title' => trim($input['title']),
        'description' => !empty($input['description']) ? trim($input['description']) : '',
        'image_url' => trim($input['imageUrl']),
        'category' => trim($input['category']),
        'order_index' => isset($input['orderIndex']) ? intval($input['orderIndex']) : 0,
        'is_active' => isset($input['isActive']) ? ($input['isActive'] ? 1 : 0) : 1
    ];
    
    $image = addGalleryImage($data);
    
    echo json_encode([
        'success' => true,
        'data' => $image,
        'message' => 'Gallery image added successfully'
    ]);
}

function handleUpdateGalleryImage($input) {
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Image ID is required']);
        return;
    }
    
    $data = [];
    
    if (isset($input['title'])) {
        $data['title'] = trim($input['title']);
    }
    
    if (isset($input['description'])) {
        $data['description'] = trim($input['description']);
    }
    
    if (isset($input['imageUrl'])) {
        $data['image_url'] = trim($input['imageUrl']);
    }
    
    if (isset($input['category'])) {
        $data['category'] = trim($input['category']);
    }
    
    if (isset($input['orderIndex'])) {
        $data['order_index'] = intval($input['orderIndex']);
    }
    
    if (isset($input['isActive'])) {
        $data['is_active'] = $input['isActive'] ? 1 : 0;
    }
    
    if (empty($data)) {
        http_response_code(400);
        echo json_encode(['error' => 'No fields to update']);
        return;
    }
    
    $image = updateGalleryImage($input['id'], $data);
    
    echo json_encode([
        'success' => true,
        'data' => $image,
        'message' => 'Gallery image updated successfully'
    ]);
}

function handleDeleteGalleryImage($input) {
    if (empty($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Image ID is required']);
        return;
    }
    
    deleteGalleryImage($input['id']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Gallery image deleted successfully'
    ]);
}

// Additional endpoints for specific data
if (isset($_GET['endpoint'])) {
    handleSpecificEndpoint($_GET['endpoint']);
}

function handleSpecificEndpoint($endpoint) {
    switch ($endpoint) {
        case 'content-types':
            echo json_encode([
                'success' => true,
                'data' => getContentTypes()
            ]);
            break;
            
        case 'gallery-categories':
            echo json_encode([
                'success' => true,
                'data' => getGalleryCategories()
            ]);
            break;
            
        case 'content-by-section':
            $section = $_GET['section'] ?? '';
            if (empty($section)) {
                http_response_code(400);
                echo json_encode(['error' => 'Section is required']);
                return;
            }
            
            $content = getContentSections($section);
            echo json_encode([
                'success' => true,
                'data' => $content
            ]);
            break;
            
        case 'gallery-by-category':
            $category = $_GET['category'] ?? '';
            $images = getGalleryImages($category, true); // Only active images
            
            echo json_encode([
                'success' => true,
                'data' => $images
            ]);
            break;
            
        case 'recent-updates':
            $stats = getCMSStatistics();
            echo json_encode([
                'success' => true,
                'data' => $stats['recentUpdates'] ?? []
            ]);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}
?>
