<?php
/**
 * Admin Customer View
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/admin/login');
}

// Get customer ID from URL
$customerId = $_GET['id'] ?? '';
if (empty($customerId)) {
    $_SESSION['error'] = 'Customer ID is required';
    redirect('/admin/customers');
}

// Try to get customer details
$customerData = getCustomerDetails($customerId);

if (!$customerData) {
    // Fallback: Try to get basic customer info directly
    $customer = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
        [$customerId]
    );

    if (!$customer) {
        $_SESSION['error'] = 'Customer not found (ID: ' . htmlspecialchars($customerId) . ')';
        redirect('/admin/customers');
    }

    // Create minimal customer data structure
    $customerData = [
        'customer' => $customer,
        'stats' => [
            'total_bookings' => 0,
            'completed_bookings' => 0,
            'cancelled_bookings' => 0,
            'total_spent' => 0,
            'last_booking_date' => null,
            'first_booking_date' => null
        ],
        'recent_bookings' => [],
        'points_history' => [],
        'favorite_services' => []
    ];

    // Try to get basic booking stats
    try {
        $basicStats = $database->fetch(
            "SELECT COUNT(*) as total_bookings,
                    COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_spent
             FROM bookings WHERE user_id = ?",
            [$customerId]
        );
        if ($basicStats) {
            $customerData['stats']['total_bookings'] = $basicStats['total_bookings'];
            $customerData['stats']['total_spent'] = $basicStats['total_spent'];
        }
    } catch (Exception $e) {
        // Ignore booking stats errors for now
    }
}

$customer = $customerData['customer'];
$stats = $customerData['stats'];
$recentBookings = $customerData['recent_bookings'];
$favoriteServices = $customerData['favorite_services'];

// Ensure customer has points field
if (!isset($customer['points'])) {
    $customer['points'] = 0;
}

$pageTitle = "Customer Details - " . $customer['name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-16 w-16">
                                    <div class="h-16 w-16 rounded-full bg-salon-gold flex items-center justify-center">
                                        <span class="text-2xl font-bold text-black">
                                            <?= strtoupper(substr($customer['name'] ?? 'UN', 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-6">
                                    <h1 class="text-2xl font-bold text-white"><?= htmlspecialchars($customer['name'] ?? 'Unknown Customer') ?></h1>
                                    <p class="mt-1 text-sm text-gray-300"><?= htmlspecialchars($customer['email'] ?? 'No email') ?></p>
                                    <?php if (!empty($customer['phone'])): ?>
                                        <p class="text-sm text-gray-300"><?= htmlspecialchars($customer['phone']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-2">
                                <a href="<?= getBasePath() ?>/admin/customers" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Customers
                                </a>
                                <button onclick="sendMessage('<?= $customer['id'] ?>')" 
                                        class="bg-green-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                                    Send Message
                                </button>
                                <button onclick="editPoints('<?= $customer['id'] ?>', '<?= htmlspecialchars($customer['name']) ?>', <?= $customer['points'] ?>)" 
                                        class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Edit Points
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Customer Information -->
                        <div class="lg:col-span-2">
                            <!-- Stats Cards -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                <div class="bg-secondary-800 rounded-lg p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-8 w-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-gray-300 truncate">Total Bookings</dt>
                                                <dd class="text-lg font-medium text-white"><?= number_format($stats['total_bookings']) ?></dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-secondary-800 rounded-lg p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-8 w-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-gray-300 truncate">Total Spent</dt>
                                                <dd class="text-lg font-medium text-white"><?= formatCurrency($stats['total_spent']) ?></dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-secondary-800 rounded-lg p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-8 w-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-gray-300 truncate">Loyalty Points</dt>
                                                <dd class="text-lg font-medium text-white"><?= number_format($customer['points']) ?></dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Bookings -->
                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h3 class="text-lg font-semibold text-white mb-4">Recent Bookings</h3>
                                <?php if (!empty($recentBookings)): ?>
                                    <div class="space-y-4">
                                        <?php foreach ($recentBookings as $booking): ?>
                                            <div class="border border-secondary-700 rounded-lg p-4">
                                                <div class="flex justify-between items-start">
                                                    <div>
                                                        <?php
                                                        // Display service name or package name depending on booking type
                                                        $displayName = 'Unknown Service';
                                                        $bookingType = '';

                                                        if (!empty($booking['service_name'])) {
                                                            $displayName = $booking['service_name'];
                                                            $bookingType = 'Service';
                                                        } elseif (!empty($booking['package_name'])) {
                                                            $displayName = $booking['package_name'];
                                                            $bookingType = 'Package';
                                                        }
                                                        ?>
                                                        <div class="flex items-center gap-2">
                                                            <h4 class="text-sm font-medium text-white"><?= htmlspecialchars($displayName) ?></h4>
                                                            <?php if ($bookingType): ?>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                                    <?= $bookingType ?>
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                        <p class="text-sm text-gray-300">
                                                            <?= date('M j, Y', strtotime($booking['date'])) ?> at <?= date('g:i A', strtotime($booking['start_time'])) ?>
                                                        </p>
                                                        <?php if (!empty($booking['staff_name'])): ?>
                                                            <p class="text-sm text-gray-400">with <?= htmlspecialchars($booking['staff_name']) ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="text-right">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                            <?php
                                                            switch ($booking['status']) {
                                                                case 'PENDING':
                                                                    echo 'bg-yellow-100 text-yellow-800';
                                                                    break;
                                                                case 'CONFIRMED':
                                                                    echo 'bg-blue-100 text-blue-800';
                                                                    break;
                                                                case 'COMPLETED':
                                                                    echo 'bg-green-100 text-green-800';
                                                                    break;
                                                                case 'CANCELLED':
                                                                    echo 'bg-red-100 text-red-800';
                                                                    break;
                                                                case 'NO_SHOW':
                                                                    echo 'bg-gray-100 text-gray-800';
                                                                    break;
                                                                default:
                                                                    echo 'bg-gray-100 text-gray-800';
                                                            }
                                                            ?>">
                                                            <?= ucfirst(strtolower(str_replace('_', ' ', $booking['status']))) ?>
                                                        </span>
                                                        <p class="text-sm font-medium text-salon-gold mt-1"><?= formatCurrency($booking['total_amount']) ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="mt-4">
                                        <a href="<?= getBasePath() ?>/admin/bookings?customer_id=<?= $customer['id'] ?>" 
                                           class="text-blue-400 hover:text-blue-300 text-sm">
                                            View all bookings →
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-gray-400">No bookings found.</p>
                                <?php endif; ?>
                            </div>

                            <!-- Favorite Services & Packages -->
                            <?php if (!empty($favoriteServices)): ?>
                                <div class="bg-secondary-800 shadow rounded-lg p-6">
                                    <h3 class="text-lg font-semibold text-white mb-4">Most Booked</h3>
                                    <div class="space-y-3">
                                        <?php foreach ($favoriteServices as $service): ?>
                                            <div class="flex justify-between items-center">
                                                <div class="flex items-center gap-2">
                                                    <span class="text-sm text-white"><?= htmlspecialchars($service['name'] ?? 'Unknown') ?></span>
                                                    <?php if (isset($service['type'])): ?>
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <?= $service['type'] === 'Package' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800' ?>">
                                                            <?= $service['type'] ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                                <span class="text-sm text-gray-400"><?= intval($service['booking_count'] ?? 0) ?> times</span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Customer Details Sidebar -->
                        <div>
                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h3 class="text-lg font-semibold text-white mb-4">Customer Details</h3>
                                
                                <div class="space-y-4">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Email</h4>
                                        <p class="text-white"><?= htmlspecialchars($customer['email'] ?? 'No email provided') ?></p>
                                    </div>

                                    <?php if (!empty($customer['phone'])): ?>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Phone</h4>
                                        <p class="text-white"><?= htmlspecialchars($customer['phone']) ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($customer['date_of_birth'])): ?>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Date of Birth</h4>
                                        <p class="text-white"><?= date('M j, Y', strtotime($customer['date_of_birth'])) ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Member Since</h4>
                                        <p class="text-white"><?= date('M j, Y', strtotime($customer['created_at'])) ?></p>
                                    </div>
                                    
                                    <?php if (!empty($stats['first_booking_date'])): ?>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">First Visit</h4>
                                        <p class="text-white"><?= date('M j, Y', strtotime($stats['first_booking_date'])) ?></p>
                                    </div>
                                    <?php endif; ?>

                                    <?php if (!empty($stats['last_booking_date'])): ?>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-300 mb-1">Last Visit</h4>
                                        <p class="text-white"><?= date('M j, Y', strtotime($stats['last_booking_date'])) ?></p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Quick Stats -->
                            <div class="bg-secondary-800 shadow rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-white mb-4">Quick Stats</h3>
                                
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-300">Completed Bookings</span>
                                        <span class="text-sm text-white"><?= number_format($stats['completed_bookings']) ?></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-300">Cancelled Bookings</span>
                                        <span class="text-sm text-white"><?= number_format($stats['cancelled_bookings']) ?></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-300">Average Booking Value</span>
                                        <span class="text-sm text-white"><?= formatCurrency($stats['avg_booking_value'] ?? 0) ?></span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-300">Loyalty Points</span>
                                        <span class="text-sm text-purple-400"><?= number_format($customer['points']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function sendMessage(customerId) {
    window.location.href = `<?= getBasePath() ?>/admin/customers/message.php?id=${customerId}`;
}

function editPoints(customerId, customerName, currentPoints) {
    // This would open a modal or redirect to points editing page
    alert('Points editing functionality would be implemented here');
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
