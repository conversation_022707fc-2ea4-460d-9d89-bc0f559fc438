<?php
/**
 * Day Bookings API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];
$date = $_GET['date'] ?? null;

if (!$date) {
    http_response_code(400);
    echo json_encode(['error' => 'Date is required']);
    exit;
}

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid date format. Use YYYY-MM-DD']);
    exit;
}

try {
    if ($method === 'GET') {
        handleGetDayBookings($date);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetDayBookings($date) {
    global $database;
    
    try {
        // Get all bookings for the specified date with detailed information
        $bookings = $database->fetchAll("
            SELECT
                b.*,
                u.name as customer_name,
                u.email as customer_email,
                u.phone as customer_phone,
                s.name as service_name,
                s.price as service_price,
                s.duration as service_duration,
                s.description as service_description,
                p.name as package_name,
                p.price as package_price,
                st.name as staff_name,
                st.email as staff_email,
                st.phone as staff_phone
            FROM bookings b
            LEFT JOIN users u ON b.user_id = u.id
            LEFT JOIN services s ON b.service_id = s.id
            LEFT JOIN packages p ON b.package_id = p.id
            LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
            WHERE b.date = ?
            ORDER BY b.start_time ASC, b.created_at ASC
        ", [$date]);
        
        // Format the bookings data
        $formattedBookings = [];
        foreach ($bookings as $booking) {
            $formattedBookings[] = [
                'id' => $booking['id'],
                'user_id' => $booking['user_id'],
                'service_id' => $booking['service_id'],
                'package_id' => $booking['package_id'],
                'staff_id' => $booking['staff_id'],
                'date' => $booking['date'],
                'start_time' => $booking['start_time'],
                'end_time' => $booking['end_time'],
                'status' => $booking['status'],
                'total_amount' => $booking['total_amount'],
                'points_used' => $booking['points_used'],
                'points_earned' => $booking['points_earned'],
                'notes' => $booking['notes'],
                'created_at' => $booking['created_at'],
                'updated_at' => $booking['updated_at'],
                
                // Customer information
                'customer_name' => $booking['customer_name'],
                'customer_email' => $booking['customer_email'],
                'customer_phone' => $booking['customer_phone'],
                
                // Service information
                'service_name' => $booking['service_name'],
                'service_price' => $booking['service_price'],
                'service_duration' => $booking['service_duration'],
                'service_description' => $booking['service_description'],

                // Package information
                'package_name' => $booking['package_name'],
                'package_price' => $booking['package_price'],
                
                // Staff information
                'staff_name' => $booking['staff_name'],
                'staff_email' => $booking['staff_email'],
                'staff_phone' => $booking['staff_phone']
            ];
        }
        
        // Get summary statistics
        $totalBookings = count($formattedBookings);

        // Calculate revenue only from completed bookings
        $completedBookings = array_filter($formattedBookings, function($booking) {
            return $booking['status'] === 'COMPLETED';
        });
        $totalRevenue = array_sum(array_column($completedBookings, 'total_amount'));
        $completedCount = count($completedBookings);

        // Get all status counts for overview
        $statusCounts = [];
        foreach ($formattedBookings as $booking) {
            $status = $booking['status'];
            $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
        }
        
        echo json_encode([
            'success' => true,
            'date' => $date,
            'bookings' => $formattedBookings,
            'summary' => [
                'total_bookings' => $totalBookings,
                'completed_bookings' => $completedCount,
                'total_revenue' => $totalRevenue,
                'status_counts' => $statusCounts
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch day bookings: ' . $e->getMessage()]);
    }
}
?>
