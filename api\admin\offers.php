<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/offer_functions.php';

// Check authentication and admin role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetOffers();
            break;
            
        case 'POST':
            handleCreateOffer();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetOffers() {
    $search = $_GET['search'] ?? '';
    $status = $_GET['status'] ?? 'all';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    
    $offers = getAllOffers($search, $status, $limit, $offset);
    
    // Format dates for JSON
    foreach ($offers as &$offer) {
        $offer['valid_from'] = date('Y-m-d', strtotime($offer['valid_from']));
        $offer['valid_to'] = date('Y-m-d', strtotime($offer['valid_to']));
        $offer['created_at'] = date('Y-m-d H:i:s', strtotime($offer['created_at']));
        $offer['updated_at'] = date('Y-m-d H:i:s', strtotime($offer['updated_at']));
        
        // Add status information
        $offer['is_expired'] = strtotime($offer['valid_to']) < time();
        $offer['is_valid'] = $offer['is_active'] && !$offer['is_expired'];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $offers
    ]);
}

function handleCreateOffer() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    // Validate required fields
    $required = ['title', 'description', 'discount', 'code', 'validFrom', 'validTo'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    // Prepare data for creation
    $data = [
        'title' => trim($input['title']),
        'description' => trim($input['description']),
        'discount' => floatval($input['discount']),
        'code' => strtoupper(trim($input['code'])),
        'valid_from' => $input['validFrom'],
        'valid_to' => $input['validTo'],
        'max_usage' => !empty($input['maxUsage']) ? intval($input['maxUsage']) : null,
        'is_active' => isset($input['isActive']) ? ($input['isActive'] ? 1 : 0) : 1,
        'image' => !empty($input['image']) ? trim($input['image']) : null
    ];
    
    $offer = createOffer($data);
    
    // Format response
    $offer['valid_from'] = date('Y-m-d', strtotime($offer['valid_from']));
    $offer['valid_to'] = date('Y-m-d', strtotime($offer['valid_to']));
    $offer['created_at'] = date('Y-m-d H:i:s', strtotime($offer['created_at']));
    $offer['updated_at'] = date('Y-m-d H:i:s', strtotime($offer['updated_at']));
    $offer['is_expired'] = strtotime($offer['valid_to']) < time();
    $offer['is_valid'] = $offer['is_active'] && !$offer['is_expired'];
    
    http_response_code(201);
    echo json_encode([
        'success' => true,
        'data' => $offer,
        'message' => 'Offer created successfully'
    ]);
}
?>
