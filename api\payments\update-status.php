<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

// Check authentication and customer role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $paymentId = $input['payment_id'] ?? '';
    $status = $input['status'] ?? '';
    $stripePaymentId = $input['stripe_payment_id'] ?? '';
    
    if (empty($paymentId)) {
        throw new Exception('Payment ID is required');
    }
    
    if (empty($status)) {
        throw new Exception('Status is required');
    }
    
    if (!in_array($status, ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'])) {
        throw new Exception('Invalid status');
    }
    
    global $database;
    
    // Verify payment belongs to user
    $payment = $database->fetch("
        SELECT p.*, b.user_id, b.id as booking_id
        FROM payments p
        INNER JOIN bookings b ON p.booking_id = b.id
        WHERE p.id = ? AND b.user_id = ?
    ", [$paymentId, $_SESSION['user_id']]);
    
    if (!$payment) {
        throw new Exception('Payment not found or access denied');
    }
    
    // Prepare gateway data
    $gatewayData = [];
    if (!empty($stripePaymentId)) {
        $gatewayData['stripe_payment_id'] = $stripePaymentId;
    }
    
    // Update payment status
    updatePaymentStatus($paymentId, $status, $gatewayData);
    
    echo json_encode([
        'success' => true,
        'message' => 'Payment status updated successfully',
        'payment_id' => $paymentId,
        'status' => $status
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
