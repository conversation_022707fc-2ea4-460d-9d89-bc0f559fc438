<?php
/**
 * Registration Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config/app.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

$errors = [];
$success = '';

// Get redirect parameter
$redirectUrl = $_GET['redirect'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = [
        'name' => sanitize($_POST['name'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'phone' => sanitize($_POST['phone'] ?? ''),
        'referral_code' => sanitize($_POST['referral_code'] ?? '')
    ];
    $redirectUrl = sanitize($_POST['redirect'] ?? '');

    // Validate password confirmation
    if ($data['password'] !== $data['confirm_password']) {
        $errors['confirm_password'] = 'Passwords do not match';
    }

    if (empty($errors)) {
        $result = $auth->register($data);

        if ($result['success']) {
            // Auto-login the user after successful registration
            $loginResult = $auth->login($data['email'], $data['password']);

            if ($loginResult['success'] && !empty($redirectUrl)) {
                // Validate redirect URL to prevent open redirect attacks
                $allowedPaths = ['/customer/', '/services.php'];
                $isValidRedirect = false;

                foreach ($allowedPaths as $allowedPath) {
                    if (strpos($redirectUrl, $allowedPath) !== false) {
                        $isValidRedirect = true;
                        break;
                    }
                }

                if ($isValidRedirect) {
                    redirect($redirectUrl);
                } else {
                    redirect('/customer');
                }
            } else {
                $success = 'Account created successfully! You can now login.';
                // Clear form data
                $data = [];
            }
        } else {
            $errors = $result['errors'];
        }
    }
}

$pageTitle = "Register";
$pageDescription = "Create your Flix Salon & SPA account";

// Include header
include __DIR__ . '/../includes/header.php';
?>
<!-- Enhanced Auth Page Styles - Unified Design -->
<style>
    /* Auth page background with gradient overlay */
    .auth-main {
        min-height: calc(100vh - 140px);
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
        background-attachment: fixed;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
    }

    .auth-main::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Glass container with enhanced effects */
    .auth-container {
        background: rgba(10, 10, 10, 0.85);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(245, 158, 11, 0.2);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(245, 158, 11, 0.1),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.5), transparent);
    }

    /* Enhanced input styling */
    .input-group {
        position: relative;
    }

    .input-group input {
        background: rgba(20, 20, 20, 0.8) !important;
        border: 1px solid rgba(75, 85, 99, 0.5) !important;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .input-group input:focus {
        background: rgba(20, 20, 20, 0.95) !important;
        border-color: rgba(245, 158, 11, 0.8) !important;
        box-shadow:
            0 0 0 3px rgba(245, 158, 11, 0.1),
            0 4px 12px rgba(245, 158, 11, 0.15);
        transform: translateY(-1px);
    }

    .input-group input:focus + .input-border {
        transform: scaleX(1);
    }

    .input-border {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f59e0b, #fbbf24, #f59e0b);
        transform: scaleX(0);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 1px;
    }

    /* Enhanced button styling */
    .btn-primary {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
        background-size: 200% 200%;
        border: 1px solid rgba(245, 158, 11, 0.3);
        box-shadow:
            0 4px 15px rgba(245, 158, 11, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
        background-position: 100% 0;
        transform: translateY(-2px);
        box-shadow:
            0 8px 25px rgba(245, 158, 11, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(245, 158, 11, 0.6);
    }

    .btn-primary:hover:not(:disabled)::before {
        left: 100%;
    }

    .btn-primary:disabled {
        background: rgba(107, 114, 128, 0.5);
        border-color: rgba(107, 114, 128, 0.3);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        backdrop-filter: blur(10px);
    }

    .btn-primary:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    /* Enhanced password requirements styling */
    .password-requirements {
        background: rgba(10, 10, 10, 0.9);
        border: 1px solid rgba(245, 158, 11, 0.2);
        backdrop-filter: blur(15px);
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .password-requirements.hidden {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
        pointer-events: none;
    }

    .password-requirements:not(.hidden) {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    .requirement-item {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 0.25rem;
        border-radius: 0.5rem;
    }

    .requirement-met {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
        transform: scale(1.02);
    }

    .requirement-unmet {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.05);
    }

    /* Enhanced strength bar */
    .strength-bar {
        height: 6px;
        background: rgba(55, 65, 81, 0.8);
        border-radius: 3px;
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(75, 85, 99, 0.3);
    }

    #password-strength-container {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #password-strength-container.hidden {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
        pointer-events: none;
    }

    #password-strength-container:not(.hidden) {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    .strength-fill {
        height: 100%;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 2px;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
    }

    .strength-weak {
        background: linear-gradient(90deg, #ef4444, #dc2626);
        width: 20%;
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
    }
    .strength-fair {
        background: linear-gradient(90deg, #f59e0b, #d97706);
        width: 40%;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
    }
    .strength-good {
        background: linear-gradient(90deg, #eab308, #ca8a04);
        width: 60%;
        box-shadow: 0 0 10px rgba(234, 179, 8, 0.3);
    }
    .strength-strong {
        background: linear-gradient(90deg, #22c55e, #16a34a);
        width: 80%;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
    }
    .strength-excellent {
        background: linear-gradient(90deg, #10b981, #059669);
        width: 100%;
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
    }

    /* Enhanced animations */
    .error-message, .success-message {
        animation: slideInEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideInEnhanced {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .pulse-animation {
        animation: pulseEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes pulseEnhanced {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(245, 158, 11, 0.3); }
        100% { transform: scale(1); }
    }

    /* Enhanced checkbox styling */
    input[type="checkbox"] {
        background: rgba(20, 20, 20, 0.8);
        border: 1px solid rgba(75, 85, 99, 0.5);
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    input[type="checkbox"]:checked {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        border-color: #f59e0b;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
    }

    /* Link enhancements */
    a {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    a:hover {
        text-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
    }

    /* Section headers enhancement */
    h3 {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }
</style>
</style>

<!-- Auth Page Content -->
<div class="auth-main">
    <div class="max-w-4xl w-full space-y-8">
        <div class="auth-container auth-card rounded-2xl p-8 sm:p-10 lg:p-12">
            <div class="text-center mb-8 lg:mb-10">
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Create Your Account</h2>
                <p class="text-gray-300 text-sm sm:text-base">
                    Join us for an exclusive beauty experience
                </p>
            </div>

            <form id="registrationForm" class="space-y-6" method="POST" autocomplete="off">
                <!-- Hidden redirect field -->
                <?php if (!empty($redirectUrl)): ?>
                    <input type="hidden" name="redirect" value="<?= htmlspecialchars($redirectUrl) ?>">
                <?php endif; ?>

                <?php if (!empty($errors) && isset($errors['general'])): ?>
                    <div class="error-message bg-red-500/10 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-semibold text-red-400 mb-1">Registration Error</h3>
                                <p class="text-sm text-red-300"><?= htmlspecialchars($errors['general']) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="success-message bg-green-500/10 border border-green-500/30 rounded-xl p-4 backdrop-blur-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-semibold text-green-400 mb-1">Success!</h3>
                                <p class="text-sm text-green-300"><?= htmlspecialchars($success) ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Desktop: Two-column layout, Mobile: Single column -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
                    <!-- Left Column: Basic Information -->
                    <div class="space-y-6">
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                </svg>
                                Personal Information
                            </h3>
                        </div>

                        <div class="input-group">
                            <label for="name" class="block text-sm font-semibold text-gray-200 mb-2">
                                Full Name
                            </label>
                            <div class="relative">
                                <input id="name" name="name" type="text" autocomplete="off" required
                                       value="<?= htmlspecialchars($data['name'] ?? '') ?>"
                                       class="appearance-none relative block w-full px-4 py-3 border <?= isset($errors['name']) ? 'border-red-500' : 'border-gray-600' ?> placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                       placeholder="Enter your full name">
                                <div class="input-border"></div>
                            </div>
                            <?php if (isset($errors['name'])): ?>
                                <p class="mt-2 text-sm text-red-400 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?= htmlspecialchars($errors['name']) ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <div class="input-group">
                            <label for="email" class="block text-sm font-semibold text-gray-200 mb-2">
                                Email Address
                            </label>
                            <div class="relative">
                                <input id="email" name="email" type="email" autocomplete="off" required
                                       value="<?= htmlspecialchars($data['email'] ?? '') ?>"
                                       class="appearance-none relative block w-full px-4 py-3 border <?= isset($errors['email']) ? 'border-red-500' : 'border-gray-600' ?> placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                       placeholder="Enter your email address">
                                <div class="input-border"></div>
                            </div>
                            <?php if (isset($errors['email'])): ?>
                                <p class="mt-2 text-sm text-red-400 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?= htmlspecialchars($errors['email']) ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <div class="input-group">
                            <label for="phone" class="block text-sm font-semibold text-gray-200 mb-2">
                                Phone Number <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <div class="relative">
                                <input id="phone" name="phone" type="tel" autocomplete="off"
                                       value="<?= htmlspecialchars($data['phone'] ?? '') ?>"
                                       class="appearance-none relative block w-full px-4 py-3 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                       placeholder="Enter your phone number">
                                <div class="input-border"></div>
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="referral_code" class="block text-sm font-semibold text-gray-200 mb-2">
                                Referral Code <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <div class="relative">
                                <input id="referral_code" name="referral_code" type="text" autocomplete="off"
                                       value="<?= htmlspecialchars($data['referral_code'] ?? '') ?>"
                                       class="appearance-none relative block w-full px-4 py-3 border border-gray-600 placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                       placeholder="Enter referral code">
                                <div class="input-border"></div>
                            </div>
                            <p class="mt-2 text-xs text-gray-400 flex items-center">
                                <svg class="w-4 h-4 mr-1 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                                </svg>
                                Get bonus points with a referral code!
                            </p>
                        </div>
                    </div>

                    <!-- Right Column: Security Information -->
                    <div class="space-y-6">
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                                </svg>
                                Security Settings
                            </h3>
                        </div>

                        <div class="input-group">
                            <label for="password" class="block text-sm font-semibold text-gray-200 mb-2">
                                Password
                            </label>
                            <div class="relative">
                                <input id="password" name="password" type="password" autocomplete="off" required
                                       class="appearance-none relative block w-full px-4 py-3 pr-12 border <?= isset($errors['password']) ? 'border-red-500' : 'border-gray-600' ?> placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                       placeholder="Create a secure password">
                                <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-salon-gold transition-colors" onclick="togglePassword('password')">
                                    <svg id="eye-icon-password" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                                <div class="input-border"></div>
                            </div>

                            <!-- Password Strength Indicator -->
                            <div id="password-strength-container" class="mt-3 hidden">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-xs font-medium text-gray-300">Password Strength:</span>
                                    <span id="strength-text" class="text-xs font-medium text-gray-400">Weak</span>
                                </div>
                                <div class="strength-bar">
                                    <div id="strength-fill" class="strength-fill"></div>
                                </div>
                            </div>

                            <?php if (isset($errors['password'])): ?>
                                <p class="mt-2 text-sm text-red-400 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?= htmlspecialchars($errors['password']) ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <div class="input-group">
                            <label for="confirm_password" class="block text-sm font-semibold text-gray-200 mb-2">
                                Confirm Password
                            </label>
                            <div class="relative">
                                <input id="confirm_password" name="confirm_password" type="password" autocomplete="off" required
                                       class="appearance-none relative block w-full px-4 py-3 pr-12 border <?= isset($errors['confirm_password']) ? 'border-red-500' : 'border-gray-600' ?> placeholder-gray-400 text-white bg-gray-800/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-salon-gold/50 focus:border-salon-gold transition-all duration-300 text-sm backdrop-blur-sm"
                                       placeholder="Confirm your password">
                                <button type="button" class="absolute inset-y-0 right-0 pr-4 flex items-center hover:text-salon-gold transition-colors" onclick="togglePassword('confirm_password')">
                                    <svg id="eye-icon-confirm_password" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                                <div class="input-border"></div>
                            </div>

                            <!-- Password Match Indicator -->
                            <div id="password-match" class="mt-2 hidden">
                                <div class="flex items-center text-sm">
                                    <svg id="match-icon" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    <span id="match-text">Passwords match</span>
                                </div>
                            </div>

                            <?php if (isset($errors['confirm_password'])): ?>
                                <p class="mt-2 text-sm text-red-400 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <?= htmlspecialchars($errors['confirm_password']) ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Password Requirements Panel - Full Width Below Form -->
                <div id="password-requirements" class="password-requirements rounded-xl p-4 mt-6 hidden">
                    <h4 class="text-sm font-semibold text-gray-200 mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-salon-gold" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 011-1 1 1 0 110 2 1 1 0 01-1-1zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                        Password Requirements
                    </h4>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3">
                        <div id="req-length" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>8+ characters</span>
                        </div>
                        <div id="req-lowercase" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Lowercase</span>
                        </div>
                        <div id="req-uppercase" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Uppercase</span>
                        </div>
                        <div id="req-number" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Number</span>
                        </div>
                        <div id="req-special" class="requirement-item requirement-unmet flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Special (!@#$%)</span>
                        </div>
                    </div>
                </div>



                <div class="flex items-start py-2">
                    <input id="terms" name="terms" type="checkbox" required
                           class="h-4 w-4 text-salon-gold focus:ring-salon-gold border-gray-600 bg-gray-800 rounded transition-colors mt-1">
                    <label for="terms" class="ml-3 block text-sm text-gray-300 select-none cursor-pointer">
                        I agree to the
                        <a href="<?= getBasePath() ?>/terms" class="text-salon-gold hover:text-gold-light transition-colors font-medium">Terms of Service</a>
                        and
                        <a href="<?= getBasePath() ?>/privacy" class="text-salon-gold hover:text-gold-light transition-colors font-medium">Privacy Policy</a>
                    </label>
                </div>

                <div class="pt-2">
                    <button id="submitBtn" type="submit" disabled
                            class="btn-primary group relative w-full flex justify-center py-3 px-6 border border-transparent text-sm font-semibold rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold focus:ring-offset-gray-800">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                            <svg class="h-5 w-5 text-black/80 group-hover:text-black transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        <span id="submitText">Create Your Account</span>
                        <svg id="loadingSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>

                <div class="text-center pt-6">
                    <p class="text-sm text-gray-300">
                        Already have an account?
                        <a href="<?= getBasePath() ?>/auth/login.php<?= !empty($redirectUrl) ? '?redirect=' . urlencode($redirectUrl) : '' ?>"
                           class="font-semibold text-salon-gold hover:text-gold-light transition-colors ml-1">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>

        </div>
    </div>
</div>

    <script>
        // Global validation state
        let validationState = {
            password: {
                length: false,
                lowercase: false,
                uppercase: false,
                number: false,
                special: false
            },
            passwordMatch: false,
            termsAccepted: false
        };

        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const eyeIcon = document.getElementById('eye-icon-' + fieldId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                `;
            }
        }

        function updateRequirement(elementId, isMet) {
            const element = document.getElementById(elementId);
            if (isMet) {
                element.classList.remove('requirement-unmet');
                element.classList.add('requirement-met');
                element.querySelector('svg').innerHTML = `
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                `;
            } else {
                element.classList.remove('requirement-met');
                element.classList.add('requirement-unmet');
                element.querySelector('svg').innerHTML = `
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm7-4a1 1 0 11-2 0 1 1 0 012 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                `;
            }
        }

        function updatePasswordStrength(strength) {
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');

            strengthFill.className = 'strength-fill';

            if (strength === 0) {
                strengthText.textContent = 'Very Weak';
                strengthText.className = 'text-xs font-medium text-red-400';
            } else if (strength === 1) {
                strengthFill.classList.add('strength-weak');
                strengthText.textContent = 'Weak';
                strengthText.className = 'text-xs font-medium text-red-400';
            } else if (strength === 2) {
                strengthFill.classList.add('strength-fair');
                strengthText.textContent = 'Fair';
                strengthText.className = 'text-xs font-medium text-orange-400';
            } else if (strength === 3) {
                strengthFill.classList.add('strength-good');
                strengthText.textContent = 'Good';
                strengthText.className = 'text-xs font-medium text-yellow-400';
            } else if (strength === 4) {
                strengthFill.classList.add('strength-strong');
                strengthText.textContent = 'Strong';
                strengthText.className = 'text-xs font-medium text-green-400';
            } else if (strength === 5) {
                strengthFill.classList.add('strength-excellent');
                strengthText.textContent = 'Excellent';
                strengthText.className = 'text-xs font-medium text-green-400';
            }
        }

        function validateForm() {
            const allPasswordRequirementsMet = Object.values(validationState.password).every(req => req);
            const formValid = allPasswordRequirementsMet && validationState.passwordMatch && validationState.termsAccepted;

            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = !formValid;

            if (formValid) {
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // Password field focus/blur behavior
        const passwordField = document.getElementById('password');
        const requirementsContainer = document.getElementById('password-requirements');
        const strengthContainer = document.getElementById('password-strength-container');

        // Helper functions for smooth show/hide
        function showRequirementsPanel() {
            requirementsContainer.classList.remove('hidden');
            strengthContainer.classList.remove('hidden');
            // Force reflow for smooth animation
            requirementsContainer.offsetHeight;
            strengthContainer.offsetHeight;
        }

        function hideRequirementsPanel() {
            requirementsContainer.classList.add('hidden');
            strengthContainer.classList.add('hidden');
        }

        // Show requirements panel on focus
        passwordField.addEventListener('focus', function() {
            const password = this.value;
            if (password.length > 0) {
                showRequirementsPanel();
            }
        });

        // Hide requirements panel on blur (with small delay to allow for quick refocus)
        passwordField.addEventListener('blur', function() {
            setTimeout(() => {
                // Only hide if the password field is not focused and user didn't click on requirements panel
                if (document.activeElement !== passwordField &&
                    !requirementsContainer.contains(document.activeElement)) {
                    hideRequirementsPanel();
                }
            }, 150); // Small delay to handle quick focus changes
        });

        // Prevent requirements panel from hiding when clicking on it
        requirementsContainer.addEventListener('mousedown', function(e) {
            e.preventDefault(); // Prevent focus loss from password field
        });

        // Keep requirements visible when clicking on the strength container
        strengthContainer.addEventListener('mousedown', function(e) {
            e.preventDefault(); // Prevent focus loss from password field
        });

        // Password validation
        passwordField.addEventListener('input', function() {
            const password = this.value;

            // Show/hide requirements based on content and focus
            if (password.length > 0 && document.activeElement === this) {
                showRequirementsPanel();
            } else if (password.length === 0) {
                hideRequirementsPanel();
            }

            // Check requirements
            validationState.password.length = password.length >= 8;
            validationState.password.lowercase = /[a-z]/.test(password);
            validationState.password.uppercase = /[A-Z]/.test(password);
            validationState.password.number = /[0-9]/.test(password);
            validationState.password.special = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

            // Update requirement indicators
            updateRequirement('req-length', validationState.password.length);
            updateRequirement('req-lowercase', validationState.password.lowercase);
            updateRequirement('req-uppercase', validationState.password.uppercase);
            updateRequirement('req-number', validationState.password.number);
            updateRequirement('req-special', validationState.password.special);

            // Calculate strength
            const strength = Object.values(validationState.password).filter(req => req).length;
            updatePasswordStrength(strength);

            // Revalidate password match
            const confirmPassword = document.getElementById('confirm_password').value;
            if (confirmPassword) {
                validatePasswordMatch();
            }

            validateForm();
        });

        function validatePasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchIndicator = document.getElementById('password-match');
            const matchIcon = document.getElementById('match-icon');
            const matchText = document.getElementById('match-text');
            const confirmInput = document.getElementById('confirm_password');

            if (confirmPassword.length > 0) {
                matchIndicator.classList.remove('hidden');

                if (password === confirmPassword) {
                    validationState.passwordMatch = true;
                    matchIndicator.className = 'mt-2 text-green-400';
                    matchIcon.innerHTML = `
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    `;
                    matchText.textContent = 'Passwords match';
                    confirmInput.classList.remove('border-red-500');
                    confirmInput.classList.add('border-gray-600');
                } else {
                    validationState.passwordMatch = false;
                    matchIndicator.className = 'mt-2 text-red-400';
                    matchIcon.innerHTML = `
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm7-4a1 1 0 11-2 0 1 1 0 012 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    `;
                    matchText.textContent = 'Passwords do not match';
                    confirmInput.classList.add('border-red-500');
                    confirmInput.classList.remove('border-gray-600');
                }
            } else {
                matchIndicator.classList.add('hidden');
                validationState.passwordMatch = false;
                confirmInput.classList.remove('border-red-500');
                confirmInput.classList.add('border-gray-600');
            }
        }

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', validatePasswordMatch);

        // Terms checkbox validation
        document.getElementById('terms').addEventListener('change', function() {
            validationState.termsAccepted = this.checked;
            validateForm();
        });

        // Form submission
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingSpinner = document.getElementById('loadingSpinner');

            if (submitBtn.disabled) {
                e.preventDefault();
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitText.textContent = 'Creating Account...';
            loadingSpinner.classList.remove('hidden');
        });

        // Initialize form validation on page load
        document.addEventListener('DOMContentLoaded', function() {
            validateForm();
        });
    </script>

<?php
// Include footer
include __DIR__ . '/../includes/footer.php';
?>
