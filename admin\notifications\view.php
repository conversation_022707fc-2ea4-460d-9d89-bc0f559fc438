<?php
/**
 * Individual Notification View
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Require admin authentication
$auth->requireRole('ADMIN');

$notificationId = isset($_GET['id']) ? $_GET['id'] : null;

if (!$notificationId) {
    header('Location: ' . getBasePath() . '/admin/notifications');
    exit;
}

// Get notification details
$notification = $database->fetch(
    "SELECT * FROM notifications WHERE id = ? AND user_id = ?",
    [$notificationId, $_SESSION['user_id']]
);

if (!$notification) {
    header('Location: ' . getBasePath() . '/admin/notifications');
    exit;
}

// Mark as read if not already read
if (!$notification['is_read']) {
    $database->query(
        "UPDATE notifications SET is_read = TRUE, updated_at = NOW() WHERE id = ?",
        [$notificationId]
    );
    $notification['is_read'] = true;
}

// Category configuration
$categoryConfig = [
    'BOOKING' => [
        'name' => 'Booking',
        'color' => 'blue',
        'icon' => 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    ],
    'CUSTOMER' => [
        'name' => 'Customer',
        'color' => 'green',
        'icon' => 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
    ],
    'STAFF' => [
        'name' => 'Staff',
        'color' => 'purple',
        'icon' => 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
    ],
    'PAYMENT' => [
        'name' => 'Payment',
        'color' => 'yellow',
        'icon' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
    ],
    'SYSTEM' => [
        'name' => 'System',
        'color' => 'red',
        'icon' => 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'
    ],
    'MARKETING' => [
        'name' => 'Marketing',
        'color' => 'pink',
        'icon' => 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'
    ],
    'FEEDBACK' => [
        'name' => 'Feedback',
        'color' => 'indigo',
        'icon' => 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
    ]
];

$config = isset($categoryConfig[$notification['category']]) ? $categoryConfig[$notification['category']] : $categoryConfig['SYSTEM'];

$priorityColors = [
    'URGENT' => 'bg-red-100 text-red-800',
    'HIGH' => 'bg-orange-100 text-orange-800',
    'MEDIUM' => 'bg-blue-100 text-blue-800',
    'LOW' => 'bg-gray-100 text-gray-800'
];
$priorityColor = isset($priorityColors[$notification['priority']]) ? $priorityColors[$notification['priority']] : $priorityColors['MEDIUM'];

$pageTitle = 'Notification Details';
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Notification Details</h1>
                                <p class="mt-1 text-sm text-gray-300">View notification information</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-2">
                                <a href="<?= getBasePath() ?>/admin/notifications" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Notifications
                                </a>
                                <button onclick="deleteNotification('<?= htmlspecialchars($notification['id']) ?>')" 
                                        class="bg-red-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                                    Delete
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Details -->
                    <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                        <div class="p-6">
                            <!-- Header Section -->
                            <div class="flex items-start space-x-4 mb-6">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 rounded-full bg-<?= $config['color'] ?>-100 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-<?= $config['color'] ?>-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $config['icon'] ?>" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h2 class="text-xl font-bold text-white"><?= htmlspecialchars($notification['title']) ?></h2>
                                    <div class="mt-2 flex items-center space-x-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $config['color'] ?>-100 text-<?= $config['color'] ?>-800">
                                            <?= $config['name'] ?>
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $priorityColor ?>">
                                            <?= ucfirst(strtolower($notification['priority'])) ?> Priority
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Read
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Message Section -->
                            <div class="bg-secondary-700 rounded-lg p-4 mb-6">
                                <h3 class="text-sm font-medium text-gray-300 mb-2">Message</h3>
                                <p class="text-white leading-relaxed"><?= nl2br(htmlspecialchars($notification['message'])) ?></p>
                            </div>

                            <!-- Details Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-300 mb-3">Notification Details</h3>
                                    <dl class="space-y-3">
                                        <div>
                                            <dt class="text-sm text-gray-400">Type</dt>
                                            <dd class="text-sm text-white font-medium"><?= htmlspecialchars($notification['type']) ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm text-gray-400">Category</dt>
                                            <dd class="text-sm text-white font-medium"><?= $config['name'] ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm text-gray-400">Priority</dt>
                                            <dd class="text-sm text-white font-medium"><?= ucfirst(strtolower($notification['priority'])) ?></dd>
                                        </div>
                                    </dl>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-300 mb-3">Timestamps</h3>
                                    <dl class="space-y-3">
                                        <div>
                                            <dt class="text-sm text-gray-400">Created</dt>
                                            <dd class="text-sm text-white font-medium"><?= date('M j, Y \a\t g:i A', strtotime($notification['created_at'])) ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm text-gray-400">Last Updated</dt>
                                            <dd class="text-sm text-white font-medium"><?= date('M j, Y \a\t g:i A', strtotime($notification['updated_at'])) ?></dd>
                                        </div>
                                        <?php if ($notification['expires_at']): ?>
                                            <div>
                                                <dt class="text-sm text-gray-400">Expires</dt>
                                                <dd class="text-sm text-white font-medium"><?= date('M j, Y \a\t g:i A', strtotime($notification['expires_at'])) ?></dd>
                                            </div>
                                        <?php endif; ?>
                                    </dl>
                                </div>
                            </div>

                            <!-- Metadata Section -->
                            <?php if ($notification['metadata']): ?>
                                <div class="bg-secondary-700 rounded-lg p-4 mb-6">
                                    <h3 class="text-sm font-medium text-gray-300 mb-2">Additional Information</h3>
                                    <pre class="text-sm text-gray-300 whitespace-pre-wrap"><?= htmlspecialchars($notification['metadata']) ?></pre>
                                </div>
                            <?php endif; ?>

                            <!-- Action URL Section -->
                            <?php if ($notification['action_url']): ?>
                                <div class="border-t border-secondary-600 pt-6">
                                    <h3 class="text-sm font-medium text-gray-300 mb-3">Related Action</h3>
                                    <a href="<?= htmlspecialchars($notification['action_url']) ?>" 
                                       class="inline-flex items-center px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors">
                                        <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                        View Related Item
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
const basePath = '<?= getBasePath() ?>';

function deleteNotification(notificationId) {
    if (confirm('Are you sure you want to delete this notification?')) {
        fetch(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = `${basePath}/admin/notifications`;
            } else {
                alert('Failed to delete notification');
            }
        })
        .catch(error => {
            console.error('Error deleting notification:', error);
            alert('Failed to delete notification');
        });
    }
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
