<?php
/**
 * Fix AJAX Reminder System
 * Comprehensive fix script for common issues
 */

require_once 'config/app.php';

echo "<h1>Fix AJAX Reminder System</h1>";

$fixes = [];
$errors = [];

// Fix 1: Ensure reminder tables exist
echo "<h2>1. Creating/Fixing Database Tables</h2>";
try {
    // Drop and recreate tables for clean setup
    $database->query("DROP TABLE IF EXISTS reminder_logs");
    $database->query("DROP TABLE IF EXISTS booking_reminders");
    
    // Create booking_reminders table
    $database->query("
        CREATE TABLE booking_reminders (
            id VARCHAR(36) PRIMARY KEY,
            booking_id VARCHAR(36) NOT NULL,
            reminder_type ENUM('24_HOURS', '30_MINUTES', 'AT_TIME') NOT NULL,
            priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
            status ENUM('PENDING', 'SENT', 'FAILED', 'SKIPPED') NOT NULL DEFAULT 'PENDING',
            scheduled_time DATETIME NOT NULL,
            sent_time DATETIME NULL,
            attempts INT DEFAULT 0,
            max_attempts INT DEFAULT 3,
            customer_email_sent BOOLEAN DEFAULT FALSE,
            staff_email_sent BOOLEAN DEFAULT FALSE,
            customer_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
            staff_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
            error_message TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_booking_id (booking_id),
            INDEX idx_status (status),
            INDEX idx_scheduled_time (scheduled_time),
            UNIQUE KEY unique_booking_reminder (booking_id, reminder_type)
        )
    ");
    
    // Create reminder_logs table
    $database->query("
        CREATE TABLE reminder_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            reminder_id VARCHAR(36) NOT NULL,
            booking_id VARCHAR(36) NOT NULL,
            action ENUM('CREATED', 'SENT', 'FAILED', 'RETRY', 'SKIPPED') NOT NULL,
            recipient_type ENUM('CUSTOMER', 'STAFF', 'BOTH') NOT NULL,
            recipient_email VARCHAR(255),
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    echo "✅ Database tables created successfully<br>";
    $fixes[] = "Database tables created";
    
} catch (Exception $e) {
    echo "❌ Database table creation failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Database table creation failed: " . $e->getMessage();
}

// Fix 2: Create test booking with reminders
echo "<h2>2. Creating Test Booking</h2>";
try {
    $testEmail = '<EMAIL>';
    $testName = 'Charles Martial';
    
    // Check if test user exists
    $user = $database->fetch("SELECT * FROM users WHERE email = ?", [$testEmail]);
    
    if (!$user) {
        $userId = generateUUID();
        $database->query(
            "INSERT INTO users (id, name, email, password, role, status) VALUES (?, ?, ?, ?, ?, ?)",
            [$userId, $testName, $testEmail, password_hash('testpassword123', PASSWORD_DEFAULT), 'CUSTOMER', 'ACTIVE']
        );
        echo "✅ Test user created<br>";
    } else {
        $userId = $user['id'];
        echo "✅ Test user already exists<br>";
    }
    
    // Get a service
    $service = $database->fetch("SELECT * FROM services WHERE status = 'ACTIVE' LIMIT 1");
    if (!$service) {
        echo "⚠️ No active services found, creating test service<br>";
        $serviceId = generateUUID();
        $database->query(
            "INSERT INTO services (id, name, description, duration, price, status) VALUES (?, ?, ?, ?, ?, ?)",
            [$serviceId, 'Test Service', 'Test service for reminders', 60, 50000, 'ACTIVE']
        );
        $service = ['id' => $serviceId, 'duration' => 60, 'price' => 50000];
    }
    
    // Create test booking for tomorrow
    $bookingId = generateUUID();
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $time = '14:00:00';
    
    $database->query(
        "INSERT INTO bookings (id, user_id, service_id, date, start_time, end_time, status, total_amount, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
        [
            $bookingId,
            $userId,
            $service['id'],
            $tomorrow,
            $time,
            date('H:i:s', strtotime($time) + ($service['duration'] * 60)),
            'CONFIRMED',
            $service['price']
        ]
    );
    
    echo "✅ Test booking created: $bookingId<br>";
    
    // Create test reminders
    $appointmentDateTime = $tomorrow . ' ' . $time;
    $appointmentTimestamp = strtotime($appointmentDateTime);
    
    $reminders = [
        ['type' => '24_HOURS', 'hours_before' => 24, 'priority' => 'MEDIUM'],
        ['type' => '30_MINUTES', 'hours_before' => 0.5, 'priority' => 'HIGH'],
        ['type' => 'AT_TIME', 'hours_before' => 0, 'priority' => 'URGENT']
    ];
    
    foreach ($reminders as $reminder) {
        $scheduledTime = date('Y-m-d H:i:s', $appointmentTimestamp - ($reminder['hours_before'] * 3600));
        
        // For testing, make 30_MINUTES reminder due now
        if ($reminder['type'] === '30_MINUTES') {
            $scheduledTime = date('Y-m-d H:i:s', time() - 60); // 1 minute ago
        }
        
        $reminderId = generateUUID();
        $database->query(
            "INSERT INTO booking_reminders (id, booking_id, reminder_type, priority, scheduled_time, max_attempts)
             VALUES (?, ?, ?, ?, ?, ?)",
            [$reminderId, $bookingId, $reminder['type'], $reminder['priority'], $scheduledTime, 3]
        );
    }
    
    echo "✅ Test reminders created<br>";
    $fixes[] = "Test booking and reminders created";
    
} catch (Exception $e) {
    echo "❌ Test booking creation failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Test booking creation failed: " . $e->getMessage();
}

// Fix 3: Test AJAX endpoints
echo "<h2>3. Testing AJAX Endpoints</h2>";

// Test main endpoint
try {
    $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/ajax_process_reminders.php';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode(['action' => 'test']),
            'timeout' => 10
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    
    if ($result !== false) {
        $data = json_decode($result, true);
        if ($data && isset($data['success'])) {
            echo "✅ Main AJAX endpoint working<br>";
            $fixes[] = "Main AJAX endpoint functional";
        } else {
            echo "⚠️ Main AJAX endpoint returns invalid response<br>";
        }
    } else {
        echo "❌ Main AJAX endpoint not accessible<br>";
    }
} catch (Exception $e) {
    echo "❌ Main AJAX endpoint test failed: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Test fallback endpoint
try {
    $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/ajax_process_reminders_simple.php';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode(['action' => 'test']),
            'timeout' => 10
        ]
    ]);
    
    $result = @file_get_contents($url, false, $context);
    
    if ($result !== false) {
        $data = json_decode($result, true);
        if ($data && isset($data['success'])) {
            echo "✅ Fallback AJAX endpoint working<br>";
            $fixes[] = "Fallback AJAX endpoint functional";
        } else {
            echo "⚠️ Fallback AJAX endpoint returns invalid response<br>";
        }
    } else {
        echo "❌ Fallback AJAX endpoint not accessible<br>";
    }
} catch (Exception $e) {
    echo "❌ Fallback AJAX endpoint test failed: " . htmlspecialchars($e->getMessage()) . "<br>";
}

// Fix 4: Test manual processing
echo "<h2>4. Testing Manual Processing</h2>";
try {
    // Simple manual processing
    $pendingReminders = $database->fetchAll(
        "SELECT * FROM booking_reminders WHERE status = 'PENDING' LIMIT 5"
    );
    
    echo "Found " . count($pendingReminders) . " pending reminders<br>";
    
    $processed = 0;
    $sent = 0;
    
    foreach ($pendingReminders as $reminder) {
        $processed++;
        
        // Simple email test
        if (function_exists('sendBookingReminderEmail')) {
            $result = sendBookingReminderEmail($reminder['booking_id'], $reminder['reminder_type']);
            if ($result) {
                $sent++;
                $database->query(
                    "UPDATE booking_reminders SET status = 'SENT', sent_time = NOW() WHERE id = ?",
                    [$reminder['id']]
                );
            }
        } else {
            // Mark as processed anyway for testing
            $database->query(
                "UPDATE booking_reminders SET status = 'SENT', sent_time = NOW() WHERE id = ?",
                [$reminder['id']]
            );
            $sent++;
        }
    }
    
    echo "✅ Manual processing completed: $processed processed, $sent sent<br>";
    $fixes[] = "Manual processing successful";
    
} catch (Exception $e) {
    echo "❌ Manual processing failed: " . htmlspecialchars($e->getMessage()) . "<br>";
    $errors[] = "Manual processing failed: " . $e->getMessage();
}

// Summary
echo "<h2>5. Summary</h2>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ System Fixed Successfully!</h3>";
    echo "<p>All fixes applied successfully. The AJAX reminder system should now be working.</p>";
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Some Issues Remain</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>6. Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Test AJAX System:</strong> <a href='test_ajax_reminders.php'>Open test interface</a></li>";
echo "<li><strong>Run Diagnostics:</strong> <a href='debug_ajax_reminders.php'>Run full diagnostics</a></li>";
echo "<li><strong>Check Browser Console:</strong> Open developer tools and look for JavaScript errors</li>";
echo "<li><strong>Monitor Email Delivery:</strong> Check <EMAIL> for test emails</li>";
echo "</ol>";

echo "<h2>7. Manual Test</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<p>You can now test the system manually:</p>";
echo "<ol>";
echo "<li>Open browser console (F12)</li>";
echo "<li>Run: <code>window.reminderProcessor.processReminders()</code></li>";
echo "<li>Check the response in console</li>";
echo "<li>Verify emails are <NAME_EMAIL></li>";
echo "</ol>";
echo "</div>";
?>
