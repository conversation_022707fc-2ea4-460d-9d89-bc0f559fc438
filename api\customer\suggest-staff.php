<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is logged in as customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    // Get request data
    $data = json_decode(file_get_contents('php://input'), true);
    $serviceId = $data['service_id'] ?? null;
    $packageId = $data['package_id'] ?? null;

    global $database;
    
    if ($serviceId) {
        // For single service
        $staff = $database->fetchAll("
            SELECT DISTINCT 
                u.*,
                ss.proficiency_level,
                GROUP_CONCAT(
                    CONCAT(s2.name, ' (', ss2.proficiency_level, ')')
                    SEPARATOR '|'
                ) as all_specialties,
                CASE 
                    WHEN ss.proficiency_level = 'EXPERT' THEN 1
                    WHEN ss.proficiency_level = 'ADVANCED' THEN 2
                    WHEN ss.proficiency_level = 'INTERMEDIATE' THEN 3
                    WHEN ss.proficiency_level = 'BEGINNER' THEN 4
                    ELSE 5
                END as ranking
            FROM users u
            LEFT JOIN staff_specialties ss ON u.id = ss.user_id AND ss.service_id = ?
            LEFT JOIN staff_specialties ss2 ON u.id = ss2.user_id
            LEFT JOIN services s2 ON ss2.service_id = s2.id
            WHERE u.role = 'STAFF'
            GROUP BY u.id
            ORDER BY 
                ranking ASC,
                u.name ASC
        ", [$serviceId]);

    } elseif ($packageId) {
        // For package, get all services in the package
        $packageServices = $database->fetchAll("
            SELECT service_id 
            FROM package_services 
            WHERE package_id = ?
        ", [$packageId]);

        $serviceIds = array_column($packageServices, 'service_id');
        
        if (empty($serviceIds)) {
            throw new Exception('No services found in the package');
        }

        // Get staff with specialties in any of the package services
        $placeholders = str_repeat('?,', count($serviceIds) - 1) . '?';
        $staff = $database->fetchAll("
            SELECT DISTINCT 
                u.*,
                COUNT(DISTINCT ss.service_id) as matched_services,
                AVG(
                    CASE ss.proficiency_level 
                        WHEN 'EXPERT' THEN 4
                        WHEN 'ADVANCED' THEN 3
                        WHEN 'INTERMEDIATE' THEN 2
                        WHEN 'BEGINNER' THEN 1
                        ELSE 0
                    END
                ) as avg_proficiency,
                GROUP_CONCAT(
                    CONCAT(s2.name, ' (', ss2.proficiency_level, ')')
                    SEPARATOR '|'
                ) as all_specialties,
                (COUNT(DISTINCT ss.service_id) * 100 + 
                    AVG(
                        CASE ss.proficiency_level 
                            WHEN 'EXPERT' THEN 4
                            WHEN 'ADVANCED' THEN 3
                            WHEN 'INTERMEDIATE' THEN 2
                            WHEN 'BEGINNER' THEN 1
                            ELSE 0
                        END
                    ) * 10
                ) as ranking
            FROM users u
            LEFT JOIN staff_specialties ss ON u.id = ss.user_id 
                AND ss.service_id IN ($placeholders)
            LEFT JOIN staff_specialties ss2 ON u.id = ss2.user_id
            LEFT JOIN services s2 ON ss2.service_id = s2.id
            WHERE u.role = 'STAFF'
            GROUP BY u.id
            ORDER BY 
                ranking DESC,
                u.name ASC
        ", $serviceIds);
    } else {
        throw new Exception('Either service_id or package_id is required');
    }

    // Process staff data and mark best fit
    if (!empty($staff)) {
        $bestRanking = $serviceId 
            ? min(array_column($staff, 'ranking'))  // For service (lower is better)
            : max(array_column($staff, 'ranking')); // For package (higher is better)

        foreach ($staff as &$member) {
            $member['is_best_fit'] = $member['ranking'] === $bestRanking;
            $member['specialties'] = !empty($member['all_specialties']) 
                ? explode('|', $member['all_specialties']) 
                : [];
            
            // Clean up response
            unset($member['all_specialties']);
            unset($member['ranking']);
            unset($member['password']);
            unset($member['email']);
        }
    }

    echo json_encode([
        'success' => true,
        'staff' => $staff
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 