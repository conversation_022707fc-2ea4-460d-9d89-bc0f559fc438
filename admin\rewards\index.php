<?php
/**
 * Admin Rewards Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/rewards_functions.php';

// Create customer_tiers table if it doesn't exist
try {
    $database->execute("
        CREATE TABLE IF NOT EXISTS customer_tiers (
            id VARCHAR(36) PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            min_spent INT NOT NULL DEFAULT 0,
            points_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.00,
            benefits TEXT,
            color VARCHAR(50) NOT NULL DEFAULT 'text-gray-600',
            bg_color VARCHAR(50) NOT NULL DEFAULT 'bg-gray-100',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // Check if table has any data, if not create default tiers
    $tierCountResult = $database->fetch("SELECT COUNT(*) as count FROM customer_tiers");
    $tierCount = $tierCountResult['count'];
    if ($tierCount == 0) {
        createDefaultCustomerTiers();
    }
} catch (Exception $e) {
    error_log("Error creating customer_tiers table: " . $e->getMessage());
}

// Check if user is admin
if (!isLoggedIn() || !hasRole('ADMIN')) {
    redirect('/admin/login');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_settings':
                    $settings = [
                        'pointsPerTZS' => floatval($_POST['points_per_tzs']),
                        'welcomeBonus' => intval($_POST['welcome_bonus']),
                        'referralBonus' => intval($_POST['referral_bonus']),
                        'birthdayBonus' => intval($_POST['birthday_bonus']),
                        'reviewBonus' => intval($_POST['review_bonus']),
                        'minimumRedemption' => intval($_POST['minimum_redemption'])
                    ];
                    
                    updateRewardSettings($settings);
                    $message = 'Reward settings updated successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'award_points':
                    $customerId = $_POST['customer_id'];
                    $points = intval($_POST['points']);
                    $description = $_POST['description'];
                    
                    awardPoints($customerId, $points, $description);
                    $message = 'Points awarded successfully!';
                    $messageType = 'success';
                    break;
                    
                case 'redeem_points':
                    $customerId = $_POST['customer_id'];
                    $points = intval($_POST['points']);
                    $description = $_POST['description'];

                    redeemPoints($customerId, $points, $description);
                    $message = 'Points redeemed successfully!';
                    $messageType = 'success';
                    break;

                case 'create_reward':
                    $rewardData = [
                        'name' => $_POST['reward_name'],
                        'description' => $_POST['reward_description'],
                        'points_required' => intval($_POST['points_cost']),
                        'value' => floatval($_POST['reward_value']),
                        'type' => $_POST['reward_type'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];

                    $rewardId = createReward($rewardData);
                    if ($rewardId) {
                        $message = 'Reward created successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to create reward');
                    }
                    break;

                case 'update_reward':
                    $rewardId = $_POST['reward_id'];
                    $rewardData = [
                        'name' => $_POST['reward_name'],
                        'description' => $_POST['reward_description'],
                        'points_required' => intval($_POST['points_cost']),
                        'value' => floatval($_POST['reward_value']),
                        'type' => $_POST['reward_type'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];

                    $result = updateReward($rewardId, $rewardData);
                    if ($result) {
                        $message = 'Reward updated successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to update reward');
                    }
                    break;

                case 'delete_reward':
                    $rewardId = $_POST['reward_id'];

                    $result = deleteReward($rewardId);
                    if ($result) {
                        $message = 'Reward deleted successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to delete reward');
                    }
                    break;

                case 'create_tier':
                    $tierData = [
                        'name' => $_POST['tier_name'],
                        'min_spent' => intval($_POST['min_spent']),
                        'points_multiplier' => floatval($_POST['points_multiplier']),
                        'benefits' => $_POST['benefits'],
                        'color' => $_POST['color'],
                        'bg_color' => $_POST['bg_color'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'sort_order' => intval($_POST['sort_order'])
                    ];

                    $tierId = createCustomerTier($tierData);
                    if ($tierId) {
                        $message = 'Customer tier created successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to create customer tier');
                    }
                    break;

                case 'update_tier':
                    $tierId = $_POST['tier_id'];
                    $tierData = [
                        'name' => $_POST['tier_name'],
                        'min_spent' => intval($_POST['min_spent']),
                        'points_multiplier' => floatval($_POST['points_multiplier']),
                        'benefits' => $_POST['benefits'],
                        'color' => $_POST['color'],
                        'bg_color' => $_POST['bg_color'],
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'sort_order' => intval($_POST['sort_order'])
                    ];

                    $result = updateCustomerTier($tierId, $tierData);
                    if ($result) {
                        $message = 'Customer tier updated successfully!';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to update customer tier');
                    }
                    break;

                case 'delete_tier':
                    $tierId = $_POST['tier_id'];

                    $result = deleteCustomerTier($tierId);
                    if ($result) {
                        $message = 'Customer tier deactivated successfully. The tier is now inactive and customers will be reassigned to appropriate tiers.';
                        $messageType = 'success';
                    } else {
                        throw new Exception('Failed to deactivate customer tier');
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get rewards data
$rewardsData = getRewardsData();

$pageTitle = "Points & Rewards";
include __DIR__ . '/../../includes/admin_header.php';
?>

<style>
/* Modern modal animations */
#deactivateTierModal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

#deactivateModalContent {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth hover effects */
.hover\:scale-105:hover {
    transform: scale(1.05);
}

/* Gradient button animation */
.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Enhanced shadow effects */
.shadow-2xl {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dark\:bg-secondary-800 {
        background-color: rgb(31 41 55);
    }
    .dark\:bg-secondary-700 {
        background-color: rgb(55 65 81);
    }
    .dark\:bg-secondary-600 {
        background-color: rgb(75 85 99);
    }
}
</style>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white">Points & Rewards</h1>
                    <p class="mt-2 text-gray-400">Manage loyalty program and customer rewards</p>
                </div>
                <div class="mt-4 sm:mt-0">
                    <button onclick="openSettingsModal()" class="bg-salon-gold hover:bg-yellow-500 text-black px-6 py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-cog mr-2"></i>Settings
                    </button>
                </div>
            </div>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-600/20 border border-green-500/30 text-green-300' : 'bg-red-600/20 border border-red-500/30 text-red-300' ?>">
                <div class="flex items-center">
                    <i class="<?= $messageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle' ?> mr-3"></i>
                    <div><?= htmlspecialchars($message) ?></div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-coins text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Points Issued</p>
                        <p class="text-2xl font-semibold text-white"><?= number_format($rewardsData['stats']['totalPointsIssued']) ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-gift text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Points Redeemed</p>
                        <p class="text-2xl font-semibold text-white"><?= number_format($rewardsData['stats']['totalPointsRedeemed']) ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Active Members</p>
                        <p class="text-2xl font-semibold text-white"><?= number_format($rewardsData['stats']['activeMembers']) ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Engagement Rate</p>
                        <p class="text-2xl font-semibold text-white"><?= $rewardsData['stats']['engagementRate'] ?>%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Customer Tiers -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Customer Tiers</h3>
                    <button onclick="openCreateTierModal()"
                            class="bg-salon-gold hover:bg-yellow-500 text-black px-4 py-2 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Tier
                    </button>
                </div>
                <div class="space-y-4">
                    <?php if (empty($rewardsData['tiers'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-crown text-4xl text-gray-600 mb-4"></i>
                            <p class="text-gray-400">No customer tiers available</p>
                            <button onclick="openCreateTierModal()"
                                    class="mt-4 bg-salon-gold hover:bg-yellow-500 text-black px-4 py-2 rounded-lg font-semibold transition-colors">
                                Create First Tier
                            </button>
                        </div>
                    <?php else: ?>
                        <?php foreach ($rewardsData['tiers'] as $tier): ?>
                            <div class="border border-secondary-600 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center gap-3">
                                        <div class="w-4 h-4 rounded-full <?= str_replace('text-', 'bg-', $tier['color']) ?>"></div>
                                        <h4 class="font-semibold text-white"><?= $tier['name'] ?></h4>
                                        <?php if (!$tier['isActive']): ?>
                                            <span class="px-2 py-1 bg-red-600/20 text-red-400 text-xs rounded">Inactive</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm text-gray-400"><?= $tier['pointsMultiplier'] ?>x points</span>
                                        <div class="flex items-center gap-2">
                                            <button onclick="editTier('<?= $tier['id'] ?>')"
                                                    class="bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 hover:text-blue-300 px-2 py-1 rounded transition-colors"
                                                    title="Edit Tier">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                            <button onclick="deleteTierConfirm('<?= $tier['id'] ?>', '<?= htmlspecialchars($tier['name']) ?>')"
                                                    class="bg-orange-600/20 hover:bg-orange-600/30 text-orange-400 hover:text-orange-300 px-2 py-1 rounded transition-colors"
                                                    title="Deactivate Tier">
                                                <i class="fas fa-pause mr-1"></i>Deactivate
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-400 mb-2">Minimum spent: TSH <?= number_format($tier['minSpent']) ?></p>
                                <div class="flex flex-wrap gap-1">
                                    <?php foreach ($tier['benefits'] as $benefit): ?>
                                        <span class="inline-block px-2 py-1 bg-secondary-700 text-xs rounded text-gray-300">
                                            <?= htmlspecialchars($benefit) ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Tier Distribution -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Tier Distribution</h3>
                <div class="space-y-4">
                    <?php foreach ($rewardsData['tierDistribution'] as $distribution): ?>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-4 h-4 rounded-full <?= str_replace('text-', 'bg-', $distribution['color']) ?>"></div>
                                <span class="text-white"><?= $distribution['name'] ?></span>
                            </div>
                            <div class="text-right">
                                <span class="text-white font-semibold"><?= $distribution['count'] ?></span>
                                <span class="text-sm text-gray-400 ml-1">customers</span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Rewards Catalog & Top Rewards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Rewards Management -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Reward Catalog</h3>
                    <button onclick="openCreateRewardModal()"
                            class="bg-salon-gold hover:bg-yellow-500 text-black px-4 py-2 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Reward
                    </button>
                </div>
                <div class="space-y-4">
                    <?php if (empty($rewardsData['redemptions'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-gift text-4xl text-gray-600 mb-4"></i>
                            <p class="text-gray-400">No rewards available</p>
                            <button onclick="openCreateRewardModal()"
                                    class="mt-4 bg-salon-gold hover:bg-yellow-500 text-black px-4 py-2 rounded-lg font-semibold transition-colors">
                                Create First Reward
                            </button>
                        </div>
                    <?php else: ?>
                        <?php foreach ($rewardsData['redemptions'] as $reward): ?>
                            <div class="border border-secondary-600 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center gap-3">
                                        <h4 class="font-medium text-white"><?= htmlspecialchars($reward['name']) ?></h4>
                                        <?php if (!$reward['isActive']): ?>
                                            <span class="px-2 py-1 bg-red-600/20 text-red-400 text-xs rounded">Inactive</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <span class="text-salon-gold font-semibold"><?= number_format($reward['pointsCost']) ?> pts</span>
                                        <div class="flex items-center gap-2">
                                            <button onclick="editReward('<?= $reward['id'] ?>')"
                                                    class="bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 hover:text-blue-300 px-2 py-1 rounded transition-colors"
                                                    title="Edit Reward">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                            <button onclick="deleteRewardConfirm('<?= $reward['id'] ?>', '<?= htmlspecialchars($reward['name']) ?>')"
                                                    class="bg-red-600/20 hover:bg-red-600/30 text-red-400 hover:text-red-300 px-2 py-1 rounded transition-colors"
                                                    title="Delete Reward">
                                                <i class="fas fa-trash mr-1"></i>Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-400 mb-2"><?= htmlspecialchars($reward['description']) ?></p>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-green-400">Value: TSH <?= number_format($reward['value']) ?></span>
                                    <div class="flex items-center gap-4">
                                        <span class="text-sm text-gray-400"><?= $reward['usageCount'] ?> redeemed</span>
                                        <span class="text-xs text-gray-500 capitalize"><?= $reward['type'] ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Recent Transactions</h3>
                <div class="space-y-4">
                    <?php if (empty($rewardsData['recentTransactions'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-coins text-4xl text-gray-600 mb-4"></i>
                            <p class="text-gray-400">No point transactions yet</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($rewardsData['recentTransactions'] as $transaction): ?>
                            <div class="flex items-center justify-between border-b border-secondary-700 pb-3">
                                <div>
                                    <p class="text-sm font-medium text-white"><?= htmlspecialchars($transaction['customerName']) ?></p>
                                    <p class="text-xs text-gray-400"><?= htmlspecialchars($transaction['description']) ?></p>
                                    <p class="text-xs text-gray-500"><?= date('M j, Y', strtotime($transaction['createdAt'])) ?></p>
                                </div>
                                <div class="text-right">
                                    <span class="font-semibold <?= $transaction['type'] === 'EARNED' ? 'text-green-400' : 'text-red-400' ?>">
                                        <?= $transaction['type'] === 'EARNED' ? '+' : '' ?><?= number_format($transaction['points']) ?>
                                    </span>
                                    <p class="text-xs text-gray-400"><?= $transaction['type'] ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-secondary-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="openAwardPointsModal()" 
                        class="flex items-center justify-center p-4 border-2 border-dashed border-secondary-600 rounded-lg hover:border-salon-gold transition-colors">
                    <div class="text-center">
                        <i class="fas fa-plus-circle text-2xl text-salon-gold mb-2"></i>
                        <p class="text-white font-medium">Award Points</p>
                        <p class="text-sm text-gray-400">Give points to customers</p>
                    </div>
                </button>
                
                <button onclick="openRedeemPointsModal()" 
                        class="flex items-center justify-center p-4 border-2 border-dashed border-secondary-600 rounded-lg hover:border-salon-gold transition-colors">
                    <div class="text-center">
                        <i class="fas fa-minus-circle text-2xl text-salon-gold mb-2"></i>
                        <p class="text-white font-medium">Redeem Points</p>
                        <p class="text-sm text-gray-400">Process point redemptions</p>
                    </div>
                </button>
            </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Settings Modal -->
<div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Reward Settings</h3>
                    <button onclick="closeModal('settingsModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="update_settings">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="points_per_tzs" class="block text-sm font-medium text-gray-300 mb-2">Points per TSH 1,000</label>
                            <input type="number" id="points_per_tzs" name="points_per_tzs"
                                   value="<?= $rewardsData['settings']['pointsPerTZS'] ?>" min="0" step="0.1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="minimum_redemption" class="block text-sm font-medium text-gray-300 mb-2">Minimum Redemption</label>
                            <input type="number" id="minimum_redemption" name="minimum_redemption"
                                   value="<?= $rewardsData['settings']['minimumRedemption'] ?>" min="1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="welcome_bonus" class="block text-sm font-medium text-gray-300 mb-2">Welcome Bonus</label>
                            <input type="number" id="welcome_bonus" name="welcome_bonus"
                                   value="<?= $rewardsData['settings']['welcomeBonus'] ?>" min="0" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="referral_bonus" class="block text-sm font-medium text-gray-300 mb-2">Referral Bonus</label>
                            <input type="number" id="referral_bonus" name="referral_bonus"
                                   value="<?= $rewardsData['settings']['referralBonus'] ?>" min="0" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="birthday_bonus" class="block text-sm font-medium text-gray-300 mb-2">Birthday Bonus</label>
                            <input type="number" id="birthday_bonus" name="birthday_bonus"
                                   value="<?= $rewardsData['settings']['birthdayBonus'] ?>" min="0" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="review_bonus" class="block text-sm font-medium text-gray-300 mb-2">Review Bonus</label>
                            <input type="number" id="review_bonus" name="review_bonus"
                                   value="<?= $rewardsData['settings']['reviewBonus'] ?>" min="0" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal('settingsModal')"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Award Points Modal -->
<div id="awardPointsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Award Points</h3>
                    <button onclick="closeModal('awardPointsModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="award_points">

                    <div>
                        <label for="customer_id_award" class="block text-sm font-medium text-gray-300 mb-2">Customer ID</label>
                        <input type="text" id="customer_id_award" name="customer_id" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="points_award" class="block text-sm font-medium text-gray-300 mb-2">Points to Award</label>
                        <input type="number" id="points_award" name="points" min="1" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="description_award" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                        <input type="text" id="description_award" name="description" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-4">
                        <button type="button" onclick="closeModal('awardPointsModal')"
                                class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            Award Points
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Redeem Points Modal -->
<div id="redeemPointsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Redeem Points</h3>
                    <button onclick="closeModal('redeemPointsModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" class="space-y-4">
                    <input type="hidden" name="action" value="redeem_points">

                    <div>
                        <label for="customer_id_redeem" class="block text-sm font-medium text-gray-300 mb-2">Customer ID</label>
                        <input type="text" id="customer_id_redeem" name="customer_id" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="points_redeem" class="block text-sm font-medium text-gray-300 mb-2">Points to Redeem</label>
                        <input type="number" id="points_redeem" name="points" min="1" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div>
                        <label for="description_redeem" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                        <input type="text" id="description_redeem" name="description" required
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-4">
                        <button type="button" onclick="closeModal('redeemPointsModal')"
                                class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-semibold transition-colors">
                            Redeem Points
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Reward Modal -->
<div id="rewardModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" onclick="closeModal('rewardModal')">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto" onclick="event.stopPropagation()">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="rewardModalTitle" class="text-xl font-semibold text-white">Create Reward</h3>
                    <button onclick="closeModal('rewardModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="rewardForm" method="POST" class="space-y-6">
                    <input type="hidden" id="rewardAction" name="action" value="create_reward">
                    <input type="hidden" id="rewardId" name="reward_id" value="">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="reward_name" class="block text-sm font-medium text-gray-300 mb-2">Reward Name *</label>
                            <input type="text" id="reward_name" name="reward_name" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div class="md:col-span-2">
                            <label for="reward_description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                            <textarea id="reward_description" name="reward_description" rows="3" required
                                      class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                        </div>

                        <div>
                            <label for="points_cost" class="block text-sm font-medium text-gray-300 mb-2">Points Cost *</label>
                            <input type="number" id="points_cost" name="points_cost" min="1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="reward_value" class="block text-sm font-medium text-gray-300 mb-2">Value (TSH) *</label>
                            <input type="number" id="reward_value" name="reward_value" min="0" step="1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="reward_type" class="block text-sm font-medium text-gray-300 mb-2">Type *</label>
                            <select id="reward_type" name="reward_type" required
                                    class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select Type</option>
                                <option value="discount">Discount</option>
                                <option value="service">Free Service</option>
                                <option value="product">Product</option>
                                <option value="bonus">Bonus/Upgrade</option>
                            </select>
                        </div>

                        <div class="flex items-center">
                            <label class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0 bg-secondary-700">
                                <span class="ml-2 text-sm text-gray-300">Active</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal('rewardModal')"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="rewardSubmitBtn"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            Create Reward
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Deactivate Tier Confirmation Modal -->
<div id="deactivateTierModal" class="fixed inset-0 bg-black/60 backdrop-blur-sm hidden z-50 flex items-center justify-center p-4" onclick="closeDeactivateModal(event)">
    <div class="bg-white dark:bg-secondary-800 rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-95 opacity-0" id="deactivateModalContent" onclick="event.stopPropagation()">
        <!-- Header -->
        <div class="p-6 pb-4">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                        <i class="fas fa-pause text-orange-600 dark:text-orange-400 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Deactivate Customer Tier</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">This action will temporarily disable the tier</p>
                    </div>
                </div>
                <button onclick="closeDeactivateModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-1">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="px-6 pb-6">
            <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-4 mb-6">
                <p class="text-gray-800 dark:text-gray-200 mb-3">
                    Are you sure you want to deactivate the <strong id="tierNameToDelete" class="text-orange-600 dark:text-orange-400"></strong> tier?
                </p>

                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-check-circle text-green-500 mt-0.5 text-xs"></i>
                        <span>Tier status will be set to inactive</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-check-circle text-green-500 mt-0.5 text-xs"></i>
                        <span>All existing customer data will be preserved</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-check-circle text-green-500 mt-0.5 text-xs"></i>
                        <span>Customers will be reassigned to appropriate tiers</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-undo text-blue-500 mt-0.5 text-xs"></i>
                        <span>This action can be reversed by reactivating the tier</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-3">
                <button onclick="closeDeactivateModal()"
                        class="px-6 py-2.5 bg-gray-100 hover:bg-gray-200 dark:bg-secondary-700 dark:hover:bg-secondary-600 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-all duration-200 hover:scale-105">
                    Cancel
                </button>
                <button onclick="confirmDeactivateTier()"
                        class="px-6 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-xl font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl">
                    <i class="fas fa-pause mr-2"></i>Deactivate Tier
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Customer Tier Modal -->
<div id="tierModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" onclick="closeModal('tierModal')">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto" onclick="event.stopPropagation()">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="tierModalTitle" class="text-xl font-semibold text-white">Create Customer Tier</h3>
                    <button onclick="closeModal('tierModal')" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form id="tierForm" method="POST" class="space-y-6">
                    <input type="hidden" id="tierAction" name="action" value="create_tier">
                    <input type="hidden" id="tierId" name="tier_id" value="">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="tier_name" class="block text-sm font-medium text-gray-300 mb-2">Tier Name *</label>
                            <input type="text" id="tier_name" name="tier_name" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="min_spent" class="block text-sm font-medium text-gray-300 mb-2">Minimum Spent (TSH) *</label>
                            <input type="number" id="min_spent" name="min_spent" min="0" step="1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="points_multiplier" class="block text-sm font-medium text-gray-300 mb-2">Points Multiplier *</label>
                            <input type="number" id="points_multiplier" name="points_multiplier" min="0.1" max="10" step="0.1" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order *</label>
                            <input type="number" id="sort_order" name="sort_order" min="0" required
                                   class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>

                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-300 mb-2">Text Color *</label>
                            <select id="color" name="color" required
                                    class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select Color</option>
                                <option value="text-orange-600">Orange</option>
                                <option value="text-gray-600">Gray</option>
                                <option value="text-yellow-600">Yellow</option>
                                <option value="text-purple-600">Purple</option>
                                <option value="text-blue-600">Blue</option>
                                <option value="text-green-600">Green</option>
                                <option value="text-red-600">Red</option>
                                <option value="text-pink-600">Pink</option>
                            </select>
                        </div>

                        <div>
                            <label for="bg_color" class="block text-sm font-medium text-gray-300 mb-2">Background Color *</label>
                            <select id="bg_color" name="bg_color" required
                                    class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select Background</option>
                                <option value="bg-orange-100">Orange</option>
                                <option value="bg-gray-100">Gray</option>
                                <option value="bg-yellow-100">Yellow</option>
                                <option value="bg-purple-100">Purple</option>
                                <option value="bg-blue-100">Blue</option>
                                <option value="bg-green-100">Green</option>
                                <option value="bg-red-100">Red</option>
                                <option value="bg-pink-100">Pink</option>
                            </select>
                        </div>

                        <div class="md:col-span-2">
                            <label for="benefits" class="block text-sm font-medium text-gray-300 mb-2">Benefits (comma-separated) *</label>
                            <textarea id="benefits" name="benefits" rows="3" required placeholder="Basic rewards,Birthday bonus,Priority booking"
                                      class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                        </div>

                        <div class="flex items-center">
                            <label class="flex items-center">
                                <input type="checkbox" id="tier_is_active" name="is_active" checked
                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0 bg-secondary-700">
                                <span class="ml-2 text-sm text-gray-300">Active</span>
                            </label>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeModal('tierModal')"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit" id="tierSubmitBtn"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            Create Tier
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function openSettingsModal() {
    document.getElementById('settingsModal').classList.remove('hidden');
}

function openAwardPointsModal() {
    document.getElementById('awardPointsModal').classList.remove('hidden');
}

function openRedeemPointsModal() {
    document.getElementById('redeemPointsModal').classList.remove('hidden');
}

function openCreateRewardModal() {
    // Reset form
    document.getElementById('rewardForm').reset();
    document.getElementById('rewardAction').value = 'create_reward';
    document.getElementById('rewardId').value = '';
    document.getElementById('rewardModalTitle').textContent = 'Create Reward';
    document.getElementById('rewardSubmitBtn').textContent = 'Create Reward';
    document.getElementById('is_active').checked = true;

    document.getElementById('rewardModal').classList.remove('hidden');
}

function editReward(rewardId) {
    // Fetch reward data and populate form
    fetch(`<?= getBasePath() ?>/api/admin/rewards.php?action=reward&id=${rewardId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.reward) {
                const reward = data.reward;

                // Populate form
                document.getElementById('rewardAction').value = 'update_reward';
                document.getElementById('rewardId').value = reward.id;
                document.getElementById('reward_name').value = reward.name;
                document.getElementById('reward_description').value = reward.description;
                document.getElementById('points_cost').value = reward.pointsCost;
                document.getElementById('reward_value').value = reward.value;
                document.getElementById('reward_type').value = reward.type;
                document.getElementById('is_active').checked = reward.isActive;

                // Update modal title and button
                document.getElementById('rewardModalTitle').textContent = 'Edit Reward';
                document.getElementById('rewardSubmitBtn').textContent = 'Update Reward';

                document.getElementById('rewardModal').classList.remove('hidden');
            } else {
                console.error('API Response:', data);
                alert('Failed to load reward details: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load reward details: ' + error.message);
        });
}

function deleteRewardConfirm(rewardId, rewardName) {
    const confirmMessage = `🗑️ Delete Reward\n\nAre you sure you want to delete "${rewardName}"?\n\n⚠️ WARNING: This action cannot be undone!\nAll associated data will be permanently removed.`;

    if (confirm(confirmMessage)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_reward">
            <input type="hidden" name="reward_id" value="${rewardId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Customer Tier Management Functions
function openCreateTierModal() {
    // Reset form
    document.getElementById('tierForm').reset();
    document.getElementById('tierAction').value = 'create_tier';
    document.getElementById('tierId').value = '';
    document.getElementById('tierModalTitle').textContent = 'Create Customer Tier';
    document.getElementById('tierSubmitBtn').textContent = 'Create Tier';
    document.getElementById('tier_is_active').checked = true;

    document.getElementById('tierModal').classList.remove('hidden');
}

function editTier(tierId) {
    // Fetch tier data and populate form
    fetch(`<?= getBasePath() ?>/api/admin/rewards.php?action=tier&id=${tierId}`, {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Get response text first to debug
            return response.text();
        })
        .then(text => {
            console.log('Raw response:', text);

            // Try to parse as JSON
            try {
                const data = JSON.parse(text);

                if (data.success && data.tier) {
                    const tier = data.tier;

                    // Populate form
                    document.getElementById('tierAction').value = 'update_tier';
                    document.getElementById('tierId').value = tier.id;
                    document.getElementById('tier_name').value = tier.name;
                    document.getElementById('min_spent').value = tier.minSpent;
                    document.getElementById('points_multiplier').value = tier.pointsMultiplier;
                    document.getElementById('benefits').value = tier.benefits.join(',');
                    document.getElementById('color').value = tier.color;
                    document.getElementById('bg_color').value = tier.bgColor;
                    document.getElementById('tier_is_active').checked = tier.isActive;
                    document.getElementById('sort_order').value = tier.sortOrder;

                    // Update modal title and button
                    document.getElementById('tierModalTitle').textContent = 'Edit Customer Tier';
                    document.getElementById('tierSubmitBtn').textContent = 'Update Tier';

                    document.getElementById('tierModal').classList.remove('hidden');
                } else {
                    console.error('API Response:', data);
                    alert('Failed to load tier details: ' + (data.error || 'Unknown error'));
                }
            } catch (parseError) {
                console.error('JSON Parse Error:', parseError);
                console.error('Response text:', text);

                // Check if it's an HTML error page
                if (text.includes('<html') || text.includes('<!DOCTYPE')) {
                    alert('Server returned an error page. Please check if you are logged in as admin.');
                } else {
                    alert('Failed to parse server response. Check console for details.');
                }
            }
        })
        .catch(error => {
            console.error('Fetch Error:', error);
            alert('Failed to load tier details: ' + error.message);
        });
}

// Modern deactivate tier confirmation
let tierToDeactivate = null;

function deleteTierConfirm(tierId, tierName) {
    // Store tier info for confirmation
    tierToDeactivate = { id: tierId, name: tierName };

    // Update modal content
    document.getElementById('tierNameToDelete').textContent = tierName;

    // Show modal with animation
    const modal = document.getElementById('deactivateTierModal');
    const modalContent = document.getElementById('deactivateModalContent');

    modal.classList.remove('hidden');

    // Trigger animation
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
}

function closeDeactivateModal(event) {
    // Only close if clicking outside modal content or close button
    if (event && event.target !== event.currentTarget && !event.target.closest('button[onclick="closeDeactivateModal()"]')) {
        return;
    }

    const modal = document.getElementById('deactivateTierModal');
    const modalContent = document.getElementById('deactivateModalContent');

    // Animate out
    modalContent.classList.remove('scale-100', 'opacity-100');
    modalContent.classList.add('scale-95', 'opacity-0');

    // Hide modal after animation
    setTimeout(() => {
        modal.classList.add('hidden');
        tierToDeactivate = null;
    }, 300);
}

function confirmDeactivateTier() {
    if (!tierToDeactivate) return;

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="delete_tier">
        <input type="hidden" name="tier_id" value="${tierToDeactivate.id}">
    `;
    document.body.appendChild(form);
    form.submit();
}

// Add form submission handler for better UX
document.addEventListener('DOMContentLoaded', function() {
    const rewardForm = document.getElementById('rewardForm');
    if (rewardForm) {
        rewardForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('rewardSubmitBtn');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';

            // Re-enable after a delay (form will submit)
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }, 3000);
        });
    }

    const tierForm = document.getElementById('tierForm');
    if (tierForm) {
        tierForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('tierSubmitBtn');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';

            // Re-enable after a delay (form will submit)
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }, 3000);
        });
    }
});

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Add escape key support for modals
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        // Close deactivate tier modal if open
        const deactivateModal = document.getElementById('deactivateTierModal');
        if (deactivateModal && !deactivateModal.classList.contains('hidden')) {
            closeDeactivateModal();
            return;
        }

        // Close tier modal if open
        const tierModal = document.getElementById('tierModal');
        if (tierModal && !tierModal.classList.contains('hidden')) {
            closeModal('tierModal');
            return;
        }

        // Close reward modal if open
        const rewardModal = document.getElementById('rewardModal');
        if (rewardModal && !rewardModal.classList.contains('hidden')) {
            closeModal('rewardModal');
            return;
        }

        // Close settings modal if open
        const settingsModal = document.getElementById('settingsModal');
        if (settingsModal && !settingsModal.classList.contains('hidden')) {
            closeModal('settingsModal');
            return;
        }

        // Close award points modal if open
        const awardModal = document.getElementById('awardPointsModal');
        if (awardModal && !awardModal.classList.contains('hidden')) {
            closeModal('awardPointsModal');
            return;
        }

        // Close redeem points modal if open
        const redeemModal = document.getElementById('redeemPointsModal');
        if (redeemModal && !redeemModal.classList.contains('hidden')) {
            closeModal('redeemPointsModal');
            return;
        }
    }
});

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed')) {
        closeModal(e.target.id);
    }
});

// Auto-refresh data every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
