<?php
/**
 * Fix Database AUTO_INCREMENT Conflicts
 * Specifically addresses the system_settings table AUTO_INCREMENT issue
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>🔧 Database AUTO_INCREMENT Fix</h1>";

try {
    echo "<h2>1. Analyzing Database Structure</h2>";
    
    // Check all tables for AUTO_INCREMENT vs UUID conflicts
    $tables = ['users', 'bookings', 'point_transactions', 'system_settings', 'email_logs', 'customer_messages'];
    $conflicts = [];
    
    foreach ($tables as $table) {
        try {
            $columns = $database->fetchAll("DESCRIBE $table");
            
            foreach ($columns as $column) {
                if ($column['Field'] === 'id') {
                    if (strpos($column['Extra'], 'auto_increment') !== false) {
                        $conflicts[] = $table;
                        echo "<p style='color: red;'>❌ $table.id has AUTO_INCREMENT: {$column['Type']} {$column['Extra']}</p>";
                    } elseif (strpos($column['Type'], 'varchar(36)') !== false) {
                        echo "<p style='color: green;'>✅ $table.id uses UUID: {$column['Type']}</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ $table.id: {$column['Type']} {$column['Extra']}</p>";
                    }
                    break;
                }
            }
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Table $table doesn't exist or error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    if (empty($conflicts)) {
        echo "<p style='color: green; font-size: 18px;'>🎉 No AUTO_INCREMENT conflicts found!</p>";
    } else {
        echo "<p style='color: red; font-size: 16px;'>Found AUTO_INCREMENT conflicts in: " . implode(', ', $conflicts) . "</p>";
    }
    
    echo "<h2>2. Fix AUTO_INCREMENT Conflicts</h2>";
    
    if (isset($_POST['fix_conflicts']) && !empty($conflicts)) {
        echo "<h3>Applying Fixes...</h3>";
        
        try {
            $database->beginTransaction();
            
            foreach ($conflicts as $table) {
                echo "<h4>Fixing table: $table</h4>";
                
                if ($table === 'system_settings') {
                    // Special handling for system_settings
                    echo "<p>Converting system_settings from AUTO_INCREMENT to UUID...</p>";
                    
                    // Check if table exists and has data
                    $existingData = $database->fetchAll("SELECT * FROM system_settings ORDER BY id");
                    
                    // Create new table structure
                    $database->execute("
                        CREATE TABLE system_settings_new (
                            id VARCHAR(36) PRIMARY KEY,
                            setting_key VARCHAR(100) NOT NULL,
                            setting_value TEXT DEFAULT NULL,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            UNIQUE KEY unique_setting_key (setting_key)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
                    ");
                    
                    // Migrate existing data with new UUIDs
                    foreach ($existingData as $row) {
                        $newId = generateUUID();
                        $database->execute(
                            "INSERT INTO system_settings_new (id, setting_key, setting_value, updated_at) VALUES (?, ?, ?, ?)",
                            [$newId, $row['setting_key'], $row['setting_value'], $row['updated_at']]
                        );
                        echo "<p>✓ Migrated setting: {$row['setting_key']}</p>";
                    }
                    
                    // Replace old table
                    $database->execute("DROP TABLE system_settings");
                    $database->execute("RENAME TABLE system_settings_new TO system_settings");
                    
                    echo "<p style='color: green;'>✅ Successfully converted system_settings to UUID</p>";
                    
                } else {
                    // Generic AUTO_INCREMENT to UUID conversion
                    echo "<p>Converting $table from AUTO_INCREMENT to UUID...</p>";
                    
                    // Get existing data
                    $existingData = $database->fetchAll("SELECT * FROM $table ORDER BY id");
                    
                    // Create backup table name
                    $backupTable = $table . '_backup_' . date('Ymd_His');
                    
                    // Rename current table to backup
                    $database->execute("RENAME TABLE $table TO $backupTable");
                    
                    // Get table structure and modify it
                    $createStatement = $database->fetch("SHOW CREATE TABLE $backupTable")['Create Table'];
                    
                    // Replace AUTO_INCREMENT with VARCHAR(36)
                    $newCreateStatement = str_replace(
                        ['`id` int(11) NOT NULL AUTO_INCREMENT', '`id` INT NOT NULL AUTO_INCREMENT'],
                        '`id` varchar(36) NOT NULL',
                        $createStatement
                    );
                    $newCreateStatement = str_replace($backupTable, $table, $newCreateStatement);
                    $newCreateStatement = preg_replace('/AUTO_INCREMENT=\d+\s*/', '', $newCreateStatement);
                    
                    // Create new table
                    $database->execute($newCreateStatement);
                    
                    // Migrate data with new UUIDs
                    foreach ($existingData as $row) {
                        $oldId = $row['id'];
                        $newId = generateUUID();
                        $row['id'] = $newId;
                        
                        // Build insert statement
                        $columns = array_keys($row);
                        $placeholders = array_fill(0, count($columns), '?');
                        $values = array_values($row);
                        
                        $insertSql = "INSERT INTO $table (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
                        $database->execute($insertSql, $values);
                        
                        echo "<p>✓ Migrated record: $oldId → $newId</p>";
                    }
                    
                    echo "<p style='color: green;'>✅ Successfully converted $table to UUID</p>";
                    echo "<p style='color: blue;'>ℹ️ Backup table created: $backupTable</p>";
                }
            }
            
            $database->commit();
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 All AUTO_INCREMENT conflicts fixed successfully!</p>";
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ Error fixing conflicts: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p style='color: blue;'>ℹ️ Transaction rolled back. No changes were made.</p>";
        }
    }
    
    echo "<h2>3. Verify Database Integrity</h2>";
    
    if (isset($_POST['verify_integrity'])) {
        echo "<h3>Running Integrity Checks...</h3>";
        
        try {
            // Test inserting records into each table
            $testResults = [];
            
            // Test system_settings
            try {
                $testId = generateUUID();
                $database->execute(
                    "INSERT INTO system_settings (id, setting_key, setting_value) VALUES (?, ?, ?)",
                    [$testId, 'test_setting_' . time(), 'test_value']
                );
                $database->execute("DELETE FROM system_settings WHERE id = ?", [$testId]);
                $testResults['system_settings'] = '✅ PASS';
            } catch (Exception $e) {
                $testResults['system_settings'] = '❌ FAIL: ' . $e->getMessage();
            }
            
            // Test point_transactions
            try {
                $testUser = $database->fetch("SELECT id FROM users WHERE role = 'CUSTOMER' LIMIT 1");
                if ($testUser) {
                    $testId = generateUUID();
                    $database->execute(
                        "INSERT INTO point_transactions (id, user_id, points, type, description) VALUES (?, ?, ?, ?, ?)",
                        [$testId, $testUser['id'], 10, 'BONUS', 'Test transaction']
                    );
                    $database->execute("DELETE FROM point_transactions WHERE id = ?", [$testId]);
                    $testResults['point_transactions'] = '✅ PASS';
                } else {
                    $testResults['point_transactions'] = '⚠️ SKIP: No test user found';
                }
            } catch (Exception $e) {
                $testResults['point_transactions'] = '❌ FAIL: ' . $e->getMessage();
            }
            
            // Display results
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Table</th><th>Insert Test Result</th></tr>";
            foreach ($testResults as $table => $result) {
                echo "<tr><td>$table</td><td>$result</td></tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Integrity check error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Critical error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🛠️ Action Forms</h2>";
?>

<?php if (!empty($conflicts)): ?>
<div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>⚠️ Fix AUTO_INCREMENT Conflicts</h3>
    <p><strong>Warning:</strong> This will convert AUTO_INCREMENT tables to use UUID. Backup your database first!</p>
    <p><strong>Tables to fix:</strong> <?= implode(', ', $conflicts) ?></p>
    <form method="post">
        <input type="hidden" name="fix_conflicts" value="1">
        <button type="submit" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Fix AUTO_INCREMENT Conflicts</button>
    </form>
</div>
<?php endif; ?>

<div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>✅ Verify Database Integrity</h3>
    <p>Test that all tables can accept new records without errors.</p>
    <form method="post">
        <input type="hidden" name="verify_integrity" value="1">
        <button type="submit" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Run Integrity Tests</button>
    </form>
</div>

<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>📋 Manual Verification Steps</h3>
    <ol>
        <li><strong>Test Customer Registration:</strong> <a href="<?= getBasePath() ?>/auth/register.php" target="_blank">Register new customer</a></li>
        <li><strong>Test Booking Creation:</strong> <a href="<?= getBasePath() ?>/customer/book.php" target="_blank">Create test booking</a></li>
        <li><strong>Test Point Transactions:</strong> Complete a booking to trigger point transactions</li>
        <li><strong>Test System Settings:</strong> Update any setting in admin panel</li>
    </ol>
</div>
