<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Get date range filters
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-t'); // Last day of current month

// Get staff data
$staffId = $_SESSION['user_id'];
$profile = getStaffProfile($staffId);
$monthlyStats = getMonthlyStats($staffId);
$performanceMetrics = getPerformanceMetrics($staffId);
$earnings = getStaffEarnings($staffId, $startDate, $endDate);

// Calculate totals for the period
$totalEarnings = array_sum(array_column($earnings, 'commission'));
$totalRevenue = array_sum(array_column($earnings, 'revenue'));
$totalAppointments = array_sum(array_column($earnings, 'completed'));

$pageTitle = "Earnings Tracking";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-white">Earnings Tracking</h1>
            <p class="mt-2 text-gray-400">Track your commission and performance metrics</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <div class="text-right">
                <p class="text-sm text-gray-400">Commission Rate</p>
                <p class="text-lg font-semibold text-salon-gold">60%</p>
            </div>
        </div>
    </div>
</div>

            <!-- Date Range Filter -->
            <div class="bg-secondary-800 rounded-lg p-6 mb-6">
                <form method="GET" class="flex flex-col sm:flex-row gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                        <input type="date" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>"
                               class="px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                        <input type="date" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>"
                               class="px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                    <div class="flex items-end">
                        <button type="button" onclick="setCurrentMonth()" class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            This Month
                        </button>
                    </div>
                </form>
            </div>

            <!-- Earnings Overview Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Period Commission -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-dollar-sign text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Period Commission</p>
                            <p class="text-2xl font-semibold text-white"><?= formatCurrency($totalEarnings) ?></p>
                        </div>
                    </div>
                </div>

                <!-- Period Revenue -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-chart-line text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Revenue Generated</p>
                            <p class="text-2xl font-semibold text-white"><?= formatCurrency($totalRevenue) ?></p>
                        </div>
                    </div>
                </div>

                <!-- Completed Services -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-check-circle text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Completed Services</p>
                            <p class="text-2xl font-semibold text-white"><?= $totalAppointments ?></p>
                        </div>
                    </div>
                </div>

                <!-- Average Per Service -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-calculator text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Avg Commission</p>
                            <p class="text-2xl font-semibold text-white"><?= $totalAppointments > 0 ? formatCurrency($totalEarnings / $totalAppointments) : formatCurrency(0) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Monthly Performance -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Monthly Performance</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Total Appointments</span>
                            <span class="text-white font-semibold"><?= $monthlyStats['totalAppointments'] ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Completed</span>
                            <span class="text-white font-semibold"><?= $monthlyStats['completedAppointments'] ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Cancelled</span>
                            <span class="text-white font-semibold"><?= $monthlyStats['cancelledAppointments'] ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">No Shows</span>
                            <span class="text-white font-semibold"><?= $monthlyStats['noShowAppointments'] ?></span>
                        </div>
                        <div class="flex items-center justify-between border-t border-secondary-700 pt-4">
                            <span class="text-gray-400">Completion Rate</span>
                            <span class="text-white font-semibold"><?= $monthlyStats['completionRate'] ?>%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Monthly Commission</span>
                            <span class="text-green-400 font-semibold"><?= formatCurrency($monthlyStats['commissionEarned']) ?></span>
                        </div>
                    </div>
                </div>

                <!-- Overall Performance -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Overall Performance</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Total Bookings</span>
                            <span class="text-white font-semibold"><?= $performanceMetrics['totalBookings'] ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Completed Bookings</span>
                            <span class="text-white font-semibold"><?= $performanceMetrics['completedBookings'] ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Overall Completion Rate</span>
                            <span class="text-white font-semibold"><?= $performanceMetrics['completionRate'] ?>%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Average Service Value</span>
                            <span class="text-white font-semibold"><?= formatCurrency($performanceMetrics['avgServiceValue']) ?></span>
                        </div>
                        <div class="flex items-center justify-between border-t border-secondary-700 pt-4">
                            <span class="text-gray-400">Total Revenue Generated</span>
                            <span class="text-white font-semibold"><?= formatCurrency($performanceMetrics['totalRevenue']) ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Monthly Growth</span>
                            <span class="font-semibold <?= $performanceMetrics['monthlyGrowth'] >= 0 ? 'text-green-400' : 'text-red-400' ?>">
                                <?= $performanceMetrics['monthlyGrowth'] >= 0 ? '+' : '' ?><?= number_format($performanceMetrics['monthlyGrowth'], 1) ?>%
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Earnings Breakdown -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Daily Earnings Breakdown</h3>
                
                <?php if (empty($earnings)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-chart-bar text-6xl text-gray-600 mb-4"></i>
                        <h4 class="text-xl font-semibold text-white mb-2">No Earnings Data</h4>
                        <p class="text-gray-400">No completed appointments found for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-secondary-700">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Appointments</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Completed</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Revenue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Commission</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-secondary-700">
                                <?php foreach ($earnings as $earning): ?>
                                    <tr class="hover:bg-secondary-700 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            <?= date('M j, Y', strtotime($earning['date'])) ?>
                                            <div class="text-xs text-gray-400"><?= date('l', strtotime($earning['date'])) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            <?= $earning['appointments'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            <?= $earning['completed'] ?>
                                            <?php if ($earning['appointments'] > 0): ?>
                                                <div class="text-xs text-gray-400">
                                                    <?= round(($earning['completed'] / $earning['appointments']) * 100, 1) ?>% rate
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            <?= formatCurrency($earning['revenue']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-400">
                                            <?= formatCurrency($earning['commission']) ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="bg-secondary-700">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-white">Total</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-white">
                                        <?= array_sum(array_column($earnings, 'appointments')) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-white">
                                        <?= $totalAppointments ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-white">
                                        <?= formatCurrency($totalRevenue) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-green-400">
                                        <?= formatCurrency($totalEarnings) ?>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
    <script>
        function setCurrentMonth() {
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

            document.getElementById('start_date').value = firstDay.toISOString().split('T')[0];
            document.getElementById('end_date').value = lastDay.toISOString().split('T')[0];
        }
    </script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
