<?php
/**
 * Flutterwave Webhook Handler
 * Handles real-time payment updates from Flutterwave
 */

header('Content-Type: application/json');

require_once __DIR__ . '/../../../config/app.php';

// Check if Flutterwave is enabled
if (!FLUTTERWAVE_ENABLED) {
    http_response_code(403);
    echo json_encode(['error' => 'Flutterwave webhooks are disabled']);
    exit;
}

try {
    // Get the payload
    $payload = @file_get_contents('php://input');
    $signature = $_SERVER['HTTP_VERIF_HASH'] ?? '';
    
    if (empty($payload)) {
        throw new Exception('Missing payload');
    }
    
    // Verify webhook signature
    $webhook_secret = FLUTTERWAVE_SECRET_KEY; // Use your secret key as webhook hash
    $expected_signature = hash('sha256', $webhook_secret);
    
    // For development, we'll skip signature verification
    // In production, uncomment the verification below
    /*
    if (!hash_equals($expected_signature, $signature)) {
        throw new Exception('Invalid signature');
    }
    */
    
    // Parse the event
    $event = json_decode($payload, true);
    
    if (!$event) {
        throw new Exception('Invalid JSON payload');
    }
    
    // Log webhook receipt
    global $database;
    $webhookId = generateUUID();
    
    $database->execute("
        INSERT INTO payment_webhooks (
            id, gateway, webhook_id, event_type, payload, 
            signature_verified, created_at
        ) VALUES (?, 'FLUTTERWAVE', ?, ?, ?, FALSE, NOW())
    ", [
        $webhookId,
        $event['id'] ?? 'unknown',
        $event['event'] ?? 'unknown',
        $payload
    ]);
    
    // Handle the event
    switch ($event['event']) {
        case 'charge.completed':
            handleChargeCompleted($event['data'], $webhookId);
            break;
            
        case 'charge.failed':
            handleChargeFailed($event['data'], $webhookId);
            break;
            
        default:
            // Log unhandled event
            error_log("Unhandled Flutterwave webhook event: " . ($event['event'] ?? 'unknown'));
            break;
    }
    
    // Mark webhook as processed
    $database->execute("
        UPDATE payment_webhooks 
        SET status = 'PROCESSED', processed_at = NOW()
        WHERE id = ?
    ", [$webhookId]);
    
    echo json_encode(['status' => 'success']);
    
} catch (Exception $e) {
    error_log("Flutterwave webhook error: " . $e->getMessage());
    
    if (isset($webhookId)) {
        $database->execute("
            UPDATE payment_webhooks 
            SET status = 'FAILED', error_message = ?, processed_at = NOW()
            WHERE id = ?
        ", [$e->getMessage(), $webhookId]);
    }
    
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleChargeCompleted($chargeData, $webhookId) {
    global $database;
    
    try {
        // Verify the transaction with Flutterwave API
        $txRef = $chargeData['tx_ref'] ?? '';
        $txId = $chargeData['id'] ?? '';
        
        if (empty($txRef)) {
            throw new Exception('Missing transaction reference');
        }
        
        // Find payment by transaction reference
        $payment = $database->fetch("
            SELECT p.*, b.user_id, b.id as booking_id
            FROM payments p
            INNER JOIN bookings b ON p.booking_id = b.id
            WHERE p.flutterwave_tx_ref = ? AND p.payment_gateway = 'FLUTTERWAVE'
        ", [$txRef]);
        
        if (!$payment) {
            throw new Exception('Payment not found for transaction reference: ' . $txRef);
        }
        
        // Verify transaction with Flutterwave API
        $verificationResult = verifyFlutterwaveTransaction($txId);
        
        if (!$verificationResult['success']) {
            throw new Exception('Transaction verification failed: ' . $verificationResult['error']);
        }
        
        $txData = $verificationResult['data'];
        
        // Verify transaction details
        if ($txData['status'] !== 'successful') {
            throw new Exception('Transaction was not successful: ' . $txData['status']);
        }
        
        if ($txData['amount'] != $payment['amount']) {
            throw new Exception('Payment amount mismatch');
        }
        
        // Update payment status if not already completed
        if ($payment['status'] !== 'COMPLETED') {
            updatePaymentStatus($payment['id'], 'COMPLETED', [
                'flutterwave_tx_ref' => $txData['tx_ref'],
                'flutterwave_tx_id' => $txData['id'],
                'flutterwave_status' => $txData['status']
            ]);
            
            logPaymentEvent($payment['id'], 'COMPLETED', 'FLUTTERWAVE', [
                'webhook_id' => $webhookId,
                'transaction_id' => $txData['id'],
                'tx_ref' => $txData['tx_ref'],
                'amount' => $txData['amount']
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Error handling charge completion: " . $e->getMessage());
        throw $e;
    }
}

function handleChargeFailed($chargeData, $webhookId) {
    global $database;
    
    try {
        $txRef = $chargeData['tx_ref'] ?? '';
        $txId = $chargeData['id'] ?? '';
        
        if (empty($txRef)) {
            throw new Exception('Missing transaction reference');
        }
        
        // Find payment by transaction reference
        $payment = $database->fetch("
            SELECT p.*, b.user_id, b.id as booking_id
            FROM payments p
            INNER JOIN bookings b ON p.booking_id = b.id
            WHERE p.flutterwave_tx_ref = ? AND p.payment_gateway = 'FLUTTERWAVE'
        ", [$txRef]);
        
        if (!$payment) {
            throw new Exception('Payment not found for transaction reference: ' . $txRef);
        }
        
        // Update payment status
        updatePaymentStatus($payment['id'], 'FAILED', [
            'flutterwave_tx_ref' => $txRef,
            'flutterwave_tx_id' => $txId,
            'flutterwave_status' => 'failed'
        ]);
        
        logPaymentEvent($payment['id'], 'FAILED', 'FLUTTERWAVE', [
            'webhook_id' => $webhookId,
            'transaction_id' => $txId,
            'tx_ref' => $txRef,
            'failure_reason' => $chargeData['processor_response'] ?? 'unknown'
        ]);
        
    } catch (Exception $e) {
        error_log("Error handling charge failure: " . $e->getMessage());
        throw $e;
    }
}

function verifyFlutterwaveTransaction($transactionId) {
    try {
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/{$transactionId}/verify",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . FLUTTERWAVE_SECRET_KEY,
                "Content-Type: application/json"
            ],
            // SSL options for development environment
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_USERAGENT => 'Flix Salon Payment System/1.0'
        ]);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $err = curl_error($curl);
        curl_close($curl);
        
        if ($err) {
            throw new Exception("cURL Error: " . $err);
        }
        
        if ($httpCode !== 200) {
            throw new Exception("Flutterwave API error: HTTP {$httpCode}");
        }
        
        $result = json_decode($response, true);
        
        if (!$result || $result['status'] !== 'success') {
            throw new Exception('Invalid response from Flutterwave API');
        }
        
        return [
            'success' => true,
            'data' => $result['data']
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
