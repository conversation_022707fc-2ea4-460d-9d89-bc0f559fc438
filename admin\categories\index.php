<?php
/**
 * Admin Categories Management
 * Flix Salonce - PHP Version
 * User-friendly category and subcategory management
 */

require_once __DIR__ . '/../../config/app.php';

// Check authentication and admin role
if (!isLoggedIn() || !hasRole('ADMIN')) {
    header('Location: ' . getBasePath() . '/auth/login.php');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_category':
                    $result = createServiceCategory($_POST);
                    if ($result['success']) {
                        $message = 'Main category created successfully!';
                        $messageType = 'success';
                    } else {
                        $message = $result['error'];
                        $messageType = 'error';
                    }
                    break;
                    
                case 'create_subcategory':
                    $result = createServiceSubcategory($_POST);
                    if ($result['success']) {
                        $message = 'Subcategory created successfully!';
                        $messageType = 'success';
                    } else {
                        $message = $result['error'];
                        $messageType = 'error';
                    }
                    break;
                    
                case 'update_category':
                    $result = updateServiceCategory($_POST['id'], $_POST);
                    if ($result['success']) {
                        $message = 'Category updated successfully!';
                        $messageType = 'success';
                    } else {
                        $message = $result['error'];
                        $messageType = 'error';
                    }
                    break;
                    
                case 'update_subcategory':
                    $result = updateServiceSubcategory($_POST['id'], $_POST);
                    if ($result['success']) {
                        $message = 'Subcategory updated successfully!';
                        $messageType = 'success';
                    } else {
                        $message = $result['error'];
                        $messageType = 'error';
                    }
                    break;
                    
                case 'delete_category':
                    $result = deleteServiceCategory($_POST['id']);
                    if ($result['success']) {
                        $message = 'Category deleted successfully!';
                        $messageType = 'success';
                    } else {
                        $message = $result['error'];
                        $messageType = 'error';
                    }
                    break;
                    
                case 'delete_subcategory':
                    $result = deleteServiceSubcategory($_POST['id']);
                    if ($result['success']) {
                        $message = 'Subcategory deleted successfully!';
                        $messageType = 'success';
                    } else {
                        $message = $result['error'];
                        $messageType = 'error';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get categories with subcategories
$categoriesWithSubs = getCategoriesWithSubcategories(false);
$allCategories = getAllServiceCategories(true);

include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-white">Categories Management</h1>
                            <p class="mt-1 text-sm text-gray-300">Manage service categories and subcategories</p>
                        </div>
                        <div class="mt-4 sm:mt-0 flex gap-3">
                            <button onclick="openCreateSubcategoryModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Subcategory
                            </button>
                            <button onclick="openCreateCategoryModal()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Category
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <!-- Categories Overview -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-salon-gold/20 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white">Main Categories</h3>
                                <p class="text-2xl font-bold text-salon-gold"><?= count($allCategories) ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white">Subcategories</h3>
                                <p class="text-2xl font-bold text-blue-400">
                                    <?php
                                    $totalSubs = 0;
                                    foreach ($categoriesWithSubs as $cat) {
                                        $totalSubs += count($cat['subcategories']);
                                    }
                                    echo $totalSubs;
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6 border border-secondary-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-white">Total Services</h3>
                                <p class="text-2xl font-bold text-green-400">
                                    <?php
                                    $totalServices = $database->fetch("SELECT COUNT(*) as count FROM services")['count'];
                                    echo $totalServices;
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Categories List -->
                <div class="bg-secondary-800 rounded-lg border border-secondary-700">
                    <div class="p-6 border-b border-secondary-700">
                        <h2 class="text-xl font-semibold text-white">Categories & Subcategories</h2>
                        <p class="text-gray-400 mt-1">Manage your service organization structure</p>
                    </div>
                    
                    <div class="p-6">
                        <?php if (empty($categoriesWithSubs)): ?>
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-folder-open text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-white mb-2">No Categories Yet</h3>
                                <p class="text-gray-400 mb-6">Start by creating your first main category</p>
                                <button onclick="openCreateCategoryModal()" 
                                        class="bg-salon-gold text-black px-6 py-3 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Create First Category
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="space-y-6">
                                <?php foreach ($categoriesWithSubs as $category): ?>
                                    <div class="bg-secondary-700 rounded-lg border border-secondary-600">
                                        <!-- Main Category Header -->
                                        <div class="p-4 border-b border-secondary-600">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="w-10 h-10 bg-salon-gold/20 rounded-lg flex items-center justify-center mr-3">
                                                        <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <h3 class="text-lg font-semibold text-white"><?= htmlspecialchars($category['name']) ?></h3>
                                                        <?php if ($category['description']): ?>
                                                            <p class="text-sm text-gray-400"><?= htmlspecialchars($category['description']) ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $category['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                        <?= $category['is_active'] ? 'Active' : 'Inactive' ?>
                                                    </span>
                                                    <button onclick="editCategory('<?= $category['id'] ?>', '<?= htmlspecialchars($category['name']) ?>', '<?= htmlspecialchars($category['description']) ?>', <?= $category['is_active'] ? 'true' : 'false' ?>)"
                                                            class="text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-blue-500/10 transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                    </button>
                                                    <button onclick="addSubcategory('<?= $category['id'] ?>', '<?= htmlspecialchars($category['name']) ?>')"
                                                            class="text-green-400 hover:text-green-300 p-2 rounded-lg hover:bg-green-500/10 transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                                        </svg>
                                                    </button>
                                                    <button onclick="deleteCategory('<?= $category['id'] ?>', '<?= htmlspecialchars($category['name']) ?>')"
                                                            class="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-500/10 transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Subcategories -->
                                        <?php if (!empty($category['subcategories'])): ?>
                                            <div class="p-4">
                                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                    <?php foreach ($category['subcategories'] as $subcategory): ?>
                                                        <div class="bg-secondary-600 rounded-lg p-3 border border-secondary-500">
                                                            <div class="flex items-center justify-between">
                                                                <div class="flex items-center">
                                                                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-2">
                                                                        <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                                        </svg>
                                                                    </div>
                                                                    <div>
                                                                        <h4 class="text-sm font-medium text-white"><?= htmlspecialchars($subcategory['name']) ?></h4>
                                                                        <?php if ($subcategory['description']): ?>
                                                                            <p class="text-xs text-gray-400 truncate"><?= htmlspecialchars($subcategory['description']) ?></p>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center gap-1">
                                                                    <button onclick="editSubcategory('<?= $subcategory['id'] ?>', '<?= htmlspecialchars($subcategory['name']) ?>', '<?= htmlspecialchars($subcategory['description']) ?>', '<?= $subcategory['category_id'] ?>', <?= $subcategory['is_active'] ? 'true' : 'false' ?>)"
                                                                            class="text-blue-400 hover:text-blue-300 p-1 rounded hover:bg-blue-500/10 transition-colors">
                                                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                                        </svg>
                                                                    </button>
                                                                    <button onclick="deleteSubcategory('<?= $subcategory['id'] ?>', '<?= htmlspecialchars($subcategory['name']) ?>')"
                                                                            class="text-red-400 hover:text-red-300 p-1 rounded hover:bg-red-500/10 transition-colors">
                                                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                        </svg>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="p-4 text-center">
                                                <p class="text-gray-400 text-sm">No subcategories yet. 
                                                    <button onclick="addSubcategory('<?= $category['id'] ?>', '<?= htmlspecialchars($category['name']) ?>')" 
                                                            class="text-blue-400 hover:text-blue-300 underline">
                                                        Add the first one
                                                    </button>
                                                </p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit Category Modal -->
<div id="categoryModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 id="categoryModalTitle" class="text-xl font-bold text-white">Add Main Category</h2>
            <button onclick="closeCategoryModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="categoryForm" method="POST">
            <input type="hidden" name="action" id="categoryAction" value="create_category">
            <input type="hidden" name="id" id="categoryId">

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Category Name *</label>
                <input type="text" name="name" id="categoryName" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea name="description" id="categoryDescription" rows="3"
                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
            </div>

            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" id="categoryActive" value="1" checked
                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                    <span class="ml-2 text-sm text-gray-300">Category is active</span>
                </label>
            </div>

            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Save Category
                </button>
                <button type="button" onclick="closeCategoryModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Create/Edit Subcategory Modal -->
<div id="subcategoryModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 id="subcategoryModalTitle" class="text-xl font-bold text-white">Add Subcategory</h2>
            <button onclick="closeSubcategoryModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="subcategoryForm" method="POST">
            <input type="hidden" name="action" id="subcategoryAction" value="create_subcategory">
            <input type="hidden" name="id" id="subcategoryId">

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Main Category *</label>
                <select name="category_id" id="subcategoryCategory" required
                        class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <option value="">Select Main Category</option>
                    <?php foreach ($allCategories as $cat): ?>
                        <option value="<?= htmlspecialchars($cat['id']) ?>">
                            <?= htmlspecialchars($cat['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Subcategory Name *</label>
                <input type="text" name="name" id="subcategoryName" required
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea name="description" id="subcategoryDescription" rows="3"
                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
            </div>

            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_active" id="subcategoryActive" value="1" checked
                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                    <span class="ml-2 text-sm text-gray-300">Subcategory is active</span>
                </label>
            </div>

            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Save Subcategory
                </button>
                <button type="button" onclick="closeSubcategoryModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4 border border-red-600">
        <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Confirm Deletion</h3>
                <p class="text-gray-400 text-sm">This action cannot be undone</p>
            </div>
        </div>

        <div id="deleteMessage" class="text-gray-300 mb-6"></div>

        <form id="deleteForm" method="POST">
            <input type="hidden" name="action" id="deleteAction">
            <input type="hidden" name="id" id="deleteId">

            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                    Delete
                </button>
                <button type="button" onclick="closeDeleteModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Modal functions
function openCreateCategoryModal() {
    document.getElementById('categoryModalTitle').textContent = 'Add Main Category';
    document.getElementById('categoryAction').value = 'create_category';
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryActive').checked = true;
    document.getElementById('categoryModal').classList.remove('hidden');
}

function openCreateSubcategoryModal() {
    document.getElementById('subcategoryModalTitle').textContent = 'Add Subcategory';
    document.getElementById('subcategoryAction').value = 'create_subcategory';
    document.getElementById('subcategoryForm').reset();
    document.getElementById('subcategoryActive').checked = true;
    document.getElementById('subcategoryModal').classList.remove('hidden');
}

function editCategory(id, name, description, isActive) {
    document.getElementById('categoryModalTitle').textContent = 'Edit Category';
    document.getElementById('categoryAction').value = 'update_category';
    document.getElementById('categoryId').value = id;
    document.getElementById('categoryName').value = name;
    document.getElementById('categoryDescription').value = description;
    document.getElementById('categoryActive').checked = isActive;
    document.getElementById('categoryModal').classList.remove('hidden');
}

function editSubcategory(id, name, description, categoryId, isActive) {
    document.getElementById('subcategoryModalTitle').textContent = 'Edit Subcategory';
    document.getElementById('subcategoryAction').value = 'update_subcategory';
    document.getElementById('subcategoryId').value = id;
    document.getElementById('subcategoryName').value = name;
    document.getElementById('subcategoryDescription').value = description;
    document.getElementById('subcategoryCategory').value = categoryId;
    document.getElementById('subcategoryActive').checked = isActive;
    document.getElementById('subcategoryModal').classList.remove('hidden');
}

function addSubcategory(categoryId, categoryName) {
    document.getElementById('subcategoryModalTitle').textContent = `Add Subcategory to ${categoryName}`;
    document.getElementById('subcategoryAction').value = 'create_subcategory';
    document.getElementById('subcategoryForm').reset();
    document.getElementById('subcategoryCategory').value = categoryId;
    document.getElementById('subcategoryActive').checked = true;
    document.getElementById('subcategoryModal').classList.remove('hidden');
}

function deleteCategory(id, name) {
    document.getElementById('deleteMessage').textContent = `Are you sure you want to delete the category "${name}"? This will also delete all its subcategories.`;
    document.getElementById('deleteAction').value = 'delete_category';
    document.getElementById('deleteId').value = id;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function deleteSubcategory(id, name) {
    document.getElementById('deleteMessage').textContent = `Are you sure you want to delete the subcategory "${name}"?`;
    document.getElementById('deleteAction').value = 'delete_subcategory';
    document.getElementById('deleteId').value = id;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeCategoryModal() {
    document.getElementById('categoryModal').classList.add('hidden');
}

function closeSubcategoryModal() {
    document.getElementById('subcategoryModal').classList.add('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modals on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeCategoryModal();
        closeSubcategoryModal();
        closeDeleteModal();
    }
});

// Close modals on backdrop click
document.getElementById('categoryModal').addEventListener('click', function(e) {
    if (e.target === this) closeCategoryModal();
});

document.getElementById('subcategoryModal').addEventListener('click', function(e) {
    if (e.target === this) closeSubcategoryModal();
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) closeDeleteModal();
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
