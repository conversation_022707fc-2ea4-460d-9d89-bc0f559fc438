<?php
/**
 * OTP Verification Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/forgot_password_functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'ADMIN') {
        redirect('/admin');
    } elseif ($user['role'] === 'STAFF') {
        redirect('/staff');
    } else {
        redirect('/customer');
    }
}

// Check if user has valid session for password reset
if (!isset($_SESSION['reset_email']) || !isset($_SESSION['reset_step']) || $_SESSION['reset_step'] !== 'verify_otp') {
    redirect('/auth/forgot-password.php');
}

$error = '';
$success = '';
$email = $_SESSION['reset_email'];

// Get remaining time for OTP
$timeRemaining = getOTPTimeRemaining($email);
if ($timeRemaining <= 0) {
    unset($_SESSION['reset_email'], $_SESSION['reset_step']);
    redirect('/auth/forgot-password.php?error=expired');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF Protection
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error = 'Invalid request. Please try again.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'verify') {
            $otp = sanitize($_POST['otp'] ?? '');

            if (empty($otp)) {
                $error = 'Please enter the verification code';
            } elseif (!preg_match('/^\d{6}$/', $otp)) {
                $error = 'Please enter a valid 6-digit code';
            } else {
                // Verify OTP
                $result = verifyOTP($email, $otp);
                
                if ($result['success']) {
                    // Store user ID for password reset
                    $_SESSION['reset_user_id'] = $result['user_id'];
                    $_SESSION['reset_step'] = 'reset_password';
                    
                    // Redirect to password reset page
                    redirect('/auth/reset-password.php');
                } else {
                    $error = $result['error'];
                }
            }
        } elseif ($action === 'resend') {
            // Resend OTP
            $result = sendPasswordResetOTP($email);
            
            if ($result['success']) {
                $success = 'New verification code sent to your email';
            } else {
                $error = $result['error'];
            }
        }
    }
}

$pageTitle = "Verify Code";
$pageDescription = "Enter the verification code sent to your email";

// Include header
include __DIR__ . '/../includes/header.php';
?>
<!-- Enhanced Auth Page Styles - Unified Design -->
<style>
    /* Auth page background with gradient overlay */
    .auth-main {
        min-height: calc(100vh - 140px);
        background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #141414 100%);
        background-attachment: fixed;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
    }

    .auth-main::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
        pointer-events: none;
    }

    /* Glass container with enhanced effects */
    .auth-container {
        background: rgba(10, 10, 10, 0.85);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(245, 158, 11, 0.2);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.8),
            0 0 0 1px rgba(245, 158, 11, 0.1),
            inset 0 1px 0 rgba(245, 158, 11, 0.1);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.5), transparent);
    }

    /* Enhanced OTP input styling */
    .otp-input {
        width: 3.5rem;
        height: 3.5rem;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        border: 2px solid rgba(75, 85, 99, 0.5);
        background: rgba(20, 20, 20, 0.8);
        color: white;
        border-radius: 1rem;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .otp-input:focus {
        outline: none;
        border-color: rgba(245, 158, 11, 0.8);
        box-shadow:
            0 0 0 3px rgba(245, 158, 11, 0.2),
            0 8px 25px rgba(245, 158, 11, 0.15);
        background: rgba(20, 20, 20, 0.95);
        transform: translateY(-2px) scale(1.05);
    }

    .otp-input.filled {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.15);
        box-shadow:
            0 0 0 2px rgba(16, 185, 129, 0.3),
            0 8px 25px rgba(16, 185, 129, 0.2);
        transform: scale(1.02);
    }

    /* Enhanced button styling */
    .btn-primary {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #f59e0b 100%);
        background-size: 200% 200%;
        border: 1px solid rgba(245, 158, 11, 0.3);
        box-shadow:
            0 4px 15px rgba(245, 158, 11, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #fbbf24 100%);
        background-position: 100% 0;
        transform: translateY(-2px);
        box-shadow:
            0 8px 25px rgba(245, 158, 11, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(245, 158, 11, 0.6);
    }

    .btn-primary:hover::before {
        left: 100%;
    }

    .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }

    /* Enhanced secondary button */
    .btn-secondary {
        background: rgba(20, 20, 20, 0.8);
        border: 1px solid rgba(75, 85, 99, 0.5);
        color: #d1d5db;
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .btn-secondary:hover {
        background: rgba(75, 85, 99, 0.8);
        border-color: rgba(156, 163, 175, 0.8);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 8px 20px rgba(75, 85, 99, 0.3);
    }

    /* Enhanced animations */
    .error-message, .success-message {
        animation: slideInEnhanced 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideInEnhanced {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Enhanced timer circle */
    .timer-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(#f59e0b 0deg, rgba(55, 65, 81, 0.8) 0deg);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow:
            0 8px 25px rgba(245, 158, 11, 0.3),
            inset 0 2px 4px rgba(245, 158, 11, 0.1);
        border: 2px solid rgba(245, 158, 11, 0.3);
        backdrop-filter: blur(10px);
    }

    .timer-circle::before {
        content: '';
        width: 65px;
        height: 65px;
        border-radius: 50%;
        background: rgba(10, 10, 10, 0.95);
        position: absolute;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .timer-text {
        position: relative;
        z-index: 1;
        font-weight: bold;
        color: #f59e0b;
        font-size: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    /* Link enhancements */
    a {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    a:hover {
        text-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
    }

    /* Icon container enhancement */
    .auth-container .mx-auto.h-16.w-16 {
        background: rgba(245, 158, 11, 0.15);
        border: 1px solid rgba(245, 158, 11, 0.3);
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
    }

    /* Disabled button state */
    .btn-primary:disabled {
        background: rgba(107, 114, 128, 0.5);
        border-color: rgba(107, 114, 128, 0.3);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        backdrop-filter: blur(10px);
    }

    .btn-primary:disabled::before {
        display: none;
    }
</style>

<!-- Auth Page Content -->
<div class="auth-main">
    <div class="max-w-md w-full space-y-8">
        <div class="auth-container auth-card rounded-2xl p-8 sm:p-10">
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 bg-salon-gold/20 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
                <h2 class="text-2xl sm:text-3xl font-bold text-white mb-2">Check Your Email</h2>
                <p class="text-gray-300 text-sm sm:text-base mb-2">
                    We sent a 6-digit verification code to
                </p>
                <p class="text-salon-gold font-semibold"><?= htmlspecialchars($email) ?></p>
            </div>

            <?php if ($error): ?>
                <div class="error-message bg-red-500/10 border border-red-500/30 rounded-xl p-4 backdrop-blur-sm mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-400 mb-1">Error</h3>
                            <p class="text-sm text-red-300"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="success-message bg-green-500/10 border border-green-500/30 rounded-xl p-4 backdrop-blur-sm mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-400 mb-1">Success</h3>
                            <p class="text-sm text-green-300"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Timer Display -->
            <div class="text-center mb-6">
                <div class="timer-circle mx-auto mb-3">
                    <div class="timer-text" id="timer-display">
                        <span id="timer-minutes">00</span>:<span id="timer-seconds">00</span>
                    </div>
                </div>
                <p class="text-sm text-gray-400">Code expires in</p>
            </div>

            <form class="space-y-6" method="POST" autocomplete="off">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                <input type="hidden" name="action" value="verify">

                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-semibold text-gray-200 mb-4 text-center">
                            Enter Verification Code
                        </label>
                        <div class="flex justify-center space-x-3">
                            <input type="text" maxlength="1" class="otp-input" id="otp1" data-index="0" autocomplete="off">
                            <input type="text" maxlength="1" class="otp-input" id="otp2" data-index="1" autocomplete="off">
                            <input type="text" maxlength="1" class="otp-input" id="otp3" data-index="2" autocomplete="off">
                            <input type="text" maxlength="1" class="otp-input" id="otp4" data-index="3" autocomplete="off">
                            <input type="text" maxlength="1" class="otp-input" id="otp5" data-index="4" autocomplete="off">
                            <input type="text" maxlength="1" class="otp-input" id="otp6" data-index="5" autocomplete="off">
                        </div>
                        <input type="hidden" name="otp" id="otp-hidden">
                    </div>
                </div>

                <div class="pt-2 space-y-3">
                    <button type="submit" id="verify-btn"
                            class="btn-primary group relative w-full flex justify-center py-3 px-6 border border-transparent text-sm font-semibold rounded-xl text-black focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-salon-gold focus:ring-offset-gray-800">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                            <svg class="h-5 w-5 text-black/80 group-hover:text-black transition-colors" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        Verify Code
                    </button>
                </div>
            </form>

            <!-- Resend Form -->
            <form method="POST" class="mt-4">
                <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">
                <input type="hidden" name="action" value="resend">
                
                <div class="text-center">
                    <p class="text-sm text-gray-300 mb-3">
                        Didn't receive the code?
                    </p>
                    <button type="submit" id="resend-btn"
                            class="btn-secondary px-6 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                        Resend Code
                    </button>
                </div>
            </form>

            <div class="text-center pt-6">
                <a href="<?= getBasePath() ?>/auth/forgot-password.php"
                   class="text-sm text-gray-400 hover:text-salon-gold transition-colors">
                    ← Back to email entry
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize timer
let timeRemaining = <?= $timeRemaining ?>;

function updateTimer() {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    
    document.getElementById('timer-minutes').textContent = minutes.toString().padStart(2, '0');
    document.getElementById('timer-seconds').textContent = seconds.toString().padStart(2, '0');
    
    // Update circle progress
    const totalTime = 15 * 60; // 15 minutes in seconds
    const progress = ((totalTime - timeRemaining) / totalTime) * 360;
    const circle = document.querySelector('.timer-circle');
    circle.style.background = `conic-gradient(#374151 ${progress}deg, #f59e0b ${progress}deg)`;
    
    if (timeRemaining <= 0) {
        window.location.href = '<?= getBasePath() ?>/auth/forgot-password.php?error=expired';
        return;
    }
    
    timeRemaining--;
}

// Update timer every second
updateTimer();
setInterval(updateTimer, 1000);

// OTP Input handling
const otpInputs = document.querySelectorAll('.otp-input');
const otpHidden = document.getElementById('otp-hidden');

otpInputs.forEach((input, index) => {
    input.addEventListener('input', function(e) {
        const value = e.target.value;
        
        // Only allow digits
        if (!/^\d$/.test(value) && value !== '') {
            e.target.value = '';
            return;
        }
        
        // Update visual state
        if (value) {
            e.target.classList.add('filled');
            // Move to next input
            if (index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }
        } else {
            e.target.classList.remove('filled');
        }
        
        // Update hidden field
        updateOTPValue();
    });
    
    input.addEventListener('keydown', function(e) {
        // Handle backspace
        if (e.key === 'Backspace' && !e.target.value && index > 0) {
            otpInputs[index - 1].focus();
            otpInputs[index - 1].value = '';
            otpInputs[index - 1].classList.remove('filled');
            updateOTPValue();
        }
        
        // Handle paste
        if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            navigator.clipboard.readText().then(text => {
                const digits = text.replace(/\D/g, '').slice(0, 6);
                for (let i = 0; i < digits.length && i < otpInputs.length; i++) {
                    otpInputs[i].value = digits[i];
                    otpInputs[i].classList.add('filled');
                }
                updateOTPValue();
            });
        }
    });
});

function updateOTPValue() {
    const otp = Array.from(otpInputs).map(input => input.value).join('');
    otpHidden.value = otp;
    
    // Enable/disable verify button
    const verifyBtn = document.getElementById('verify-btn');
    if (otp.length === 6) {
        verifyBtn.disabled = false;
        verifyBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        verifyBtn.disabled = true;
        verifyBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
}

// Initialize
updateOTPValue();

// Focus first input
otpInputs[0].focus();
</script>

<?php
// Include footer
include __DIR__ . '/../includes/footer.php';
?>
