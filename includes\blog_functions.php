<?php

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/upload_functions.php'; // For image uploads

/**
 * Creates a new blog post.
 * @param array $data Post data from the form.
 * @param ?array $fileData File data for image upload.
 * @return array Result array with success status and message/data.
 */
function createBlogPost(array $data, ?array $fileData): array {
    global $database, $auth;

    $title = sanitize($data['title'] ?? '');
    $summary = sanitize($data['summary'] ?? null);
    $full_content = $data['full_content'] ?? ''; // Allow HTML, sanitize on display
    $publish_date_str = sanitize($data['publish_date'] ?? null);
    $status = sanitize($data['status'] ?? 'draft');
    $author_id = !empty($data['author_id']) ? sanitize($data['author_id']) : ($auth->getUserId() ?? null); // Default to current admin if not set
    
    $image_url_option = sanitize($data['image_url_option'] ?? 'url');
    $image_url_input = sanitize($data['image_url_input'] ?? null); // URL from input
    
    $image_final_url = null;

    if (empty($title) || empty($full_content)) {
        return ['success' => false, 'error' => 'Title and Full Content are required.'];
    }

    // Handle Image
    if ($image_url_option === 'upload' && $fileData && $fileData['error'] === UPLOAD_ERR_OK) {
        $uploadResult = uploadFile($fileData, 'blog', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        if ($uploadResult['success']) {
            $image_final_url = $uploadResult['filename'];
        } else {
            return ['success' => false, 'error' => 'Image upload failed: ' . $uploadResult['error']];
        }
    } elseif ($image_url_option === 'url' && !empty($image_url_input)) {
        if (filter_var($image_url_input, FILTER_VALIDATE_URL)) {
            $image_final_url = $image_url_input;
        } else {
            return ['success' => false, 'error' => 'Invalid image URL provided.'];
        }
    }

    $slug = generateSlug($title);
    // Ensure slug is unique
    $count = 0;
    $originalSlug = $slug;
    while ($database->fetch("SELECT id FROM blog_posts WHERE slug = ?", [$slug])) {
        $count++;
        $slug = $originalSlug . '-' . $count;
    }

    $publish_date = null;
    if (!empty($publish_date_str)) {
        try {
            $dt = new DateTime($publish_date_str);
            $publish_date = $dt->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Invalid publish date format.'];
        }
    }

    // Generate UUID for the blog post
    $postId = generateUUID();

    $sql = "INSERT INTO blog_posts (id, title, slug, summary, full_content, image_url, publish_date, author_id, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    try {
        // Use the query method which handles prepare and execute for INSERT
        $database->query($sql, [$postId, $title, $slug, $summary, $full_content, $image_final_url, $publish_date, $author_id, $status]);
        return ['success' => true, 'id' => $postId];
    } catch (PDOException $e) {
        // Log error $e->getMessage()
        return ['success' => false, 'error' => 'Database error: Could not create blog post. ' . $e->getMessage()];
    }
}

/**
 * Updates an existing blog post.
 * @param string $id Post ID.
 * @param array $data Post data from the form.
 * @param ?array $fileData File data for image upload.
 * @return array Result array with success status and message/data.
 */
function updateBlogPost(string $id, array $data, ?array $fileData): array {
    global $database, $auth;

    $post = $database->fetch("SELECT * FROM blog_posts WHERE id = ?", [$id]);
    if (!$post) {
        return ['success' => false, 'error' => 'Blog post not found.'];
    }

    $title = sanitize($data['title'] ?? $post['title']);
    $summary = sanitize($data['summary'] ?? $post['summary']);
    $full_content = $data['full_content'] ?? $post['full_content']; // Allow HTML
    $publish_date_str = sanitize($data['publish_date'] ?? null);
    $status = sanitize($data['status'] ?? $post['status']);
    $author_id = !empty($data['author_id']) ? sanitize($data['author_id']) : $post['author_id'];

    $image_url_option = sanitize($data['image_url_option'] ?? 'url');
    $image_url_input = sanitize($data['image_url_input'] ?? null);
    $existing_image_url = sanitize($data['existing_image_url'] ?? $post['image_url']);
    
    $image_final_url = $existing_image_url; // Default to existing

    if (empty($title) || empty($full_content)) {
        return ['success' => false, 'error' => 'Title and Full Content are required.'];
    }

    // Handle Image Update
    if ($image_url_option === 'upload' && $fileData && $fileData['error'] === UPLOAD_ERR_OK) {
        // If there's an old uploaded image (not a URL), delete it
        if ($post['image_url'] && !filter_var($post['image_url'], FILTER_VALIDATE_URL)) {
            deleteUploadedFile($post['image_url'], 'blog');
        }
        $uploadResult = uploadFile($fileData, 'blog', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        if ($uploadResult['success']) {
            $image_final_url = $uploadResult['filename'];
        } else {
            return ['success' => false, 'error' => 'Image upload failed: ' . $uploadResult['error']];
        }
    } elseif ($image_url_option === 'url') {
        if (!empty($image_url_input)) {
            if (filter_var($image_url_input, FILTER_VALIDATE_URL)) {
                // If old image was an uploaded file and new one is URL, delete old file
                if ($post['image_url'] && !filter_var($post['image_url'], FILTER_VALIDATE_URL) && $post['image_url'] !== $image_url_input) {
                     deleteUploadedFile($post['image_url'], 'blog');
                }
                $image_final_url = $image_url_input;
            } else {
                return ['success' => false, 'error' => 'Invalid image URL provided.'];
            }
        } elseif (empty($image_url_input) && $existing_image_url) {
            // If URL field is cleared, and there was an existing image, remove it
            if ($post['image_url'] && !filter_var($post['image_url'], FILTER_VALIDATE_URL)) {
                deleteUploadedFile($post['image_url'], 'blog');
            }
            $image_final_url = null;
        }
    }
    // If image_url_option is 'upload' but no new file is given, $image_final_url remains $existing_image_url

    $slug = generateSlug($title);
    if ($slug !== $post['slug']) {
        // Ensure slug is unique if changed
        $count = 0;
        $originalSlug = $slug;
        while ($database->fetch("SELECT id FROM blog_posts WHERE slug = ? AND id != ?", [$slug, $id])) {
            $count++;
            $slug = $originalSlug . '-' . $count;
        }
    }

    $publish_date = $post['publish_date']; // Keep existing if not changed
    if (!empty($publish_date_str)) {
        try {
            $dt = new DateTime($publish_date_str);
            $publish_date = $dt->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Invalid publish date format.'];
        }
    } elseif (isset($data['publish_date']) && empty($publish_date_str)) {
        // If field was submitted but empty, set to NULL
        $publish_date = null;
    }


    $sql = "UPDATE blog_posts SET 
                title = ?, slug = ?, summary = ?, full_content = ?, 
                image_url = ?, publish_date = ?, author_id = ?, status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
    
    try {
        // Use the query method which handles prepare and execute for UPDATE
        $database->query($sql, [$title, $slug, $summary, $full_content, $image_final_url, $publish_date, $author_id, $status, $id]);
        return ['success' => true];
    } catch (PDOException $e) {
        // Log error $e->getMessage()
        return ['success' => false, 'error' => 'Database error: Could not update blog post. ' . $e->getMessage()];
    }
}

/**
 * Deletes a blog post.
 * @param string $id Post ID.
 * @return array Result array with success status and message.
 */
function deleteBlogPost(string $id): array {
    global $database;

    $post = $database->fetch("SELECT image_url FROM blog_posts WHERE id = ?", [$id]);
    if (!$post) {
        return ['success' => false, 'error' => 'Blog post not found.'];
    }

    // If there's an uploaded image (not a URL), delete it
    if ($post['image_url'] && !filter_var($post['image_url'], FILTER_VALIDATE_URL)) {
        deleteUploadedFile($post['image_url'], 'blog');
    }

    $sql = "DELETE FROM blog_posts WHERE id = ?";
    try {
        // Use the execute method for DELETE, which returns rowCount
        $rowCount = $database->execute($sql, [$id]);
        if ($rowCount > 0) {
            return ['success' => true];
        }
        return ['success' => false, 'error' => 'Blog post not found or already deleted.'];
    } catch (PDOException $e) {
        // Log error $e->getMessage()
        return ['success' => false, 'error' => 'Database error: Could not delete blog post.'];
    }
}

/**
 * Fetches a single blog post by ID for editing.
 * @param string $id
 * @return array|false
 */
function getBlogPostById(string $id) {
    global $database;
    return $database->fetch("SELECT * FROM blog_posts WHERE id = ?", [$id]);
}

/**
 * Get blog image URL - handles both uploaded files and external URLs
 * @param string|null $image The image value from database
 * @return string The full URL to the image or empty string
 */
function getBlogImageUrl($image) {
    if (empty($image)) {
        return '';
    }

    // If it's already a URL, return as is
    if (filter_var($image, FILTER_VALIDATE_URL)) {
        return $image;
    }

    // If it's a filename, construct the URL
    return getBasePath() . '/uploads/blog/' . $image;
}