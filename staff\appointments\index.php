<?php
session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_status':
                    $appointmentId = $_POST['appointment_id'];
                    $status = $_POST['status'];
                    $notes = $_POST['notes'] ?? null;
                    
                    updateAppointmentStatus($appointmentId, $_SESSION['user_id'], $status, $notes);
                    $message = 'Appointment status updated successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get filters
$dateFilter = $_GET['date'] ?? '';  // Empty by default to show all dates
$statusFilter = $_GET['status'] ?? 'all';

// Get staff appointments
$staffId = $_SESSION['user_id'];
$profile = getStaffProfile($staffId);

// Get appointments with filters
global $database;
$conditions = ["b.staff_id = ?"];
$params = [$staffId];

// Only apply date filter if specifically provided
if ($dateFilter && $dateFilter !== '') {
    $conditions[] = "DATE(b.date) = ?";
    $params[] = $dateFilter;
}

if ($statusFilter !== 'all') {
    $conditions[] = "b.status = ?";
    $params[] = $statusFilter;
}

$whereClause = 'WHERE ' . implode(' AND ', $conditions);

$appointments = $database->fetchAll("
    SELECT
        b.*,
        u.name as customer_name,
        u.phone as customer_phone,
        u.email as customer_email,
        u.points as customer_points,
        s.name as service_name,
        s.duration as service_duration,
        s.price as service_price,
        CASE
            WHEN b.date >= CURDATE() THEN 0
            ELSE 1
        END as is_past
    FROM bookings b
    LEFT JOIN users u ON b.user_id = u.id
    LEFT JOIN services s ON b.service_id = s.id
    $whereClause
    ORDER BY is_past ASC, b.date ASC, b.start_time ASC
", $params);

$pageTitle = "Appointments Management";
include __DIR__ . '/../../includes/staff_header.php';
?>

<!-- Message Display -->
<?php if ($message): ?>
    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
        <?= htmlspecialchars($message) ?>
    </div>
<?php endif; ?>

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-white">Appointments Management</h1>
            <p class="mt-2 text-gray-400">View and manage your assigned appointments</p>
        </div>
    </div>
</div>

            <!-- Quick Filters -->
            <div class="bg-secondary-800 rounded-lg p-4 mb-4">
                <div class="flex flex-wrap gap-2">
                    <span class="text-sm text-gray-400 mr-4 flex items-center">Quick Filters:</span>
                    <button type="button" onclick="clearFilters()" class="px-3 py-1 bg-salon-gold hover:bg-yellow-500 text-black text-sm rounded transition-colors">
                        All Appointments
                    </button>
                    <button type="button" onclick="setDateFilter(0)" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                        Today
                    </button>
                    <button type="button" onclick="setDateFilter(1)" class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors">
                        Tomorrow
                    </button>
                    <button type="button" onclick="setWeekFilter()" class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors">
                        This Week
                    </button>
                    <button type="button" onclick="setStatusFilter('PENDING')" class="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded transition-colors">
                        Pending
                    </button>
                    <button type="button" onclick="setStatusFilter('CONFIRMED')" class="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white text-sm rounded transition-colors">
                        Confirmed
                    </button>
                </div>
            </div>

            <!-- Advanced Filters -->
            <div class="bg-secondary-800 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">Advanced Filters</h3>
                <form method="GET" class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <label for="date" class="block text-sm font-medium text-gray-300 mb-2">Date (Optional)</label>
                        <input type="date" id="date" name="date" value="<?= htmlspecialchars($dateFilter) ?>"
                               placeholder="Select date to filter..."
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                        <select id="status" name="status" 
                                class="px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="all" <?= $statusFilter === 'all' ? 'selected' : '' ?>>All Status</option>
                            <option value="PENDING" <?= $statusFilter === 'PENDING' ? 'selected' : '' ?>>Pending</option>
                            <option value="CONFIRMED" <?= $statusFilter === 'CONFIRMED' ? 'selected' : '' ?>>Confirmed</option>
                            <option value="IN_PROGRESS" <?= $statusFilter === 'IN_PROGRESS' ? 'selected' : '' ?>>In Progress</option>
                            <option value="COMPLETED" <?= $statusFilter === 'COMPLETED' ? 'selected' : '' ?>>Completed</option>
                            <option value="CANCELLED" <?= $statusFilter === 'CANCELLED' ? 'selected' : '' ?>>Cancelled</option>
                            <option value="NO_SHOW" <?= $statusFilter === 'NO_SHOW' ? 'selected' : '' ?>>No Show</option>
                        </select>
                    </div>
                    <div class="flex items-end gap-2">
                        <button type="submit" class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                        <button type="button" onclick="clearFilters()" class="px-4 py-2 bg-secondary-600 hover:bg-secondary-500 text-white rounded-lg font-semibold transition-colors">
                            <i class="fas fa-times mr-2"></i>Clear
                        </button>
                    </div>
                </form>
            </div>

            <!-- Filter Summary -->
            <div class="bg-secondary-800 rounded-lg p-4 mb-4">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-white font-medium">
                            Showing <?= count($appointments) ?> appointment<?= count($appointments) !== 1 ? 's' : '' ?>
                        </span>
                        <?php if ($dateFilter): ?>
                            <span class="text-sm bg-blue-600 text-white px-2 py-1 rounded">
                                Date: <?= date('M j, Y', strtotime($dateFilter)) ?>
                            </span>
                        <?php else: ?>
                            <span class="text-sm bg-salon-gold text-black px-2 py-1 rounded">
                                All Dates
                            </span>
                        <?php endif; ?>
                        <?php if ($statusFilter !== 'all'): ?>
                            <span class="text-sm bg-purple-600 text-white px-2 py-1 rounded">
                                Status: <?= ucfirst(strtolower($statusFilter)) ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    <?php if ($dateFilter || $statusFilter !== 'all'): ?>
                        <button onclick="clearFilters()" class="mt-2 sm:mt-0 text-sm text-salon-gold hover:text-yellow-400 transition-colors">
                            <i class="fas fa-times mr-1"></i>Clear All Filters
                        </button>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Appointments List -->
            <div class="bg-secondary-800 rounded-lg overflow-hidden">
                <?php if (empty($appointments)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-calendar-times text-6xl text-gray-600 mb-4"></i>
                        <h3 class="text-xl font-semibold text-white mb-2">No Appointments Found</h3>
                        <p class="text-gray-400">No appointments match your current filters</p>
                    </div>
                <?php else: ?>
                    <div class="divide-y divide-secondary-700">
                        <?php foreach ($appointments as $appointment): ?>
                            <div class="p-6 <?= $appointment['is_past'] ? 'opacity-75 bg-secondary-900' : '' ?>">
                                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-start gap-4">
                                            <div class="w-12 h-12 rounded-full bg-salon-gold flex items-center justify-center">
                                                <i class="fas fa-user text-black"></i>
                                            </div>
                                            <div class="flex-1">
                                                <div class="flex items-center gap-3 mb-2">
                                                    <h3 class="text-lg font-semibold text-white"><?= htmlspecialchars($appointment['customer_name']) ?></h3>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                        <?php
                                                        switch($appointment['status']) {
                                                            case 'PENDING': echo 'bg-yellow-100 text-yellow-800'; break;
                                                            case 'CONFIRMED': echo 'bg-blue-100 text-blue-800'; break;
                                                            case 'IN_PROGRESS': echo 'bg-purple-100 text-purple-800'; break;
                                                            case 'COMPLETED': echo 'bg-green-100 text-green-800'; break;
                                                            case 'CANCELLED': echo 'bg-red-100 text-red-800'; break;
                                                            case 'NO_SHOW': echo 'bg-gray-100 text-gray-800'; break;
                                                            default: echo 'bg-gray-100 text-gray-800';
                                                        }
                                                        ?>">
                                                        <?= $appointment['status'] ?>
                                                    </span>
                                                </div>
                                                
                                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                                                    <div>
                                                        <p class="text-gray-400">Service</p>
                                                        <p class="text-white font-medium"><?= htmlspecialchars($appointment['service_name']) ?></p>
                                                    </div>
                                                    <div>
                                                        <p class="text-gray-400">Date & Time</p>
                                                        <p class="text-white font-medium">
                                                            <?= date('M j, Y', strtotime($appointment['date'])) ?>
                                                            <?php if ($appointment['is_past']): ?>
                                                                <span class="text-xs text-gray-500 ml-1">(Past)</span>
                                                            <?php elseif ($appointment['date'] === date('Y-m-d')): ?>
                                                                <span class="text-xs text-salon-gold ml-1">(Today)</span>
                                                            <?php endif; ?>
                                                            <br>
                                                            <?= date('g:i A', strtotime($appointment['start_time'])) ?>
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p class="text-gray-400">Duration & Price</p>
                                                        <p class="text-white font-medium">
                                                            <?= $appointment['service_duration'] ?> min<br>
                                                            $<?= number_format($appointment['total_amount'], 2) ?>
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <p class="text-gray-400">Contact</p>
                                                        <p class="text-white font-medium">
                                                            <?= htmlspecialchars($appointment['customer_phone']) ?><br>
                                                            <span class="text-xs text-gray-400"><?= htmlspecialchars($appointment['customer_email']) ?></span>
                                                        </p>
                                                    </div>
                                                </div>
                                                
                                                <?php if ($appointment['notes']): ?>
                                                    <div class="mt-3">
                                                        <p class="text-gray-400 text-sm">Notes:</p>
                                                        <p class="text-gray-300 text-sm"><?= htmlspecialchars($appointment['notes']) ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4 lg:mt-0 lg:ml-6">
                                        <div class="flex flex-wrap gap-2">
                                            <?php if ($appointment['status'] === 'PENDING'): ?>
                                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'CONFIRMED')" 
                                                        class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
                                                    Confirm
                                                </button>
                                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'CANCELLED')" 
                                                        class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
                                                    Cancel
                                                </button>
                                            <?php elseif ($appointment['status'] === 'CONFIRMED'): ?>
                                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'IN_PROGRESS')" 
                                                        class="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded transition-colors">
                                                    Start Service
                                                </button>
                                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'NO_SHOW')" 
                                                        class="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors">
                                                    No Show
                                                </button>
                                            <?php elseif ($appointment['status'] === 'IN_PROGRESS'): ?>
                                                <button onclick="updateStatus('<?= $appointment['id'] ?>', 'COMPLETED')" 
                                                        class="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors">
                                                    Complete
                                                </button>
                                            <?php endif; ?>
                                            
                                            <button onclick="openNotesModal('<?= $appointment['id'] ?>', '<?= htmlspecialchars($appointment['notes'] ?? '') ?>')" 
                                                    class="px-3 py-1 bg-salon-gold hover:bg-yellow-500 text-black text-sm rounded transition-colors">
                                                <i class="fas fa-sticky-note mr-1"></i>Notes
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
<!-- Notes Modal -->
    <div id="notesModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-secondary-800 rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">Appointment Notes</h3>
                        <button onclick="closeNotesModal()" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form id="notesForm" method="POST">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" id="notesAppointmentId" name="appointment_id" value="">
                        <input type="hidden" id="notesStatus" name="status" value="">

                        <div class="mb-4">
                            <label for="notes" class="block text-sm font-medium text-gray-300 mb-2">Notes</label>
                            <textarea id="notes" name="notes" rows="4" 
                                      class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                      placeholder="Add notes about this appointment..."></textarea>
                        </div>

                        <div class="flex items-center justify-end space-x-3">
                            <button type="button" onclick="closeNotesModal()" 
                                    class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                                Save Notes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(appointmentId, status) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="appointment_id" value="${appointmentId}">
                <input type="hidden" name="status" value="${status}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function openNotesModal(appointmentId, currentNotes) {
            document.getElementById('notesAppointmentId').value = appointmentId;
            document.getElementById('notes').value = currentNotes;
            document.getElementById('notesModal').classList.remove('hidden');
        }

        function closeNotesModal() {
            document.getElementById('notesModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('notesModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeNotesModal();
            }
        });

        // Clear all filters function
        function clearFilters() {
            document.getElementById('date').value = '';
            document.getElementById('status').value = 'all';
            // Submit the form to reload with cleared filters
            document.querySelector('form').submit();
        }

        // Quick filter functions
        function setDateFilter(days) {
            const date = new Date();
            date.setDate(date.getDate() + days);
            document.getElementById('date').value = date.toISOString().split('T')[0];
            document.getElementById('status').value = 'all';
            document.querySelector('form').submit();
        }

        function setWeekFilter() {
            // Clear date filter to show all appointments for the week
            // This could be enhanced to show a date range, but for now we'll show all
            document.getElementById('date').value = '';
            document.getElementById('status').value = 'all';
            document.querySelector('form').submit();
        }

        function setStatusFilter(status) {
            document.getElementById('date').value = '';
            document.getElementById('status').value = status;
            document.querySelector('form').submit();
        }
    </script>

<?php include __DIR__ . '/../../includes/staff_footer.php'; ?>
