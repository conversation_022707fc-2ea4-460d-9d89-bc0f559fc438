<?php
/**
 * Notifications Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Require admin authentication
$auth->requireRole('ADMIN');

// Get filter parameters
$category = isset($_GET['category']) ? $_GET['category'] : 'all';
$status = isset($_GET['status']) ? $_GET['status'] : 'all';
$priority = isset($_GET['priority']) ? $_GET['priority'] : 'all';
$page = max(1, (int)(isset($_GET['page']) ? $_GET['page'] : 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query conditions
$whereConditions = ['user_id = ?'];
$params = [$_SESSION['user_id']];

if ($category !== 'all') {
    $whereConditions[] = 'category = ?';
    $params[] = $category;
}

if ($status === 'read') {
    $whereConditions[] = 'is_read = TRUE';
} elseif ($status === 'unread') {
    $whereConditions[] = 'is_read = FALSE';
}

if ($priority !== 'all') {
    $whereConditions[] = 'priority = ?';
    $params[] = $priority;
}

// Add expiration check
$whereConditions[] = '(expires_at IS NULL OR expires_at > NOW())';

$whereClause = implode(' AND ', $whereConditions);

// Get notifications
$notifications = $database->fetchAll(
    "SELECT * FROM notifications 
     WHERE {$whereClause}
     ORDER BY 
        CASE priority 
            WHEN 'URGENT' THEN 1 
            WHEN 'HIGH' THEN 2 
            WHEN 'MEDIUM' THEN 3 
            WHEN 'LOW' THEN 4 
        END,
        created_at DESC
     LIMIT ? OFFSET ?",
    array_merge($params, [$limit, $offset])
);

// Get total count
$totalCount = $database->fetch(
    "SELECT COUNT(*) as count FROM notifications WHERE {$whereClause}",
    $params
)['count'];

// Get category counts
$categoryCounts = $database->fetchAll(
    "SELECT category, COUNT(*) as count 
     FROM notifications 
     WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
     GROUP BY category",
    [$_SESSION['user_id']]
);

$categoryCountsMap = [];
foreach ($categoryCounts as $cat) {
    $categoryCountsMap[$cat['category']] = (int)$cat['count'];
}

// Get status counts
$statusCounts = $database->fetchAll(
    "SELECT is_read, COUNT(*) as count 
     FROM notifications 
     WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
     GROUP BY is_read",
    [$_SESSION['user_id']]
);

$readCount = 0;
$unreadCount = 0;
foreach ($statusCounts as $status) {
    if ($status['is_read']) {
        $readCount = (int)$status['count'];
    } else {
        $unreadCount = (int)$status['count'];
    }
}

$totalPages = ceil($totalCount / $limit);

// Category definitions for visual styling
$categoryConfig = [
    'BOOKING' => [
        'name' => 'Booking',
        'color' => 'blue',
        'icon' => 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
    ],
    'CUSTOMER' => [
        'name' => 'Customer',
        'color' => 'green',
        'icon' => 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
    ],
    'STAFF' => [
        'name' => 'Staff',
        'color' => 'purple',
        'icon' => 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
    ],
    'PAYMENT' => [
        'name' => 'Payment',
        'color' => 'yellow',
        'icon' => 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
    ],
    'SYSTEM' => [
        'name' => 'System',
        'color' => 'red',
        'icon' => 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'
    ],
    'MARKETING' => [
        'name' => 'Marketing',
        'color' => 'pink',
        'icon' => 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'
    ],
    'FEEDBACK' => [
        'name' => 'Feedback',
        'color' => 'indigo',
        'icon' => 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
    ]
];

$pageTitle = 'Notifications Management';
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Notifications Management</h1>
                                <p class="mt-1 text-sm text-gray-300">Manage and view all system notifications</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-2">
                                <button onclick="markAllPageNotificationsAsRead()"
                                        class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Mark All Read
                                </button>
                                <button onclick="showBulkActions()" 
                                        class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                    Bulk Actions
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Filters and Stats -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <!-- Stats Row -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-white"><?= $totalCount ?></div>
                                <div class="text-sm text-gray-400">Total</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-red-400"><?= $unreadCount ?></div>
                                <div class="text-sm text-gray-400">Unread</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-400"><?= $readCount ?></div>
                                <div class="text-sm text-gray-400">Read</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-salon-gold"><?= count($categoryCountsMap) ?></div>
                                <div class="text-sm text-gray-400">Categories</div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <!-- Category Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                                <select name="category" class="w-full bg-secondary-700 border border-secondary-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                                    <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>All Categories</option>
                                    <?php foreach ($categoryConfig as $key => $config): ?>
                                        <option value="<?= $key ?>" <?= $category === $key ? 'selected' : '' ?>>
                                            <?= $config['name'] ?> 
                                            <?php if (isset($categoryCountsMap[$key])): ?>
                                                (<?= $categoryCountsMap[$key] ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                                <select name="status" class="w-full bg-secondary-700 border border-secondary-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                                    <option value="unread" <?= $status === 'unread' ? 'selected' : '' ?>>Unread (<?= $unreadCount ?>)</option>
                                    <option value="read" <?= $status === 'read' ? 'selected' : '' ?>>Read (<?= $readCount ?>)</option>
                                </select>
                            </div>

                            <!-- Priority Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Priority</label>
                                <select name="priority" class="w-full bg-secondary-700 border border-secondary-600 text-white rounded-lg px-3 py-2 focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                                    <option value="all" <?= $priority === 'all' ? 'selected' : '' ?>>All Priorities</option>
                                    <option value="URGENT" <?= $priority === 'URGENT' ? 'selected' : '' ?>>Urgent</option>
                                    <option value="HIGH" <?= $priority === 'HIGH' ? 'selected' : '' ?>>High</option>
                                    <option value="MEDIUM" <?= $priority === 'MEDIUM' ? 'selected' : '' ?>>Medium</option>
                                    <option value="LOW" <?= $priority === 'LOW' ? 'selected' : '' ?>>Low</option>
                                </select>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-end">
                                <button type="submit" class="w-full bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Apply Filters
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Notifications List -->
                    <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                        <?php if (empty($notifications)): ?>
                            <!-- Empty State -->
                            <div class="text-center py-12">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-300">No notifications</h3>
                                <p class="mt-1 text-sm text-gray-400">
                                    <?php if ($category !== 'all' || $status !== 'all' || $priority !== 'all'): ?>
                                        No notifications match your current filters.
                                    <?php else: ?>
                                        You don't have any notifications yet.
                                    <?php endif; ?>
                                </p>
                                <?php if ($category !== 'all' || $status !== 'all' || $priority !== 'all'): ?>
                                    <div class="mt-6">
                                        <a href="<?= getBasePath() ?>/admin/notifications"
                                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-black bg-salon-gold hover:bg-gold-light">
                                            Clear Filters
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <!-- Notifications Table -->
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-secondary-700">
                                    <thead class="bg-secondary-900">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()"
                                                       class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                Notification
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                Category
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                Priority
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                Date
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                                        <?php foreach ($notifications as $notification):
                                            $config = isset($categoryConfig[$notification['category']]) ? $categoryConfig[$notification['category']] : $categoryConfig['SYSTEM'];
                                            $priorityColors = [
                                                'URGENT' => 'bg-red-100 text-red-800',
                                                'HIGH' => 'bg-orange-100 text-orange-800',
                                                'MEDIUM' => 'bg-blue-100 text-blue-800',
                                                'LOW' => 'bg-gray-100 text-gray-800'
                                            ];
                                            $priorityColor = isset($priorityColors[$notification['priority']]) ? $priorityColors[$notification['priority']] : $priorityColors['MEDIUM'];
                                        ?>
                                            <tr class="<?= $notification['is_read'] ? 'opacity-60' : '' ?> hover:bg-secondary-700 transition-colors">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <input type="checkbox" name="notification_ids[]" value="<?= htmlspecialchars($notification['id']) ?>"
                                                           class="notification-checkbox rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="flex items-start space-x-3">
                                                        <div class="flex-shrink-0">
                                                            <div class="w-8 h-8 rounded-full bg-<?= $config['color'] ?>-100 flex items-center justify-center">
                                                                <svg class="w-4 h-4 text-<?= $config['color'] ?>-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $config['icon'] ?>" />
                                                                </svg>
                                                            </div>
                                                        </div>
                                                        <div class="flex-1 min-w-0">
                                                            <p class="text-sm font-medium text-white <?= !$notification['is_read'] ? 'font-semibold' : '' ?>">
                                                                <?= htmlspecialchars($notification['title']) ?>
                                                            </p>
                                                            <p class="text-sm text-gray-400 mt-1 line-clamp-2">
                                                                <?= htmlspecialchars($notification['message']) ?>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $config['color'] ?>-100 text-<?= $config['color'] ?>-800">
                                                        <?= $config['name'] ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $priorityColor ?>">
                                                        <?= ucfirst(strtolower($notification['priority'])) ?>
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                                    <div>
                                                        <?= date('M j, Y', strtotime($notification['created_at'])) ?>
                                                    </div>
                                                    <div class="text-xs text-gray-500">
                                                        <?= date('g:i A', strtotime($notification['created_at'])) ?>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <?php if ($notification['is_read']): ?>
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            Read
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                            Unread
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                    <div class="flex items-center space-x-2">
                                                        <button onclick="openNotificationDetails('<?= htmlspecialchars($notification['id']) ?>')"
                                                                class="text-salon-gold hover:text-gold-light transition-colors">
                                                            View
                                                        </button>
                                                        <?php if (!$notification['is_read']): ?>
                                                            <button onclick="markAsRead('<?= htmlspecialchars($notification['id']) ?>')"
                                                                    class="text-blue-400 hover:text-blue-300 transition-colors">
                                                                Mark Read
                                                            </button>
                                                        <?php else: ?>
                                                            <button onclick="markAsUnread('<?= htmlspecialchars($notification['id']) ?>')"
                                                                    class="text-gray-400 hover:text-gray-300 transition-colors">
                                                                Mark Unread
                                                            </button>
                                                        <?php endif; ?>
                                                        <button onclick="deleteNotification('<?= htmlspecialchars($notification['id']) ?>')"
                                                                class="text-red-400 hover:text-red-300 transition-colors">
                                                            Delete
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-lg mt-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>"
                                       class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if ($page < $totalPages): ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>"
                                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-400">
                                        Showing <span class="font-medium"><?= ($page - 1) * $limit + 1 ?></span> to
                                        <span class="font-medium"><?= min($page * $limit, $totalCount) ?></span> of
                                        <span class="font-medium"><?= $totalCount ?></span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <?php if ($page > 1): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>"
                                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-600 bg-secondary-700 text-sm font-medium text-gray-300 hover:bg-secondary-600">
                                                <span class="sr-only">Previous</span>
                                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        <?php endif; ?>

                                        <?php
                                        $start = max(1, $page - 2);
                                        $end = min($totalPages, $page + 2);

                                        for ($i = $start; $i <= $end; $i++): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"
                                               class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium <?= $i === $page ? 'bg-salon-gold text-black' : 'bg-secondary-700 text-gray-300 hover:bg-secondary-600' ?>">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>

                                        <?php if ($page < $totalPages): ?>
                                            <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>"
                                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-600 bg-secondary-700 text-sm font-medium text-gray-300 hover:bg-secondary-600">
                                                <span class="sr-only">Next</span>
                                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                                </svg>
                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
<!-- Notification Details Modal - New Implementation -->
<div id="notificationDetailsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-2xl border border-secondary-700">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-secondary-700">
                <h3 class="text-xl font-semibold text-white">Notification Details</h3>
                <button id="closeNotificationDetailsBtn" class="text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-secondary-700">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                <div id="notificationDetailsContent">
                    <!-- Loading state -->
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-salon-gold"></div>
                        <span class="ml-3 text-gray-300">Loading notification details...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div id="bulkActionsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-lg max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-white">Bulk Actions</h3>
                    <button onclick="closeBulkActionsModal()" class="text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <button onclick="bulkMarkAsRead()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Mark Selected as Read
                    </button>
                    <button onclick="bulkMarkAsUnread()" class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        Mark Selected as Unread
                    </button>
                    <button onclick="bulkDelete()" class="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        Delete Selected
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Confirmation Modal -->
<div id="confirmationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-secondary-800 rounded-xl max-w-md w-full shadow-2xl border border-secondary-700">
            <div class="p-6">
                <!-- Icon -->
                <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full" id="confirmationIcon">
                    <!-- Icon will be set dynamically -->
                </div>

                <!-- Title -->
                <h3 class="text-lg font-semibold text-white text-center mb-2" id="confirmationTitle">
                    Confirm Action
                </h3>

                <!-- Message -->
                <p class="text-gray-300 text-center mb-6" id="confirmationMessage">
                    Are you sure you want to proceed?
                </p>

                <!-- Actions -->
                <div class="flex space-x-3">
                    <button onclick="closeConfirmationModal()"
                            class="flex-1 px-4 py-2 bg-secondary-700 text-gray-300 rounded-lg hover:bg-secondary-600 transition-colors font-medium">
                        Cancel
                    </button>
                    <button id="confirmationConfirmBtn"
                            class="flex-1 px-4 py-2 rounded-lg font-medium transition-colors">
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notification Container -->
<div id="toastContainer" class="fixed top-4 right-4 z-[60] space-y-2"></div>

<script>
const basePath = '<?= getBasePath() ?>';

// Page-specific notification variables (renamed to avoid conflicts with header system)
let pageLastNotificationCount = <?= $unreadCount ?>;
let pageNotificationRefreshInterval;

// Initialize page-specific notifications
document.addEventListener('DOMContentLoaded', function() {
    startPageNotificationRefresh();

    // Add escape key handler for modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeNotificationModal();
            closeBulkActionsModal();
            closeConfirmationModal();
        }
    });
});

// Page-specific notification refresh
function startPageNotificationRefresh() {
    // Refresh every 60 seconds
    pageNotificationRefreshInterval = setInterval(checkForPageNewNotifications, 60000);
}

function checkForPageNewNotifications() {
    fetch(`${basePath}/api/admin/notifications.php?limit=1&unread_only=true`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data.counts) {
                const currentCount = data.data.counts.unread;

                // Check if there are new notifications
                if (currentCount > pageLastNotificationCount) {
                    const newNotifications = currentCount - pageLastNotificationCount;
                    showToast(`You have ${newNotifications} new notification${newNotifications > 1 ? 's' : ''}`, 'info', 5000);

                    // Update stats on page
                    updatePageNotificationStats(data.data.counts);
                }

                pageLastNotificationCount = currentCount;
            }
        })
        .catch(error => {
            console.error('Error checking for new notifications:', error);
        });
}

function updatePageNotificationStats(counts) {
    // Update unread count display
    const unreadElement = document.querySelector('.text-red-400');
    if (unreadElement && unreadElement.textContent !== undefined) {
        unreadElement.textContent = counts.unread;
    }

    // Update total count if needed
    const totalElement = document.querySelector('.text-white');
    if (totalElement && counts.total !== undefined) {
        totalElement.textContent = counts.total;
    }
}

// Utility functions (moved to NotificationDetailsModal class)

// Modern confirmation modal functions
function showConfirmation(title, message, confirmText, confirmAction, type = 'warning') {
    const modal = document.getElementById('confirmationModal');
    const iconContainer = document.getElementById('confirmationIcon');
    const titleElement = document.getElementById('confirmationTitle');
    const messageElement = document.getElementById('confirmationMessage');
    const confirmBtn = document.getElementById('confirmationConfirmBtn');

    // Set content
    titleElement.textContent = title;
    messageElement.textContent = message;
    confirmBtn.textContent = confirmText;

    // Set icon and colors based on type
    let iconHTML = '';
    let iconBgClass = '';
    let btnClass = '';

    switch(type) {
        case 'danger':
            iconHTML = `<svg class="w-6 h-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>`;
            iconBgClass = 'bg-red-100';
            btnClass = 'bg-red-600 text-white hover:bg-red-700';
            break;
        case 'warning':
            iconHTML = `<svg class="w-6 h-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>`;
            iconBgClass = 'bg-yellow-100';
            btnClass = 'bg-yellow-600 text-white hover:bg-yellow-700';
            break;
        case 'info':
            iconHTML = `<svg class="w-6 h-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>`;
            iconBgClass = 'bg-blue-100';
            btnClass = 'bg-blue-600 text-white hover:bg-blue-700';
            break;
    }

    iconContainer.innerHTML = iconHTML;
    iconContainer.className = `flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full ${iconBgClass}`;
    confirmBtn.className = `flex-1 px-4 py-2 rounded-lg font-medium transition-colors ${btnClass}`;

    // Set confirm action
    confirmBtn.onclick = function() {
        closeConfirmationModal();
        confirmAction();
    };

    // Show modal
    modal.classList.remove('hidden');
}

function closeConfirmationModal() {
    document.getElementById('confirmationModal').classList.add('hidden');
}

// ===== NEW NOTIFICATION DETAILS MODAL SYSTEM =====
// Completely standalone implementation that doesn't conflict with header notifications

class NotificationDetailsModal {
    constructor() {
        this.modal = null;
        this.contentContainer = null;
        this.closeBtn = null;
        this.isOpen = false;
        this.currentNotificationId = null;

        this.init();
    }

    init() {
        this.modal = document.getElementById('notificationDetailsModal');
        this.contentContainer = document.getElementById('notificationDetailsContent');
        this.closeBtn = document.getElementById('closeNotificationDetailsBtn');

        if (!this.modal || !this.contentContainer || !this.closeBtn) {
            console.error('NotificationDetailsModal: Required elements not found');
            return;
        }

        this.bindEvents();
    }

    bindEvents() {
        // Close button
        this.closeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.close();
        });

        // Backdrop click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });

        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });

        // Prevent modal content clicks from closing modal
        const modalContent = this.modal.querySelector('.bg-secondary-800');
        if (modalContent) {
            modalContent.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    }

    async open(notificationId) {
        if (!notificationId) {
            console.error('NotificationDetailsModal: No notification ID provided');
            return;
        }

        this.currentNotificationId = notificationId;
        this.showLoadingState();
        this.modal.classList.remove('hidden');
        this.isOpen = true;

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        try {
            await this.loadNotificationData(notificationId);
        } catch (error) {
            console.error('Error loading notification:', error);
            this.showErrorState('Failed to load notification details. Please try again.');
        }
    }

    close() {
        if (!this.isOpen) return;

        this.modal.classList.add('hidden');
        this.isOpen = false;
        this.currentNotificationId = null;

        // Restore body scroll
        document.body.style.overflow = '';

        // Clear content
        this.contentContainer.innerHTML = '';
    }

    showLoadingState() {
        this.contentContainer.innerHTML = `
            <div class="flex items-center justify-center py-12">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-salon-gold"></div>
                <span class="ml-3 text-gray-300">Loading notification details...</span>
            </div>
        `;
    }

    showErrorState(message) {
        this.contentContainer.innerHTML = `
            <div class="text-center py-12">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">Error Loading Notification</h3>
                <p class="text-gray-400 mb-4">${this.escapeHtml(message)}</p>
                <button onclick="notificationModal.loadNotificationData('${this.currentNotificationId}')"
                        class="bg-salon-gold text-black px-4 py-2 rounded-lg hover:bg-gold-light transition-colors">
                    Try Again
                </button>
            </div>
        `;
    }

    async loadNotificationData(notificationId) {
        try {
            console.log('Loading notification data for ID:', notificationId);
            const url = `${basePath}/api/admin/notifications.php?id=${notificationId}`;
            console.log('Fetching from URL:', url);

            const response = await fetch(url);
            console.log('Response status:', response.status);

            const data = await response.json();
            console.log('Response data:', data);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
            }

            if (!data.success) {
                throw new Error(data.error || 'Failed to load notification');
            }

            const notification = data.data;
            console.log('Notification object:', notification);

            if (!notification) {
                throw new Error('No notification data received');
            }

            this.renderNotificationContent(notification);

            // Mark as read if not already read
            if (!notification.is_read) {
                this.markNotificationAsRead(notificationId);
            }

        } catch (error) {
            console.error('Error in loadNotificationData:', error);
            throw error;
        }
    }

    renderNotificationContent(notification) {
        console.log('Rendering notification:', notification); // Debug log

        const config = this.getNotificationConfig(notification.category || 'SYSTEM');
        const priorityBadge = this.getPriorityBadge(notification.priority || 'MEDIUM');
        const statusBadge = notification.is_read
            ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Read</span>'
            : '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Unread</span>';

        // Format the date properly
        let formattedDate = 'Unknown';
        if (notification.created_at) {
            try {
                const date = new Date(notification.created_at);
                if (!isNaN(date.getTime())) {
                    formattedDate = date.toLocaleString();
                }
            } catch (e) {
                console.error('Error formatting date:', e);
            }
        }

        // Ensure we have values for all fields
        const title = notification.title || 'No Title';
        const message = notification.message || 'No message content';
        const priority = notification.priority || 'MEDIUM';
        const category = notification.category || 'SYSTEM';

        this.contentContainer.innerHTML = `
            <div class="space-y-6">
                <!-- Header with icon and title -->
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 rounded-full bg-${config.color}-100 flex items-center justify-center">
                            <svg class="w-6 h-6 text-${config.color}-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${config.icon}" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h4 class="text-xl font-semibold text-white mb-2">${this.escapeHtml(title)}</h4>
                        <div class="flex flex-wrap items-center gap-2 mb-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${config.color}-100 text-${config.color}-800">
                                ${config.name}
                            </span>
                            ${priorityBadge}
                            ${statusBadge}
                        </div>
                    </div>
                </div>

                <!-- Message content -->
                <div class="bg-secondary-700 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-300 mb-2">Message</h5>
                    <p class="text-gray-200 leading-relaxed">${this.escapeHtml(message)}</p>
                </div>

                <!-- Details grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-secondary-700 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-gray-300 mb-2">Created</h5>
                        <p class="text-white">${formattedDate}</p>
                    </div>
                    <div class="bg-secondary-700 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-gray-300 mb-2">Priority</h5>
                        <p class="text-white">${priority}</p>
                    </div>
                    <div class="bg-secondary-700 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-gray-300 mb-2">Category</h5>
                        <p class="text-white">${category}</p>
                    </div>
                    <div class="bg-secondary-700 rounded-lg p-4">
                        <h5 class="text-sm font-medium text-gray-300 mb-2">Status</h5>
                        <p class="text-white">${notification.is_read ? 'Read' : 'Unread'}</p>
                    </div>
                </div>

                ${notification.action_url && notification.action_url.trim() ? `
                    <div class="border-t border-secondary-600 pt-4">
                        <a href="${this.escapeHtml(notification.action_url)}"
                           class="inline-flex items-center px-4 py-2 bg-salon-gold text-black rounded-lg hover:bg-gold-light transition-colors font-medium">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            View Related Item
                        </a>
                    </div>
                ` : ''}
            </div>
        `;
    }

    async markNotificationAsRead(notificationId) {
        try {
            const response = await fetch(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ is_read: true })
            });

            if (response.ok) {
                // Optionally refresh the page or update the UI
                console.log('Notification marked as read');
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    getNotificationConfig(category) {
        const configs = {
            'BOOKING': {
                name: 'Booking',
                color: 'blue',
                icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
            },
            'CUSTOMER': {
                name: 'Customer',
                color: 'green',
                icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z'
            },
            'STAFF': {
                name: 'Staff',
                color: 'purple',
                icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
            },
            'PAYMENT': {
                name: 'Payment',
                color: 'yellow',
                icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
            },
            'SYSTEM': {
                name: 'System',
                color: 'red',
                icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'
            },
            'MARKETING': {
                name: 'Marketing',
                color: 'pink',
                icon: 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'
            },
            'FEEDBACK': {
                name: 'Feedback',
                color: 'indigo',
                icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
            }
        };

        return configs[category] || configs['SYSTEM'];
    }

    getPriorityBadge(priority) {
        const badges = {
            'URGENT': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Urgent</span>',
            'HIGH': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">High</span>',
            'MEDIUM': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Medium</span>',
            'LOW': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Low</span>'
        };

        return badges[priority] || badges['MEDIUM'];
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the modal system
let notificationModal;
document.addEventListener('DOMContentLoaded', function() {
    notificationModal = new NotificationDetailsModal();
});

// Global function to open notification details (called from the View buttons)
function openNotificationDetails(notificationId) {
    if (notificationModal) {
        notificationModal.open(notificationId);
    } else {
        console.error('Notification modal not initialized');
    }
}

// Keep the existing markAsRead function for other buttons
function markAsRead(notificationId) {
    fetch(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_read: true })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
        showToast('Failed to mark notification as read', 'error');
    });
}

function markAsUnread(notificationId) {
    fetch(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_read: false })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error marking notification as unread:', error);
        showToast('Failed to mark notification as unread', 'error');
    });
}

function deleteNotification(notificationId) {
    showConfirmation(
        'Delete Notification',
        'Are you sure you want to delete this notification? This action cannot be undone.',
        'Delete',
        function() {
            fetch(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('Notification deleted successfully', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('Failed to delete notification', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting notification:', error);
                showToast('Failed to delete notification', 'error');
            });
        },
        'danger'
    );
}

function markAllPageNotificationsAsRead() {
    showConfirmation(
        'Mark All as Read',
        'Are you sure you want to mark all notifications as read?',
        'Mark All Read',
        function() {
            fetch(`${basePath}/api/admin/notifications-bulk.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'mark_read',
                    category: '<?= $category !== 'all' ? $category : '' ?>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('All notifications marked as read', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('Failed to mark notifications as read', 'error');
                }
            })
            .catch(error => {
                console.error('Error marking all as read:', error);
                showToast('Failed to mark all notifications as read', 'error');
            });
        },
        'info'
    );
}

// Bulk actions
function showBulkActions() {
    document.getElementById('bulkActionsModal').classList.remove('hidden');
}

function closeBulkActionsModal() {
    document.getElementById('bulkActionsModal').classList.add('hidden');
}

function getSelectedNotifications() {
    const checkboxes = document.querySelectorAll('.notification-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function bulkMarkAsRead() {
    const selectedIds = getSelectedNotifications();
    if (selectedIds.length === 0) {
        showToast('Please select notifications first', 'warning');
        return;
    }

    fetch(`${basePath}/api/admin/notifications-bulk.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_read',
            notification_ids: selectedIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeBulkActionsModal();
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error in bulk mark as read:', error);
        showToast('Failed to mark notifications as read', 'error');
    });
}

function bulkMarkAsUnread() {
    const selectedIds = getSelectedNotifications();
    if (selectedIds.length === 0) {
        showToast('Please select notifications first', 'warning');
        return;
    }

    fetch(`${basePath}/api/admin/notifications-bulk.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_unread',
            notification_ids: selectedIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeBulkActionsModal();
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error in bulk mark as unread:', error);
        showToast('Failed to mark notifications as unread', 'error');
    });
}

function bulkDelete() {
    const selectedIds = getSelectedNotifications();
    if (selectedIds.length === 0) {
        showToast('Please select notifications first', 'warning');
        return;
    }

    showConfirmation(
        'Delete Selected Notifications',
        `Are you sure you want to delete ${selectedIds.length} notification${selectedIds.length > 1 ? 's' : ''}? This action cannot be undone.`,
        'Delete All',
        function() {
            fetch(`${basePath}/api/admin/notifications-bulk.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'delete',
                    notification_ids: selectedIds
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeBulkActionsModal();
                    showToast(`${selectedIds.length} notification${selectedIds.length > 1 ? 's' : ''} deleted successfully`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showToast('Failed to delete notifications', 'error');
                }
            })
            .catch(error => {
                console.error('Error in bulk delete:', error);
                showToast('Failed to delete notifications', 'error');
            });
        },
        'danger'
    );
}

// Checkbox management
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.notification-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// Notification configuration helper (moved to NotificationDetailsModal class)

// Enhanced toast notification function
function showToast(message, type = 'info', duration = 4000) {
    const container = document.getElementById('toastContainer');

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `flex items-center p-4 rounded-lg shadow-lg border transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;

    // Set colors and icon based on type
    let bgClass, borderClass, textClass, iconHTML;

    switch(type) {
        case 'success':
            bgClass = 'bg-green-800';
            borderClass = 'border-green-600';
            textClass = 'text-green-100';
            iconHTML = `<svg class="w-5 h-5 text-green-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>`;
            break;
        case 'error':
            bgClass = 'bg-red-800';
            borderClass = 'border-red-600';
            textClass = 'text-red-100';
            iconHTML = `<svg class="w-5 h-5 text-red-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>`;
            break;
        case 'warning':
            bgClass = 'bg-yellow-800';
            borderClass = 'border-yellow-600';
            textClass = 'text-yellow-100';
            iconHTML = `<svg class="w-5 h-5 text-yellow-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>`;
            break;
        case 'info':
        default:
            bgClass = 'bg-blue-800';
            borderClass = 'border-blue-600';
            textClass = 'text-blue-100';
            iconHTML = `<svg class="w-5 h-5 text-blue-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>`;
            break;
    }

    toast.classList.add(bgClass, borderClass, textClass);

    toast.innerHTML = `
        ${iconHTML}
        <div class="flex-1">
            <p class="font-medium">${message}</p>
        </div>
        <button onclick="this.parentElement.remove()" class="ml-3 text-gray-400 hover:text-white transition-colors">
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    `;

    container.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
    }, 100);

    // Auto remove after duration
    setTimeout(() => {
        if (toast.parentElement) {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }
    }, duration);
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
