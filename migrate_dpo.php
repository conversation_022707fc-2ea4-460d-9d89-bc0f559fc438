<?php
/**
 * DPO Pay Migration Script
 * Run this script to add DPO Pay support to the database
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>DPO Pay Migration</h1>";
echo "<p>Adding DPO Pay support to the payment system...</p>";

try {
    global $database;
    
    echo "<h2>1. Updating payment_gateway enum</h2>";
    
    // Update payment_gateway enum to include DPO
    $database->execute("
        ALTER TABLE payments 
        MODIFY COLUMN payment_gateway ENUM('DPO', 'STRIPE', 'FLUTTERWAVE') DEFAULT 'DPO'
    ");
    echo "<p style='color: green;'>✓ Updated payment_gateway enum to include DPO</p>";
    
    echo "<h2>2. Adding dpo_token column</h2>";
    
    // Add dpo_token column
    try {
        $database->execute("
            ALTER TABLE payments 
            ADD COLUMN dpo_token VARCHAR(255) NULL AFTER stripe_payment_id
        ");
        echo "<p style='color: green;'>✓ Added dpo_token column</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: orange;'>⚠ dpo_token column already exists</p>";
        } else {
            throw $e;
        }
    }
    
    echo "<h2>3. Updating payment_logs table</h2>";
    
    // Update payment_logs gateway enum
    try {
        $database->execute("
            ALTER TABLE payment_logs 
            MODIFY COLUMN gateway ENUM('DPO', 'STRIPE', 'FLUTTERWAVE') NOT NULL
        ");
        echo "<p style='color: green;'>✓ Updated payment_logs gateway enum</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ payment_logs table may not exist or already updated: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>4. Updating payment_webhooks table</h2>";
    
    // Update payment_webhooks gateway enum
    try {
        $database->execute("
            ALTER TABLE payment_webhooks 
            MODIFY COLUMN gateway ENUM('DPO', 'STRIPE', 'FLUTTERWAVE') NOT NULL
        ");
        echo "<p style='color: green;'>✓ Updated payment_webhooks gateway enum</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ payment_webhooks table may not exist or already updated: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Adding indexes</h2>";
    
    // Add index for dpo_token
    try {
        $database->execute("CREATE INDEX idx_payments_dpo_token ON payments(dpo_token)");
        echo "<p style='color: green;'>✓ Added index for dpo_token</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: orange;'>⚠ Index for dpo_token already exists</p>";
        } else {
            throw $e;
        }
    }
    
    echo "<h2>6. Updating existing records</h2>";
    
    // Update existing Stripe payments to DPO for TZS currency
    $updated = $database->execute("
        UPDATE payments 
        SET payment_gateway = 'DPO' 
        WHERE payment_gateway = 'STRIPE' AND currency = 'TZS'
    ");
    echo "<p style='color: green;'>✓ Updated existing TZS payments to use DPO gateway</p>";
    
    // Ensure all payments use TZS currency
    $currencyUpdated = $database->execute("
        UPDATE payments 
        SET currency = 'TZS' 
        WHERE currency IN ('USD', 'NGN') OR currency IS NULL
    ");
    echo "<p style='color: green;'>✓ Updated currency to TZS for all payments</p>";
    
    echo "<h2>7. Creating settings table (if needed)</h2>";
    
    // Create settings table if it doesn't exist
    try {
        $database->execute("
            CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                key_name VARCHAR(255) UNIQUE NOT NULL,
                key_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        echo "<p style='color: green;'>✓ Settings table created/verified</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Settings table issue: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>8. Verification</h2>";
    
    // Verify the changes
    $paymentCount = $database->fetch("SELECT COUNT(*) as count FROM payments WHERE payment_gateway = 'DPO'");
    echo "<p style='color: blue;'>ℹ Total DPO payments: " . $paymentCount['count'] . "</p>";
    
    $columns = $database->fetchAll("SHOW COLUMNS FROM payments LIKE 'dpo_token'");
    if (!empty($columns)) {
        echo "<p style='color: green;'>✓ dpo_token column exists</p>";
    } else {
        echo "<p style='color: red;'>✗ dpo_token column missing</p>";
    }
    
    echo "<h2 style='color: green;'>Migration Completed Successfully!</h2>";
    echo "<p>DPO Pay support has been added to your payment system.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Update your DPO Pay credentials in config/app.php</li>";
    echo "<li>Test the payment flow with DPO Pay sandbox</li>";
    echo "<li>Configure your DPO Pay account settings</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Migration Failed</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>
