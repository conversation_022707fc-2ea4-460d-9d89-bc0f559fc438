<?php
/**
 * Admin Customers Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle CSV export
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    $search = sanitize($_GET['search'] ?? '');
    $filters = ['search' => $search];
    exportCustomersCSV($filters);
    exit;
}

// Handle form actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $customerId = $_POST['customer_id'] ?? '';

    if ($action === 'update_points' && $customerId) {
        $pointsAction = $_POST['points_action'] ?? '';
        $points = (int)($_POST['points'] ?? 0);

        $result = updateCustomerPoints($customerId, $pointsAction, $points);

        if ($result['success']) {
            $_SESSION['success'] = 'Customer points updated successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/customers');
    } elseif ($action === 'delete' && $customerId) {
        $result = deleteCustomer($customerId);

        if ($result['success']) {
            $_SESSION['success'] = 'Customer deleted successfully!';
        } else {
            $_SESSION['error'] = $result['error'];
        }

        redirect('/admin/customers');
    }
}

// Get customers with pagination and filters
$page = (int)($_GET['page'] ?? 1);
$limit = 15;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$sortBy = sanitize($_GET['sort'] ?? 'created_at');
$sortOrder = sanitize($_GET['order'] ?? 'DESC');

$whereClause = "WHERE role = 'CUSTOMER'";
$params = [];

if ($search) {
    $whereClause .= " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

$allowedSorts = ['name', 'email', 'created_at', 'points', 'total_bookings', 'total_spent'];
if (!in_array($sortBy, $allowedSorts)) {
    $sortBy = 'created_at';
}

$allowedOrders = ['ASC', 'DESC'];
if (!in_array($sortOrder, $allowedOrders)) {
    $sortOrder = 'DESC';
}

$customers = $database->fetchAll(
    "SELECT u.id, u.name, u.email, u.email_verified_at, u.image, u.password, u.phone,
            u.date_of_birth, u.role, u.points, u.referral_code, u.referred_by,
            u.created_at, u.updated_at,
            COUNT(DISTINCT b.id) as total_bookings,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent,
            MAX(b.date) as last_booking_date
     FROM users u
     LEFT JOIN bookings b ON u.id = b.user_id
     $whereClause
     GROUP BY u.id, u.name, u.email, u.email_verified_at, u.image, u.password, u.phone,
              u.date_of_birth, u.role, u.points, u.referral_code, u.referred_by,
              u.created_at, u.updated_at
     ORDER BY $sortBy $sortOrder
     LIMIT $limit OFFSET $offset",
    $params
);

$totalCustomers = $database->fetch(
    "SELECT COUNT(*) as count FROM users $whereClause",
    $params
)['count'];

$totalPages = ceil($totalCustomers / $limit);

// Get customer statistics
$stats = getCustomerStats();

// Handle messages
$message = '';
$messageType = '';
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    $messageType = 'success';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = $_SESSION['error'];
    $messageType = 'error';
    unset($_SESSION['error']);
}

$pageTitle = "Customers Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-white">Customers Management</h1>
                            <p class="mt-1 text-sm text-gray-300">Manage customer accounts, loyalty points, and booking history</p>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <button onclick="exportCustomers()" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors mr-2">
                                Export CSV
                            </button>
                            <a href="<?= getBasePath() ?>/admin/customers/create.php"
                               class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                Add Customer
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Total Customers</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['total']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">New This Month</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['new_this_month']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Avg. Lifetime Value</dt>
                                    <dd class="text-lg font-medium text-white"><?= formatCurrency($stats['avg_lifetime_value']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="bg-secondary-800 rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-300 truncate">Total Points Issued</dt>
                                    <dd class="text-lg font-medium text-white"><?= number_format($stats['total_points']) ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Search by name, email, or phone..." 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="sort" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="created_at" <?= $sortBy === 'created_at' ? 'selected' : '' ?>>Date Joined</option>
                                <option value="name" <?= $sortBy === 'name' ? 'selected' : '' ?>>Name</option>
                                <option value="total_spent" <?= $sortBy === 'total_spent' ? 'selected' : '' ?>>Total Spent</option>
                                <option value="total_bookings" <?= $sortBy === 'total_bookings' ? 'selected' : '' ?>>Total Bookings</option>
                                <option value="points" <?= $sortBy === 'points' ? 'selected' : '' ?>>Points</option>
                            </select>
                        </div>
                        <div>
                            <select name="order" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="DESC" <?= $sortOrder === 'DESC' ? 'selected' : '' ?>>Descending</option>
                                <option value="ASC" <?= $sortOrder === 'ASC' ? 'selected' : '' ?>>Ascending</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if ($search || $sortBy !== 'created_at' || $sortOrder !== 'DESC'): ?>
                            <a href="<?= getBasePath() ?>/admin/customers" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Reset
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Customers Table -->
                <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-secondary-700">
                            <thead class="bg-secondary-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Contact</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Bookings</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Total Spent</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Points</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Booking</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                                <?php foreach ($customers as $customer): ?>
                                    <tr class="hover:bg-secondary-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div class="h-10 w-10 rounded-full bg-salon-gold flex items-center justify-center">
                                                        <span class="text-sm font-medium text-black">
                                                            <?= strtoupper(substr($customer['name'], 0, 2)) ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-white"><?= htmlspecialchars($customer['name']) ?></div>
                                                    <div class="text-sm text-gray-300">
                                                        Joined <?= date('M j, Y', strtotime($customer['created_at'])) ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-white"><?= htmlspecialchars($customer['email']) ?></div>
                                            <?php if ($customer['phone']): ?>
                                                <div class="text-sm text-gray-300"><?= htmlspecialchars($customer['phone']) ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-white"><?= number_format($customer['total_bookings']) ?></div>
                                            <div class="text-sm text-gray-300">bookings</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-salon-gold"><?= formatCurrency($customer['total_spent']) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-purple-400"><?= number_format($customer['points']) ?></div>
                                            <div class="text-sm text-gray-300">points</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($customer['last_booking_date']): ?>
                                                <div class="text-sm text-white"><?= date('M j, Y', strtotime($customer['last_booking_date'])) ?></div>
                                            <?php else: ?>
                                                <div class="text-sm text-gray-400">No bookings</div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex gap-2">
                                                <button onclick="viewCustomer('<?= $customer['id'] ?>')"
                                                        class="text-blue-400 hover:text-blue-300">
                                                    View
                                                </button>
                                                <button onclick="editPoints('<?= $customer['id'] ?>', '<?= htmlspecialchars($customer['name']) ?>', <?= $customer['points'] ?>)"
                                                        class="text-salon-gold hover:text-gold-light">
                                                    Points
                                                </button>
                                                <button onclick="sendMessage('<?= $customer['id'] ?>')"
                                                        class="text-green-400 hover:text-green-300">
                                                    Message
                                                </button>
                                                <?php if ((int)$customer['total_bookings'] == 0 || !$customer['last_booking_date'] || strtotime($customer['last_booking_date']) < strtotime('-6 months')): ?>
                                                    <button onclick="deleteCustomer('<?= $customer['id'] ?>')"
                                                            class="text-red-400 hover:text-red-300">
                                                        Delete
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="bg-secondary-700 px-4 py-3 flex items-center justify-between border-t border-secondary-600 sm:px-6">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>" 
                                       class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>" 
                                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-300">
                                        Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalCustomers) ?></span> of <span class="font-medium"><?= $totalCustomers ?></span> results
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <?php for ($i = 1; $i <= min($totalPages, 10); $i++): ?>
                                            <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>" 
                                               class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                                <?= $i ?>
                                            </a>
                                        <?php endfor; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Points Edit Modal -->
<div id="pointsModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">Edit Customer Points</h2>
            <button onclick="closePointsModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="pointsForm" method="POST">
            <input type="hidden" name="customer_id" id="pointsCustomerId">
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Customer</label>
                <div id="pointsCustomerName" class="text-white font-medium"></div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Current Points</label>
                <div id="currentPoints" class="text-salon-gold font-medium text-lg"></div>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Action</label>
                <select name="action" id="pointsAction" required 
                        class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    <option value="add">Add Points</option>
                    <option value="subtract">Subtract Points</option>
                    <option value="set">Set Points</option>
                </select>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Points Amount</label>
                <input type="number" name="points" id="pointsAmount" min="0" required 
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-300 mb-2">Reason (Optional)</label>
                <textarea name="reason" id="pointsReason" rows="2" 
                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                          placeholder="Reason for points adjustment..."></textarea>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Update Points
                </button>
                <button type="button" onclick="closePointsModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function viewCustomer(customerId) {
    window.location.href = `<?= getBasePath() ?>/admin/customers/view.php?id=${customerId}`;
}

function deleteCustomer(customerId) {
    if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="customer_id" value="${customerId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function editPoints(customerId, customerName, currentPoints) {
    document.getElementById('pointsCustomerId').value = customerId;
    document.getElementById('pointsCustomerName').textContent = customerName;
    document.getElementById('currentPoints').textContent = currentPoints.toLocaleString() + ' points';
    document.getElementById('pointsAmount').value = '';
    document.getElementById('pointsReason').value = '';
    document.getElementById('pointsModal').classList.remove('hidden');
}

function closePointsModal() {
    document.getElementById('pointsModal').classList.add('hidden');
}

function sendMessage(customerId) {
    window.location.href = `<?= getBasePath() ?>/admin/customers/message.php?id=${customerId}`;
}

function exportCustomers() {
    window.location.href = `<?= getBasePath() ?>/admin/customers/?export=csv&search=<?= urlencode($search) ?>&sort=<?= urlencode($sortBy) ?>&order=<?= urlencode($sortOrder) ?>`;
}

// Handle points form submission
document.getElementById('pointsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('action', 'update_points');
    formData.append('points_action', document.getElementById('pointsAction').value);
    formData.append('points', document.getElementById('pointsAmount').value);

    // Submit to current page
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePointsModal();
    }
});

// Close modal on backdrop click
document.getElementById('pointsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePointsModal();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
