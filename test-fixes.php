<?php
/**
 * Test Fixes Script
 * Tests both staff suggestions and booking functionality
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Testing Staff Suggestions and Booking Fixes</h1>";

try {
    echo "<h2>1. Testing Staff Suggestions API</h2>";
    
    // Get a test service
    $testService = $database->fetch("SELECT id, name FROM services WHERE is_active = 1 LIMIT 1");
    if ($testService) {
        echo "<p>Testing with service: <strong>" . htmlspecialchars($testService['name']) . "</strong></p>";
        
        // Test the API directly
        $requestData = ['service_id' => $testService['id']];
        
        // Simulate the API call
        $serviceId = $testService['id'];
        $packageId = null;
        
        if ($serviceId) {
            // Get staff with specialties in this service (same query as API)
            $staff = $database->fetchAll("
                SELECT DISTINCT
                    u.id,
                    u.name,
                    u.email,
                    u.phone,
                    u.image,
                    u.role,
                    u.is_active,
                    u.created_at,
                    u.updated_at,
                    MAX(ss.proficiency_level) as proficiency_level,
                    GROUP_CONCAT(
                        CONCAT(s2.name, ' (', ss2.proficiency_level, ')')
                        SEPARATOR '|'
                    ) as all_specialties,
                    CASE
                        WHEN MAX(ss.proficiency_level) = 'EXPERT' THEN 1
                        WHEN MAX(ss.proficiency_level) = 'ADVANCED' THEN 2
                        WHEN MAX(ss.proficiency_level) = 'INTERMEDIATE' THEN 3
                        WHEN MAX(ss.proficiency_level) = 'BEGINNER' THEN 4
                        ELSE 5
                    END as ranking
                FROM users u
                LEFT JOIN staff_specialties ss ON u.id = ss.user_id AND ss.service_id = ?
                LEFT JOIN staff_specialties ss2 ON u.id = ss2.user_id
                LEFT JOIN services s2 ON ss2.service_id = s2.id
                WHERE u.role = 'STAFF' AND u.is_active = 1
                GROUP BY u.id, u.name, u.email, u.phone, u.image, u.role, u.is_active, u.created_at, u.updated_at
                ORDER BY
                    ranking ASC,
                    u.name ASC
            ", [$serviceId]);
            
            echo "<h3>Staff Found: " . count($staff) . "</h3>";
            
            if (empty($staff)) {
                echo "<p style='color: orange;'>⚠️ No staff found with specialties, testing fallback...</p>";
                
                // Test fallback query
                $staff = $database->fetchAll("
                    SELECT u.*, 5 as ranking
                    FROM users u
                    WHERE u.role = 'STAFF' AND u.is_active = 1
                    ORDER BY u.name ASC
                ");
                
                echo "<p>Fallback staff found: " . count($staff) . "</p>";
            }
            
            if (!empty($staff)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>Name</th><th>Email</th><th>Active</th><th>Ranking</th></tr>";
                foreach (array_slice($staff, 0, 5) as $member) { // Show first 5
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($member['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($member['email']) . "</td>";
                    echo "<td>" . ($member['is_active'] ? '✅ Yes' : '❌ No') . "</td>";
                    echo "<td>" . $member['ranking'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "<p style='color: green;'>✅ Staff suggestions should now work!</p>";
            } else {
                echo "<p style='color: red;'>❌ No active staff found at all</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ No active services found for testing</p>";
    }
    
    echo "<h2>2. Testing Point Transactions Table Structure</h2>";
    
    // Check table structure
    $columns = $database->fetchAll("DESCRIBE point_transactions");
    $hasBookingId = false;
    $hasRewardId = false;
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'booking_id') {
            $hasBookingId = true;
        }
        if ($column['Field'] === 'reward_id') {
            $hasRewardId = true;
        }
    }
    echo "</table>";
    
    if ($hasBookingId) {
        echo "<p style='color: green;'>✅ booking_id column exists</p>";
    } else {
        echo "<p style='color: red;'>❌ booking_id column missing - run fix-booking-errors.php</p>";
    }
    
    if ($hasRewardId) {
        echo "<p style='color: green;'>✅ reward_id column exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ reward_id column missing (optional)</p>";
    }
    
    echo "<h2>3. Testing Point Transaction Insert</h2>";
    
    // Test point transaction insert
    try {
        $testId = generateUUID();
        $testUserId = $database->fetch("SELECT id FROM users WHERE role = 'CUSTOMER' LIMIT 1")['id'] ?? null;
        
        if ($testUserId) {
            if ($hasBookingId) {
                // Test with booking_id
                $database->execute("
                    INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
                    VALUES (?, ?, ?, ?, 'EARNED', 'Test transaction', NOW())
                ", [$testId, $testUserId, null, 10]);
                echo "<p style='color: green;'>✅ Point transaction with booking_id works</p>";
            } else {
                // Test without booking_id
                $database->execute("
                    INSERT INTO point_transactions (id, user_id, points, type, description, created_at)
                    VALUES (?, ?, ?, 'EARNED', 'Test transaction', NOW())
                ", [$testId, $testUserId, 10]);
                echo "<p style='color: green;'>✅ Point transaction without booking_id works</p>";
            }
            
            // Clean up test transaction
            $database->execute("DELETE FROM point_transactions WHERE id = ?", [$testId]);
            echo "<p>Test transaction cleaned up</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ No customer found for testing point transactions</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Point transaction test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h2>4. Staff Specialties Check</h2>";
    
    // Check if staff have specialties
    $staffWithSpecialties = $database->fetch("
        SELECT COUNT(DISTINCT ss.user_id) as staff_count
        FROM staff_specialties ss
        JOIN users u ON ss.user_id = u.id
        WHERE u.role = 'STAFF' AND u.is_active = 1
    ");
    
    $totalActiveStaff = $database->fetch("
        SELECT COUNT(*) as total_count
        FROM users
        WHERE role = 'STAFF' AND is_active = 1
    ");
    
    echo "<p>Active staff with specialties: <strong>" . $staffWithSpecialties['staff_count'] . "</strong></p>";
    echo "<p>Total active staff: <strong>" . $totalActiveStaff['total_count'] . "</strong></p>";
    
    if ($staffWithSpecialties['staff_count'] == 0) {
        echo "<p style='color: orange;'>⚠️ No staff have specialties assigned. The API will use fallback mode.</p>";
        echo "<p>Consider running fix-staff-specialties.php to assign default specialties.</p>";
    } else {
        echo "<p style='color: green;'>✅ Staff have specialties assigned</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Summary</h2>";
echo "<ul>";
echo "<li><strong>Staff Suggestions:</strong> Fixed with fallback for missing specialties</li>";
echo "<li><strong>Booking Errors:</strong> Added fallbacks for missing booking_id column</li>";
echo "<li><strong>Database Structure:</strong> Can be fixed with fix-booking-errors.php</li>";
echo "</ul>";

echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>If booking_id column is missing, run <a href='fix-booking-errors.php'>fix-booking-errors.php</a></li>";
echo "<li>Test booking creation from customer panel</li>";
echo "<li>Test staff suggestions from booking page</li>";
echo "<li>Monitor error logs for any remaining issues</li>";
echo "</ol>";

echo "<p><a href='debug-staff-issues.php'>← Back to Staff Diagnostic Script</a></p>";
?>
