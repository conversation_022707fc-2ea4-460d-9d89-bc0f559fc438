<?php
/**
 * Gallery Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get all active gallery images grouped by category
$galleryImages = $database->fetchAll("
    SELECT * FROM gallery
    WHERE is_active = 1
    ORDER BY category ASC, created_at DESC
");

// Group images by category
$imagesByCategory = [];
foreach ($galleryImages as $image) {
    $imagesByCategory[$image['category']][] = $image;
}

$pageTitle = "Gallery";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Luxury Contact Card Styles for Consistency */
.luxury-contact-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.luxury-contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.luxury-contact-card:hover::before {
    left: 100%;
}

.luxury-contact-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

/* Animation classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    animation: animate-on-scroll 0.8s ease-out forwards;
    animation-delay: var(--delay, 0s);
}

@keyframes animate-on-scroll {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

    <!-- Hero Section -->
    <section class="relative py-32 bg-gradient-to-br from-salon-black via-salon-black to-salon-black overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>

        <!-- Parallax Background -->
        <div class="absolute inset-0 opacity-10">
            <div class="h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-6 text-center">
            <!-- Luxury Badge -->
            <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
                <i class="fas fa-camera mr-2"></i>
                Our Work Gallery
            </div>

            <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
                Beauty
                <span class="block md:inline text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                    Gallery
                </span>
            </h1>

            <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
                Discover our portfolio of stunning transformations and witness the artistry that makes Flix Salon & SPA the premier destination for luxury beauty services.
                <span class="block mt-4 text-salon-gold text-lg md:text-xl">Every picture tells a story of transformation</span>
            </p>

            <!-- Gallery Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
                <div class="luxury-contact-card rounded-xl p-6 text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-images text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-white font-semibold mb-2"><?= count($galleryImages) ?>+</h3>
                    <p class="text-gray-400 text-sm">Transformations</p>
                </div>

                <div class="luxury-contact-card rounded-xl p-6 text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-layer-group text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-white font-semibold mb-2"><?= count($imagesByCategory) ?>+</h3>
                    <p class="text-gray-400 text-sm">Categories</p>
                </div>

                <div class="luxury-contact-card rounded-xl p-6 text-center group">
                    <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-salon-gold/30 transition-colors">
                        <i class="fas fa-heart text-salon-gold text-2xl"></i>
                    </div>
                    <h3 class="text-white font-semibold mb-2">500+</h3>
                    <p class="text-gray-400 text-sm">Happy Clients</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Tabs -->
    <section class="py-16 bg-gradient-to-b from-secondary-900 to-salon-black relative">
        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/5"></div>
        <div class="relative max-w-7xl mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Explore Our <span class="text-salon-gold">Services</span>
                </h2>
                <p class="text-gray-300 max-w-2xl mx-auto">
                    Browse through our specialized categories to see transformations tailored to your style
                </p>
            </div>
            
            <!-- Enhanced Filter Buttons -->
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                <button onclick="filterGallery('all')" class="filter-btn active group relative px-8 py-4 bg-gradient-to-r from-salon-gold to-yellow-400 text-black rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-salon-gold/25 overflow-hidden">
                    <span class="relative z-10 flex items-center">
                        <i class="fas fa-grid-2 mr-2"></i>
                        All Work
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-salon-gold opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
                <?php foreach (array_keys($imagesByCategory) as $category): ?>
                    <button onclick="filterGallery('<?= strtolower(str_replace(' ', '-', $category)) ?>')" class="filter-btn group relative px-8 py-4 bg-gradient-to-r from-secondary-800 to-secondary-900 text-white rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-secondary-700 hover:border-salon-gold/50 shadow-lg hover:shadow-salon-gold/20 overflow-hidden">
                        <span class="relative z-10 flex items-center">
                            <?php
                            $icons = [
                                'Hair' => 'fas fa-cut',
                                'Makeup' => 'fas fa-palette',
                                'Nails' => 'fas fa-hand-paper',
                                'Facial' => 'fas fa-spa',
                                'Massage' => 'fas fa-hands',
                                'Eyebrows' => 'fas fa-eye'
                            ];
                            $icon = $icons[$category] ?? 'fas fa-star';
                            ?>
                            <i class="<?= $icon ?> mr-2"></i>
                            <?= htmlspecialchars($category) ?>
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/20 to-salon-gold/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                <?php endforeach; ?>
            </div>
            
            <!-- Results Count -->
            <div class="text-center">
                <span id="resultsCount" class="text-sm text-gray-400 bg-secondary-800/50 px-4 py-2 rounded-full backdrop-blur-sm">
                    Showing <?= count($galleryImages) ?> transformations
                </span>
            </div>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="py-20 bg-salon-black relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
        </div>
        
        <div class="relative max-w-7xl mx-auto px-6">
            <!-- Masonry Grid with Modern Design -->
            <div class="columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6" id="galleryGrid">
                <?php foreach ($imagesByCategory as $category => $images): ?>
                    <?php foreach ($images as $image): ?>
                        <div class="gallery-item group <?= strtolower(str_replace(' ', '-', $category)) ?> break-inside-avoid" data-category="<?= strtolower(str_replace(' ', '-', $category)) ?>">
                            <div class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-secondary-900/50 to-secondary-800/50 backdrop-blur-sm cursor-pointer transform transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-salon-gold/10 border border-secondary-700/50 hover:border-salon-gold/30" onclick="openLightbox('<?= htmlspecialchars($image['image_url']) ?>', '<?= htmlspecialchars($image['title']) ?>', '<?= htmlspecialchars($image['description']) ?>')">
                                <?php if ($image['image_url']): ?>
                                    <!-- Enhanced Lazy Loading Placeholder -->
                                    <div class="lazy-placeholder w-full bg-gradient-to-br from-salon-gold/10 via-secondary-900/50 to-secondary-800/50 flex items-center justify-center" style="aspect-ratio: <?= rand(3,4) ?>/<?= rand(4,5) ?>;">
                                        <div class="text-center">
                                            <div class="animate-pulse">
                                                <div class="w-16 h-16 bg-gradient-to-br from-salon-gold/30 to-salon-gold/10 rounded-full mx-auto mb-3 flex items-center justify-center">
                                                    <i class="fas fa-image text-salon-gold/60 text-xl"></i>
                                                </div>
                                                <div class="h-2 bg-salon-gold/20 rounded-full w-20 mx-auto mb-2"></div>
                                                <div class="h-1.5 bg-salon-gold/15 rounded-full w-16 mx-auto"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Enhanced Image -->
                                    <img
                                        data-src="<?= htmlspecialchars($image['image_url']) ?>"
                                        alt="<?= htmlspecialchars($image['title']) ?>"
                                        class="lazy-image w-full object-cover group-hover:scale-105 transition-all duration-700 opacity-0 filter group-hover:brightness-110"
                                        loading="lazy"
                                        style="aspect-ratio: <?= rand(3,4) ?>/<?= rand(4,5) ?>;"
                                    >
                                <?php else: ?>
                                    <div class="w-full bg-gradient-to-br from-salon-gold/20 to-secondary-900 flex items-center justify-center" style="aspect-ratio: 4/5;">
                                        <div class="text-center p-8">
                                            <i class="fas fa-image text-salon-gold/60 text-5xl mb-4"></i>
                                            <div class="text-gray-300 text-sm font-medium"><?= htmlspecialchars($category) ?></div>
                                            <div class="text-gray-500 text-xs mt-1">No image available</div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Enhanced Overlay with Modern Design -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex flex-col justify-end p-6">
                                    <div class="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                                        <!-- Category Badge -->
                                        <div class="inline-flex items-center bg-gradient-to-r from-salon-gold/90 to-yellow-400/90 backdrop-blur-sm text-black px-3 py-1.5 rounded-full text-xs font-bold mb-3 shadow-lg">
                                            <?php
                                            $icons = [
                                                'Hair' => 'fas fa-cut',
                                                'Makeup' => 'fas fa-palette',
                                                'Nails' => 'fas fa-hand-paper',
                                                'Facial' => 'fas fa-spa',
                                                'Massage' => 'fas fa-hands',
                                                'Eyebrows' => 'fas fa-eye'
                                            ];
                                            $icon = $icons[$category] ?? 'fas fa-star';
                                            ?>
                                            <i class="<?= $icon ?> mr-1.5"></i>
                                            <?= htmlspecialchars($category) ?>
                                        </div>
                                        
                                        <h3 class="text-lg font-bold text-white mb-2 leading-tight"><?= htmlspecialchars($image['title']) ?></h3>
                                        <p class="text-sm text-gray-200 mb-4 leading-relaxed line-clamp-2"><?= htmlspecialchars($image['description']) ?></p>
                                        
                                        <!-- View Button -->
                                        <div class="flex items-center text-salon-gold text-sm font-semibold">
                                            <span>View Details</span>
                                            <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Subtle Border Glow -->
                                <div class="absolute inset-0 rounded-2xl border border-salon-gold/0 group-hover:border-salon-gold/30 transition-all duration-500 pointer-events-none"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </div>
            
            <!-- No Results Message -->
            <div id="noResults" class="hidden text-center py-20">
                <div class="max-w-md mx-auto">
                    <div class="w-20 h-20 bg-gradient-to-br from-salon-gold/20 to-salon-gold/10 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-search text-salon-gold/60 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2">No Results Found</h3>
                    <p class="text-gray-400">Try selecting a different category to see more transformations.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-salon-black">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6">
                    Ready for Your <span class="text-salon-gold">Transformation</span>?
                </h2>
                <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                    Book your appointment today and let our expert team create your perfect look. Your beauty journey starts here.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/customer/book" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fas fa-calendar-plus mr-2"></i>
                        Book Appointment
                    </a>
                    <a href="<?= getBasePath() ?>/contact.php" class="bg-secondary-900 hover:bg-secondary-800 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-secondary-700">
                        <i class="fas fa-phone mr-2"></i>
                        Contact Us
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Lightbox Modal -->
    <div id="lightboxModal" class="fixed inset-0 bg-black/95 backdrop-blur-md hidden z-50 flex items-center justify-center p-4 transition-all duration-300">
        <div class="relative max-w-5xl w-full transform scale-95 transition-transform duration-300" id="lightboxContent">
            <!-- Close Button -->
            <button onclick="closeLightbox()" class="absolute top-6 right-6 w-12 h-12 bg-black/50 backdrop-blur-sm text-white hover:text-salon-gold text-xl z-20 rounded-full border border-white/10 hover:border-salon-gold/50 transition-all duration-300 flex items-center justify-center group">
                <i class="fas fa-times group-hover:rotate-90 transition-transform duration-300"></i>
            </button>
            
            <!-- Navigation Arrows -->
            <button onclick="navigateLightbox(-1)" class="absolute left-6 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 backdrop-blur-sm text-white hover:text-salon-gold text-xl z-20 rounded-full border border-white/10 hover:border-salon-gold/50 transition-all duration-300 flex items-center justify-center group">
                <i class="fas fa-chevron-left group-hover:-translate-x-1 transition-transform duration-300"></i>
            </button>
            <button onclick="navigateLightbox(1)" class="absolute right-6 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-black/50 backdrop-blur-sm text-white hover:text-salon-gold text-xl z-20 rounded-full border border-white/10 hover:border-salon-gold/50 transition-all duration-300 flex items-center justify-center group">
                <i class="fas fa-chevron-right group-hover:translate-x-1 transition-transform duration-300"></i>
            </button>
            
            <div class="bg-gradient-to-br from-secondary-900/90 to-secondary-800/90 backdrop-blur-sm rounded-2xl overflow-hidden border border-secondary-600/50 shadow-2xl">
                <!-- Image Container with Loading -->
                <div class="relative bg-black/20">
                    <div id="lightboxImageLoader" class="absolute inset-0 flex items-center justify-center">
                        <div class="animate-spin w-12 h-12 border-4 border-salon-gold/20 border-t-salon-gold rounded-full"></div>
                    </div>
                    <img id="lightboxImage" src="" alt="" class="w-full h-auto max-h-[75vh] object-contain transition-opacity duration-300 opacity-0">
                </div>
                
                <!-- Enhanced Content -->
                <div class="p-8">
                    <div class="flex flex-wrap items-start justify-between gap-4 mb-6">
                        <div class="flex-1 min-w-0">
                            <h3 id="lightboxTitle" class="text-2xl md:text-3xl font-bold font-serif text-white mb-3 leading-tight"></h3>
                            <p id="lightboxDescription" class="text-gray-300 text-lg leading-relaxed"></p>
                        </div>
                        <div id="lightboxCategory" class="flex-shrink-0 bg-gradient-to-r from-salon-gold to-yellow-400 text-black px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex flex-wrap gap-4 pt-6 border-t border-secondary-700/50">
                        <a href="<?= getBasePath() ?>/customer/book" class="flex-1 min-w-max bg-gradient-to-r from-salon-gold to-yellow-400 hover:from-yellow-400 hover:to-salon-gold text-black px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 text-center shadow-lg">
                            <i class="fas fa-calendar-plus mr-2"></i>
                            Book This Service
                        </a>
                        <button onclick="shareLightbox()" class="px-6 py-3 bg-secondary-800 hover:bg-secondary-700 text-white rounded-xl font-semibold transition-all duration-300 hover:scale-105 border border-secondary-600">
                            <i class="fas fa-share mr-2"></i>
                            Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    // Enhanced Gallery System with Modern Features
    class AdvancedGallery {
        constructor() {
            this.imageObserver = null;
            this.currentLightboxIndex = 0;
            this.currentImages = [];
            this.init();
        }

        init() {
            this.setupLazyLoading();
            this.setupIntersectionAnimations();
            this.updateImagesList();
        }

        setupLazyLoading() {
            if ('IntersectionObserver' in window) {
                this.imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.loadImage(entry.target);
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.01
                });

                this.observeImages();
            } else {
                this.loadAllImages();
            }
        }

        setupIntersectionAnimations() {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            document.querySelectorAll('.gallery-item').forEach(item => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                animationObserver.observe(item);
            });
        }

        observeImages() {
            const lazyImages = document.querySelectorAll('.lazy-image');
            lazyImages.forEach(img => {
                this.imageObserver.observe(img);
            });
        }

        loadImage(img) {
            const placeholder = img.previousElementSibling;
            const imageLoader = new Image();

            imageLoader.onload = () => {
                img.src = img.dataset.src;
                img.style.opacity = '1';

                if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                    placeholder.style.transition = 'opacity 0.4s ease-out';
                    placeholder.style.opacity = '0';
                    setTimeout(() => placeholder.style.display = 'none', 400);
                }

                img.classList.add('lazy-loaded');
            };

            imageLoader.onerror = () => {
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=';
                img.style.opacity = '1';
                if (placeholder && placeholder.classList.contains('lazy-placeholder')) {
                    placeholder.style.display = 'none';
                }
            };

            imageLoader.src = img.dataset.src;
        }

        loadAllImages() {
            const lazyImages = document.querySelectorAll('.lazy-image');
            lazyImages.forEach(img => this.loadImage(img));
        }

        updateImagesList() {
            const visibleItems = document.querySelectorAll('.gallery-item:not([style*="display: none"])');
            this.currentImages = Array.from(visibleItems).map(item => {
                const img = item.querySelector('.lazy-image');
                return {
                    src: img.dataset.src || img.src,
                    title: item.querySelector('h3') ? item.querySelector('h3').textContent : '',
                    description: item.querySelector('p') ? item.querySelector('p').textContent : '',
                    category: item.dataset.category
                };
            });
        }

        reinitialize() {
            if (this.imageObserver) {
                this.observeImages();
            }
            this.setupIntersectionAnimations();
            this.updateImagesList();
        }
    }

    // Initialize advanced gallery
    const gallery = new AdvancedGallery();

    function filterGallery(category) {
        const items = document.querySelectorAll('.gallery-item');
        const buttons = document.querySelectorAll('.filter-btn');
        const resultsCount = document.getElementById('resultsCount');
        const noResults = document.getElementById('noResults');

        // Update button states with smooth transitions
        buttons.forEach(btn => {
            btn.classList.remove('active');
            btn.classList.remove('bg-gradient-to-r', 'from-salon-gold', 'to-yellow-400', 'text-black');
            btn.classList.add('bg-gradient-to-r', 'from-secondary-800', 'to-secondary-900', 'text-white');
        });

        event.target.classList.add('active');
        event.target.classList.remove('bg-gradient-to-r', 'from-secondary-800', 'to-secondary-900', 'text-white');
        event.target.classList.add('bg-gradient-to-r', 'from-salon-gold', 'to-yellow-400', 'text-black');

        // Filter items with staggered animation
        let visibleCount = 0;
        let delay = 0;

        items.forEach((item, index) => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'block';
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0) scale(1)';
                }, delay);
                delay += 50;
                visibleCount++;
            } else {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px) scale(0.95)';
                setTimeout(() => {
                    item.style.display = 'none';
                }, 300);
            }
        });

        // Update results count
        if (visibleCount === 0) {
            noResults.classList.remove('hidden');
            resultsCount.classList.add('hidden');
        } else {
            noResults.classList.add('hidden');
            resultsCount.classList.remove('hidden');
            resultsCount.textContent = `Showing ${visibleCount} transformation${visibleCount !== 1 ? 's' : ''}`;
        }

        // Reinitialize gallery features
        setTimeout(() => {
            gallery.reinitialize();
        }, 400);
    }

    function openLightbox(imageUrl, title, description) {
        const modal = document.getElementById('lightboxModal');
        const content = document.getElementById('lightboxContent');
        const image = document.getElementById('lightboxImage');
        const loader = document.getElementById('lightboxImageLoader');
        
        // Find current image index
        gallery.updateImagesList();
        gallery.currentLightboxIndex = gallery.currentImages.findIndex(img => img.src === imageUrl);
        
        // Show modal with animation
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        setTimeout(() => {
            content.style.transform = 'scale(1)';
        }, 50);

        updateLightboxContent(imageUrl, title, description);
    }

    function updateLightboxContent(imageUrl, title, description) {
        const image = document.getElementById('lightboxImage');
        const loader = document.getElementById('lightboxImageLoader');
        const titleEl = document.getElementById('lightboxTitle');
        const descEl = document.getElementById('lightboxDescription');
        const categoryEl = document.getElementById('lightboxCategory');
        
        // Show loader
        loader.classList.remove('hidden');
        image.style.opacity = '0';
        
        // Update content
        titleEl.textContent = title;
        descEl.textContent = description;
        
        // Find category from current images
        const currentImage = gallery.currentImages[gallery.currentLightboxIndex];
        if (currentImage) {
            categoryEl.textContent = currentImage.category.charAt(0).toUpperCase() + currentImage.category.slice(1);
        }
        
        // Load image
        const imgLoader = new Image();
        imgLoader.onload = () => {
            image.src = imageUrl;
            loader.classList.add('hidden');
            image.style.opacity = '1';
        };
        imgLoader.src = imageUrl;
    }

    function navigateLightbox(direction) {
        if (gallery.currentImages.length === 0) return;
        
        gallery.currentLightboxIndex += direction;
        
        if (gallery.currentLightboxIndex >= gallery.currentImages.length) {
            gallery.currentLightboxIndex = 0;
        } else if (gallery.currentLightboxIndex < 0) {
            gallery.currentLightboxIndex = gallery.currentImages.length - 1;
        }
        
        const currentImage = gallery.currentImages[gallery.currentLightboxIndex];
        updateLightboxContent(currentImage.src, currentImage.title, currentImage.description);
    }

    function closeLightbox() {
        const modal = document.getElementById('lightboxModal');
        const content = document.getElementById('lightboxContent');
        
        content.style.transform = 'scale(0.95)';
        
        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }, 200);
    }

    function shareLightbox() {
        if (navigator.share) {
            navigator.share({
                title: 'Flix Salon & SPA - Gallery',
                text: 'Check out this amazing transformation!',
                url: window.location.href
            });
        } else {
            // Fallback: copy URL to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                // Show success message
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }
    }

    // Enhanced Keyboard Navigation
    document.addEventListener('keydown', function(e) {
        const modal = document.getElementById('lightboxModal');
        if (!modal.classList.contains('hidden')) {
            switch(e.key) {
                case 'Escape':
                    closeLightbox();
                    break;
                case 'ArrowLeft':
                    navigateLightbox(-1);
                    break;
                case 'ArrowRight':
                    navigateLightbox(1);
                    break;
            }
        }
    });

    // Close lightbox on backdrop click
    document.getElementById('lightboxModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeLightbox();
        }
    });

    // Enhanced CSS Animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px) scale(0.95); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }



        .lazy-image {
            transition: opacity 0.4s ease-in-out, transform 0.3s ease;
        }

        .lazy-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .lazy-loaded {
            z-index: 2;
        }

        .gallery-item {
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Masonry Grid Adjustments */
        .columns-1 { columns: 1; }
        .columns-2 { columns: 2; }
        .columns-3 { columns: 3; }
        .columns-4 { columns: 4; }

        @media (max-width: 640px) {
            .sm\\:columns-2 { columns: 2; }
        }

        @media (min-width: 1024px) {
            .lg\\:columns-3 { columns: 3; }
        }

        @media (min-width: 1280px) {
            .xl\\:columns-4 { columns: 4; }
        }
    `;
    document.head.appendChild(style);
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
