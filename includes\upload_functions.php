<?php
/**
 * File Upload Functions
 * Flix Salonce - PHP Version
 */

/**
 * Generic file upload function.
 * @param array $file The $_FILES['input_name'] array.
 * @param string $subdirectory The subdirectory within 'uploads/' (e.g., 'packages', 'blog', 'services').
 * @param array $allowedExtensions Allowed file extensions (e.g., ['jpg', 'png']).
 * @param int $maxSize Maximum file size in bytes.
 * @return array Result array with success status, filename/error, and URL.
 */
function uploadFile(array $file, string $subdirectory, array $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'], int $maxSize = 5 * 1024 * 1024): array {
    try {
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $phpFileUploadErrors = [
                UPLOAD_ERR_OK         => 'No errors.',
                UPLOAD_ERR_INI_SIZE   => 'Larger than upload_max_filesize.',
                UPLOAD_ERR_FORM_SIZE  => 'Larger than form MAX_FILE_SIZE.',
                UPLOAD_ERR_PARTIAL    => 'Partial upload.',
                UPLOAD_ERR_NO_FILE    => 'No file.',
                UPLOAD_ERR_NO_TMP_DIR => 'No temporary directory.',
                UPLOAD_ERR_CANT_WRITE => 'Can\'t write to disk.',
                UPLOAD_ERR_EXTENSION  => 'File upload stopped by a PHP extension.',
            ];
            $errorCode = $file['error'] ?? UPLOAD_ERR_NO_FILE;
            return ['success' => false, 'error' => $phpFileUploadErrors[$errorCode] ?? 'Unknown upload error'];
        }

        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, array_map('strtolower', $allowedExtensions))) {
            return ['success' => false, 'error' => 'Invalid file type. Allowed: ' . implode(', ', $allowedExtensions)];
        }

        if ($file['size'] > $maxSize) {
            return ['success' => false, 'error' => 'File size too large. Maximum size is ' . ($maxSize / 1024 / 1024) . 'MB'];
        }

        $uploadDir = __DIR__ . '/../uploads/' . trim($subdirectory, '/\\') . '/';
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                error_log("Failed to create upload directory: " . $uploadDir);
                return ['success' => false, 'error' => 'Failed to create upload directory. Check server permissions.'];
            }
        }
        
        // Ensure generateUUID function is available
        if (!function_exists('generateUUID')) {
            // Attempt to include it if it's in a common functions file
            if (file_exists(__DIR__ . '/functions.php')) {
                require_once __DIR__ . '/functions.php';
            }
            if (!function_exists('generateUUID')) {
                 // Fallback or error if still not found
                return ['success' => false, 'error' => 'generateUUID function not found.'];
            }
        }


        $filename = generateUUID() . '.' . $fileExtension;
        $filepath = $uploadDir . $filename;

        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            error_log("Failed to move uploaded file: from " . $file['tmp_name'] . " to " . $filepath);
            return ['success' => false, 'error' => 'Failed to move uploaded file. Check server permissions and paths.'];
        }

        return [
            'success' => true,
            'filename' => $filename, // Just the filename, not the full path
            'filepath' => $filepath, // Full server path
            'url' => getBasePath() . '/uploads/' . trim($subdirectory, '/\\') . '/' . $filename
        ];

    } catch (Exception $e) {
        error_log("File upload error in subdirectory '$subdirectory': " . $e->getMessage());
        return ['success' => false, 'error' => 'Upload failed due to a server error.'];
    }
}

/**
 * Generic function to delete an uploaded file.
 * @param string $filename The name of the file to delete.
 * @param string $subdirectory The subdirectory within 'uploads/' where the file is located.
 * @return bool True on success or if file doesn't exist, false on failure.
 */
function deleteUploadedFile(string $filename, string $subdirectory): bool {
    try {
        if (empty($filename)) {
            return true; // Nothing to delete
        }

        // Don't delete if it's a URL
        if (filter_var($filename, FILTER_VALIDATE_URL)) {
            return true;
        }

        $filepath = __DIR__ . '/../uploads/' . trim($subdirectory, '/\\') . '/' . $filename;

        if (file_exists($filepath)) {
            if (is_writable($filepath)) {
                return unlink($filepath);
            } else {
                error_log("File not writable, cannot delete: " . $filepath);
                return false; // File exists but cannot be deleted
            }
        }
        return true; // File doesn't exist, consider it deleted
    } catch (Exception $e) {
        error_log("File deletion error for '$filename' in '$subdirectory': " . $e->getMessage());
        return false;
    }
}


/**
 * Upload package image file (uses generic uploadFile)
 */
function uploadPackageImage($file) {
    return uploadFile($file, 'packages', ['jpeg', 'jpg', 'png', 'gif', 'webp']);
}

/**
 * Delete package image file (uses generic deleteUploadedFile)
 */
function deletePackageImage($filename) {
    return deleteUploadedFile($filename, 'packages');
}

/**
 * Get package image URL
 */
function getPackageImageUrl($image) {
    if (empty($image)) {
        return '';
    }
    
    // If it's already a URL, return as is
    if (filter_var($image, FILTER_VALIDATE_URL)) {
        return $image;
    }
    
    // If it's a filename, construct the URL
    return getBasePath() . '/uploads/packages/' . $image;
}

/**
 * Validate image file
 */
function validateImageFile($file) {
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = 'No file uploaded or upload error occurred';
        return $errors;
    }
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        $errors[] = 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed';
    }
    
    // Validate file size (5MB max)
    $maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if ($file['size'] > $maxSize) {
        $errors[] = 'File size too large. Maximum size is 5MB';
    }
    
    // Validate image dimensions (optional)
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        $errors[] = 'Invalid image file';
    }
    
    return $errors;
}
?>
