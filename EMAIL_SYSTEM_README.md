# Flix Salon Email System Documentation

## Overview

This comprehensive email system provides SMTP-based email functionality for the Flix Salon & SPA application. It includes automated booking confirmations, reminders, password resets, and staff notifications.

## Features

- **SMTP Authentication**: Secure email sending using provided credentials
- **Template-based Emails**: Professional HTML email templates
- **Automated Reminders**: Booking reminders at 24h, 5h, 30min, and appointment time
- **Email Logging**: Complete tracking of all sent emails
- **Error Handling**: Robust error handling and logging
- **Admin Interface**: Email management and monitoring dashboard
- **Integration**: Seamless integration with existing notification system

## Email Configuration

The system is configured with the following credentials in `config/app.php`:

```php
define('SMTP_HOST', 'mail.barberianramadhancup.co.tz');
define('SMTP_PORT', 465);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'UVutmmRceu37zK6');
define('SMTP_SECURE', 'ssl');
```

## Files Created/Modified

### New Files:
- `includes/email_functions.php` - Core email functionality
- `test_email.php` - Email testing interface
- `cron/send_reminders.php` - Automated reminder cron job
- `admin/email-logs.php` - Admin email management interface

### Modified Files:
- `config/app.php` - Added email configuration and includes
- `includes/notification_triggers.php` - Added email integration

## Core Functions

### Email Sending Functions

```php
// Send SMTP email with options
sendSMTPEmail($to, $subject, $body, $options = [])

// Send booking confirmation
sendBookingConfirmationEmail($bookingId)

// Send booking reminder
sendBookingReminderEmail($bookingId, $reminderType = '30_minutes')

// Send password reset email
sendPasswordResetEmail($userId, $resetToken)

// Send welcome email to new customers
sendWelcomeEmail($userId)

// Send staff notification
sendStaffNotificationEmail($staffId, $subject, $message, $bookingId = null)

// Send general notification
sendNotificationEmail($userId, $subject, $message, $actionUrl = null)
```

### Integration Functions

```php
// Enhanced booking notification with email
createBookingNotificationWithEmail($bookingId, $type, $additionalData = [])

// Enhanced staff notification with email
createStaffNotificationWithEmail($staffId, $type, $additionalData = [])

// Send automated reminders (for cron job)
sendBookingReminders()
```

### Utility Functions

```php
// Test email configuration
testEmailConfiguration()

// Get email statistics
getEmailStats($days = 30)

// Send bulk emails
sendBulkEmail($recipients, $subject, $body, $options = [])
```

## Email Templates

The system includes professional HTML templates for:

1. **Booking Confirmation** - Sent when booking is confirmed
2. **Booking Reminder** - Sent at specified intervals before appointment
3. **Password Reset** - Sent when user requests password reset
4. **Booking Cancellation** - Sent when booking is cancelled
5. **Welcome Email** - Sent to new customers
6. **Staff Notifications** - Sent to staff for various events

## Database Schema

The system creates an `email_logs` table to track all email activity:

```sql
CREATE TABLE email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    status ENUM('SENT', 'FAILED', 'PENDING') DEFAULT 'PENDING',
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_recipient (recipient),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

## Setup Instructions

### 1. Test Email Configuration

1. Navigate to `http://your-domain/flix-php/test_email.php`
2. Click "Test Configuration" to verify SMTP settings
3. Test different email types with existing booking/user IDs

### 2. Set Up Automated Reminders

Add this cron job to run every 15 minutes:

```bash
*/15 * * * * /usr/bin/php /path/to/your/project/cron/send_reminders.php
```

### 3. Admin Interface

Access the email management interface at:
`http://your-domain/flix-php/admin/email-logs.php`

Features:
- View email statistics
- Monitor recent email logs
- Send test emails
- Clear old logs
- View current configuration

## Integration Examples

### Booking Confirmation

```php
// When a booking is confirmed
createBookingNotificationWithEmail($bookingId, 'BOOKING_CONFIRMED');
```

### Staff Assignment

```php
// When staff is assigned to booking
createStaffNotificationWithEmail($staffId, 'STAFF_NEW');
```

### Password Reset

```php
// When user requests password reset
$resetToken = generateRandomString(32);
sendPasswordResetEmail($userId, $resetToken);
```

### Welcome New Customer

```php
// When new customer registers
sendWelcomeEmail($userId);
```

## Automatic Integration Points

The email system has been automatically integrated into the following existing functions:

### 1. Booking System (`includes/booking_functions.php`)
- **createBooking()**: Now sends booking confirmation emails automatically
- **updateBookingStatus()**: Sends emails for status changes (confirmed, cancelled, completed)
- **sendBookingReminder()**: Enhanced to use the new email system

### 2. User Registration (`includes/auth.php`)
- **register()**: Automatically sends welcome emails to new customers

### 3. Notification System (`includes/notification_triggers.php`)
- **createBookingNotificationWithEmail()**: Enhanced booking notifications with email
- **createStaffNotificationWithEmail()**: Enhanced staff notifications with email
- **sendBookingReminders()**: Automated reminder system for cron jobs

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check SMTP credentials in `config/app.php`
   - Verify server can connect to SMTP host on port 465
   - Check email logs table for error details

2. **Emails going to spam**
   - Verify SPF/DKIM records for the domain
   - Check email content for spam triggers
   - Monitor sender reputation

3. **Cron job not working**
   - Verify cron job is set up correctly
   - Check server logs for errors
   - Ensure PHP path is correct in cron command

### Debugging

1. Check PHP error logs
2. Review email logs in admin interface
3. Use test email page to verify functionality
4. Monitor database email_logs table

## Security Considerations

1. **Remove test files in production**:
   - Delete or secure `test_email.php`
   - Restrict access to admin interfaces

2. **Email credentials**:
   - Keep SMTP credentials secure
   - Use environment variables in production

3. **Rate limiting**:
   - Monitor email sending rates
   - Implement additional rate limiting if needed

## Performance Optimization

1. **Bulk emails**: Use `sendBulkEmail()` for multiple recipients
2. **Cron timing**: Adjust cron frequency based on volume
3. **Log cleanup**: Regularly clean old email logs
4. **Template caching**: Consider caching email templates for high volume

## Support

For issues or questions:
1. Check the email logs in admin interface
2. Review server error logs
3. Test email configuration using the test page
4. Verify database connectivity and email_logs table

## Future Enhancements

Potential improvements:
- Email queue system for high volume
- Advanced email templates with more customization
- Email analytics and reporting
- Integration with email marketing platforms
- Multi-language email support
