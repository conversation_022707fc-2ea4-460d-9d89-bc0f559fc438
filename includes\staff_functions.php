<?php
/**
 * Staff Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new staff member
 */
function createStaffMember($data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['email']) || empty($data['password'])) {
            return ['success' => false, 'error' => 'Name, email, and password are required'];
        }
        
        // Check if email already exists
        $existingUser = $database->fetch(
            "SELECT id FROM users WHERE email = ?",
            [$data['email']]
        );
        
        if ($existingUser) {
            return ['success' => false, 'error' => 'Email address already exists'];
        }
        
        // Hash password
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $staffId = generateUUID();

        // Insert user record
        $database->query(
            "INSERT INTO users (id, name, email, password, phone, role, is_active, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, 'STAFF', ?, NOW(), NOW())",
            [
                $staffId,
                sanitize($data['name']),
                sanitize($data['email']),
                $hashedPassword,
                sanitize($data['phone'] ?? null),
                isset($data['is_active']) ? 1 : 0
            ]
        );

        // Create staff schedule record
        $scheduleId = 'schedule-' . $staffId;
        $defaultSchedule = [
            'monday' => ['start' => '09:00', 'end' => '18:00', 'available' => true],
            'tuesday' => ['start' => '09:00', 'end' => '18:00', 'available' => true],
            'wednesday' => ['start' => '09:00', 'end' => '18:00', 'available' => true],
            'thursday' => ['start' => '09:00', 'end' => '18:00', 'available' => true],
            'friday' => ['start' => '09:00', 'end' => '18:00', 'available' => true],
            'saturday' => ['start' => '09:00', 'end' => '18:00', 'available' => true],
            'sunday' => ['start' => '09:00', 'end' => '18:00', 'available' => false]
        ];

        $database->query(
            "INSERT INTO staff_schedules (id, user_id, role, hourly_rate, schedule, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $scheduleId,
                $staffId,
                $data['staff_role'] ?? 'Staff Member',
                $data['hourly_rate'] ?? 500000,
                json_encode($defaultSchedule)
            ]
        );

        // Process specialties (service IDs)
        if (isset($data['specialties']) && is_array($data['specialties'])) {
            foreach ($data['specialties'] as $serviceId) {
                $specialtyId = generateUUID();
                $database->query(
                    "INSERT INTO staff_specialties (id, user_id, service_id, proficiency_level, created_at, updated_at)
                     VALUES (?, ?, ?, 'INTERMEDIATE', NOW(), NOW())",
                    [$specialtyId, $staffId, $serviceId]
                );
            }
        }
        
        return ['success' => true, 'id' => $staffId];
        
    } catch (Exception $e) {
        error_log("Staff creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create staff member'];
    }
}

/**
 * Update an existing staff member
 */
function updateStaffMember($staffId, $data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name']) || empty($data['email'])) {
            return ['success' => false, 'error' => 'Name and email are required'];
        }
        
        // Check if staff member exists
        $currentStaff = $database->fetch(
            "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
            [$staffId]
        );
        
        if (!$currentStaff) {
            return ['success' => false, 'error' => 'Staff member not found'];
        }
        
        // Check if email already exists (excluding current staff)
        $existingUser = $database->fetch(
            "SELECT id FROM users WHERE email = ? AND id != ?",
            [$data['email'], $staffId]
        );
        
        if ($existingUser) {
            return ['success' => false, 'error' => 'Email address already exists'];
        }
        
        // Prepare update query for users table
        $updateFields = [
            'name = ?',
            'email = ?',
            'phone = ?',
            'is_active = ?',
            'updated_at = NOW()'
        ];

        $params = [
            sanitize($data['name']),
            sanitize($data['email']),
            sanitize($data['phone'] ?? null),
            isset($data['is_active']) ? 1 : 0
        ];
        
        // Update password if provided
        if (!empty($data['password'])) {
            $updateFields[] = 'password = ?';
            $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $params[] = $staffId;
        
        $database->query(
            "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?",
            $params
        );

        // Update staff schedule info if provided
        if (isset($data['staff_role']) || isset($data['hourly_rate'])) {
            $scheduleUpdateFields = [];
            $scheduleParams = [];

            if (isset($data['staff_role'])) {
                $scheduleUpdateFields[] = 'role = ?';
                $scheduleParams[] = $data['staff_role'];
            }

            if (isset($data['hourly_rate'])) {
                $scheduleUpdateFields[] = 'hourly_rate = ?';
                $scheduleParams[] = $data['hourly_rate'];
            }

            if (!empty($scheduleUpdateFields)) {
                $scheduleUpdateFields[] = 'updated_at = NOW()';
                $scheduleParams[] = $staffId;

                $database->query(
                    "UPDATE staff_schedules SET " . implode(', ', $scheduleUpdateFields) . " WHERE user_id = ?",
                    $scheduleParams
                );
            }
        }

        // Update specialties
        if (isset($data['specialties'])) {
            // Delete existing specialties
            $database->query("DELETE FROM staff_specialties WHERE user_id = ?", [$staffId]);

            // Insert new specialties
            if (is_array($data['specialties'])) {
                foreach ($data['specialties'] as $serviceId) {
                    $specialtyId = generateUUID();
                    $database->query(
                        "INSERT INTO staff_specialties (id, user_id, service_id, proficiency_level, created_at, updated_at)
                         VALUES (?, ?, ?, 'INTERMEDIATE', NOW(), NOW())",
                        [$specialtyId, $staffId, $serviceId]
                    );
                }
            }
        }

        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Staff update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update staff member'];
    }
}

/**
 * Toggle staff member status
 */
function toggleStaffStatus($staffId) {
    global $database;
    
    try {
        // Get current status
        $currentStaff = $database->fetch(
            "SELECT is_active FROM users WHERE id = ? AND role = 'STAFF'",
            [$staffId]
        );
        
        if (!$currentStaff) {
            return ['success' => false, 'error' => 'Staff member not found'];
        }
        
        $newStatus = $currentStaff['is_active'] ? 0 : 1;
        
        $database->query(
            "UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?",
            [$newStatus, $staffId]
        );
        
        return ['success' => true, 'new_status' => $newStatus];
        
    } catch (Exception $e) {
        error_log("Staff status toggle error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update staff status'];
    }
}

/**
 * Get staff statistics
 */
function getStaffStats() {
    global $database;
    
    $stats = [];
    
    // Total staff
    $stats['total'] = $database->fetch(
        "SELECT COUNT(*) as count FROM users WHERE role = 'STAFF'"
    )['count'];
    
    // Active staff
    $stats['active'] = $database->fetch(
        "SELECT COUNT(*) as count FROM users WHERE role = 'STAFF' AND is_active = 1"
    )['count'];
    
    // Total bookings handled by staff
    $stats['total_bookings'] = $database->fetch(
        "SELECT COUNT(*) as count FROM bookings WHERE staff_id IS NOT NULL"
    )['count'];
    
    // Average monthly revenue per staff
    $stats['avg_monthly_revenue'] = $database->fetch(
        "SELECT AVG(monthly_revenue) as avg_revenue FROM (
            SELECT COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' AND b.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN b.total_amount ELSE 0 END), 0) as monthly_revenue
            FROM users u
            LEFT JOIN bookings b ON u.id = b.staff_id
            WHERE u.role = 'STAFF' AND u.is_active = 1
            GROUP BY u.id
         ) as staff_revenues"
    )['avg_revenue'] ?? 0;
    
    // Top performing staff this month
    $stats['top_performers'] = $database->fetchAll(
        "SELECT u.name, 
                COUNT(CASE WHEN b.status = 'COMPLETED' AND b.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as bookings,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' AND b.date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN b.total_amount ELSE 0 END), 0) as revenue
         FROM users u
         LEFT JOIN bookings b ON u.id = b.staff_id
         WHERE u.role = 'STAFF' AND u.is_active = 1
         GROUP BY u.id, u.name
         ORDER BY revenue DESC
         LIMIT 5"
    );
    
    return $stats;
}

/**
 * Get staff member by ID
 */
function getStaffById($staffId) {
    global $database;
    
    return $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
        [$staffId]
    );
}

/**
 * Get all active staff members
 */
function getActiveStaff() {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM users WHERE role = 'STAFF' AND is_active = 1 ORDER BY name"
    );
}

/**
 * Get staff members by specialty
 */
function getStaffBySpecialty($specialty) {
    global $database;
    
    return $database->fetchAll(
        "SELECT * FROM users 
         WHERE role = 'STAFF' 
         AND is_active = 1 
         AND (specialties LIKE ? OR specialties IS NULL)
         ORDER BY name",
        ["%$specialty%"]
    );
}

/**
 * Get staff schedule for a specific date
 */
function getStaffSchedule($staffId, $date) {
    global $database;
    
    try {
        // Get staff working hours (this could be from a staff_schedules table in the future)
        $workingHours = [
            'start' => '09:00:00',
            'end' => '18:00:00'
        ];
        
        // Get bookings for this staff member on this date
        $bookings = $database->fetchAll(
            "SELECT b.*, s.name as service_name, u.name as customer_name
             FROM bookings b
             LEFT JOIN services s ON b.service_id = s.id
             LEFT JOIN users u ON b.user_id = u.id
             WHERE b.staff_id = ? AND b.date = ? AND b.status NOT IN ('CANCELLED', 'NO_SHOW')
             ORDER BY b.start_time",
            [$staffId, $date]
        );
        
        return [
            'working_hours' => $workingHours,
            'bookings' => $bookings
        ];
        
    } catch (Exception $e) {
        error_log("Staff schedule error: " . $e->getMessage());
        return null;
    }
}

/**
 * Get staff performance metrics
 */
function getStaffPerformance($staffId, $startDate = null, $endDate = null) {
    global $database;
    
    try {
        if (!$startDate) {
            $startDate = date('Y-m-01'); // First day of current month
        }
        if (!$endDate) {
            $endDate = date('Y-m-t'); // Last day of current month
        }
        
        $performance = [];
        
        // Total bookings
        $performance['total_bookings'] = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings 
             WHERE staff_id = ? AND date BETWEEN ? AND ?",
            [$staffId, $startDate, $endDate]
        )['count'];
        
        // Completed bookings
        $performance['completed_bookings'] = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings 
             WHERE staff_id = ? AND date BETWEEN ? AND ? AND status = 'COMPLETED'",
            [$staffId, $startDate, $endDate]
        )['count'];
        
        // Total revenue
        $performance['total_revenue'] = $database->fetch(
            "SELECT COALESCE(SUM(total_amount), 0) as revenue FROM bookings 
             WHERE staff_id = ? AND date BETWEEN ? AND ? AND status = 'COMPLETED'",
            [$staffId, $startDate, $endDate]
        )['revenue'];
        
        // Average rating (if reviews system is implemented)
        $performance['average_rating'] = 4.5; // Placeholder
        
        // Cancellation rate
        $cancelledBookings = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings 
             WHERE staff_id = ? AND date BETWEEN ? AND ? AND status = 'CANCELLED'",
            [$staffId, $startDate, $endDate]
        )['count'];
        
        $performance['cancellation_rate'] = $performance['total_bookings'] > 0 
            ? round(($cancelledBookings / $performance['total_bookings']) * 100, 2)
            : 0;
        
        // Most popular services
        $performance['popular_services'] = $database->fetchAll(
            "SELECT s.name, COUNT(*) as booking_count
             FROM bookings b
             INNER JOIN services s ON b.service_id = s.id
             WHERE b.staff_id = ? AND b.date BETWEEN ? AND ? AND b.status = 'COMPLETED'
             GROUP BY s.id, s.name
             ORDER BY booking_count DESC
             LIMIT 5",
            [$staffId, $startDate, $endDate]
        );
        
        return $performance;
        
    } catch (Exception $e) {
        error_log("Staff performance error: " . $e->getMessage());
        return null;
    }
}

/**
 * Set staff working hours
 */
function setStaffWorkingHours($staffId, $schedule) {
    global $database;
    
    try {
        // Delete existing schedule
        $database->query(
            "DELETE FROM staff_schedules WHERE staff_id = ?",
            [$staffId]
        );
        
        // Insert new schedule
        foreach ($schedule as $day => $hours) {
            if ($hours['is_working']) {
                $scheduleId = generateUUID();
                $database->query(
                    "INSERT INTO staff_schedules (id, staff_id, day_of_week, start_time, end_time, is_working, created_at) 
                     VALUES (?, ?, ?, ?, ?, 1, NOW())",
                    [
                        $scheduleId,
                        $staffId,
                        $day,
                        $hours['start_time'],
                        $hours['end_time']
                    ]
                );
            }
        }
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Staff schedule update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update schedule'];
    }
}


?>
