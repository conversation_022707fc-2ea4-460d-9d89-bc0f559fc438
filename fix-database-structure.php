<?php
/**
 * Comprehensive Database Structure Fix
 * Fixes the "Field 'id' doesn't have a default value" error and other database issues
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Comprehensive Database Structure Fix</h1>";

try {
    echo "<h2>1. Analyzing point_transactions Table</h2>";
    
    // Check if table exists
    $tableExists = $database->fetch("SHOW TABLES LIKE 'point_transactions'");
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ point_transactions table does not exist!</p>";
        echo "<p>Creating table with correct structure...</p>";
        
        $database->execute("
            CREATE TABLE point_transactions (
                id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                booking_id VARCHAR(36) NULL,
                reward_id VARCHAR(36) NULL,
                points INT NOT NULL,
                type ENUM('EARNED', 'REDEMPTION', 'DEDUCTION', 'BONUS') NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_point_transactions_user (user_id),
                INDEX idx_point_transactions_booking (booking_id),
                INDEX idx_point_transactions_type (type),
                INDEX idx_point_transactions_created (created_at)
            )
        ");
        
        echo "<p style='color: green;'>✅ Created point_transactions table with correct structure</p>";
    } else {
        echo "<p style='color: green;'>✅ point_transactions table exists</p>";
        
        // Get table structure
        $columns = $database->fetchAll("DESCRIBE point_transactions");
        $hasBookingId = false;
        $hasRewardId = false;
        $idColumnInfo = null;
        
        echo "<h3>Current Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
            
            if ($column['Field'] === 'id') {
                $idColumnInfo = $column;
            }
            if ($column['Field'] === 'booking_id') {
                $hasBookingId = true;
            }
            if ($column['Field'] === 'reward_id') {
                $hasRewardId = true;
            }
        }
        echo "</table>";
        
        // Check for AUTO_INCREMENT issue
        $hasAutoIncrement = false;
        if ($idColumnInfo && strpos($idColumnInfo['Extra'], 'auto_increment') !== false) {
            $hasAutoIncrement = true;
            echo "<p style='color: red;'>❌ CRITICAL: 'id' column has AUTO_INCREMENT - this causes the error!</p>";
        } else {
            echo "<p style='color: green;'>✅ 'id' column structure is correct (no AUTO_INCREMENT)</p>";
        }
        
        // Check for missing columns
        if (!$hasBookingId) {
            echo "<p style='color: red;'>❌ Missing 'booking_id' column</p>";
        } else {
            echo "<p style='color: green;'>✅ 'booking_id' column exists</p>";
        }
        
        if (!$hasRewardId) {
            echo "<p style='color: orange;'>⚠️ Missing 'reward_id' column (optional)</p>";
        } else {
            echo "<p style='color: green;'>✅ 'reward_id' column exists</p>";
        }
        
        // Show fix form if needed
        if ($hasAutoIncrement || !$hasBookingId || !$hasRewardId) {
            echo "<h2>Apply Database Fixes</h2>";
            echo "<p>The following fixes will be applied:</p>";
            echo "<ul>";
            if ($hasAutoIncrement) {
                echo "<li><strong>Remove AUTO_INCREMENT from 'id' column</strong> (fixes the main error)</li>";
            }
            if (!$hasBookingId) {
                echo "<li>Add 'booking_id' column</li>";
            }
            if (!$hasRewardId) {
                echo "<li>Add 'reward_id' column</li>";
            }
            echo "</ul>";
            
            echo "<form method='post'>";
            echo "<input type='hidden' name='action' value='fix_all'>";
            echo "<button type='submit' style='background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>🔧 Fix Database Structure</button>";
            echo "</form>";
        } else {
            echo "<p style='color: green;'>✅ Database structure is correct!</p>";
        }
    }
    
    // Handle fix action
    if (isset($_POST['action']) && $_POST['action'] === 'fix_all') {
        echo "<h2>🔧 Applying Database Fixes...</h2>";
        
        try {
            $database->beginTransaction();
            
            // Re-check structure for the fixes
            $columns = $database->fetchAll("DESCRIBE point_transactions");
            $hasBookingId = false;
            $hasRewardId = false;
            $hasAutoIncrement = false;
            
            foreach ($columns as $column) {
                if ($column['Field'] === 'id' && strpos($column['Extra'], 'auto_increment') !== false) {
                    $hasAutoIncrement = true;
                }
                if ($column['Field'] === 'booking_id') {
                    $hasBookingId = true;
                }
                if ($column['Field'] === 'reward_id') {
                    $hasRewardId = true;
                }
            }
            
            // Fix 1: Remove AUTO_INCREMENT from id column
            if ($hasAutoIncrement) {
                echo "<p>🔧 Removing AUTO_INCREMENT from 'id' column...</p>";
                $database->execute("ALTER TABLE point_transactions MODIFY id VARCHAR(36) NOT NULL PRIMARY KEY");
                echo "<p style='color: green;'>✅ Removed AUTO_INCREMENT from 'id' column</p>";
            }
            
            // Fix 2: Add booking_id column
            if (!$hasBookingId) {
                echo "<p>🔧 Adding 'booking_id' column...</p>";
                $database->execute("ALTER TABLE point_transactions ADD COLUMN booking_id VARCHAR(36) NULL");
                echo "<p style='color: green;'>✅ Added 'booking_id' column</p>";
            }
            
            // Fix 3: Add reward_id column
            if (!$hasRewardId) {
                echo "<p>🔧 Adding 'reward_id' column...</p>";
                $database->execute("ALTER TABLE point_transactions ADD COLUMN reward_id VARCHAR(36) NULL");
                echo "<p style='color: green;'>✅ Added 'reward_id' column</p>";
            }
            
            // Fix 4: Add indexes for performance
            try {
                echo "<p>🔧 Adding database indexes...</p>";
                $database->execute("CREATE INDEX IF NOT EXISTS idx_point_transactions_booking ON point_transactions(booking_id)");
                $database->execute("CREATE INDEX IF NOT EXISTS idx_point_transactions_user ON point_transactions(user_id)");
                $database->execute("CREATE INDEX IF NOT EXISTS idx_point_transactions_type ON point_transactions(type)");
                echo "<p style='color: green;'>✅ Added database indexes</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Index creation: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            $database->commit();
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 ALL FIXES APPLIED SUCCESSFULLY!</p>";
            echo "<p>The 'Field id doesn't have a default value' error should now be resolved.</p>";
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ Error applying fixes: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>2. Test Point Transaction Insert</h2>";
    
    // Test the actual insert that was failing
    try {
        $testId = generateUUID();
        $testUserId = $database->fetch("SELECT id FROM users WHERE role = 'CUSTOMER' LIMIT 1")['id'] ?? null;
        
        if ($testUserId) {
            echo "<p>Testing point transaction insert...</p>";
            
            // Test the exact insert that was failing
            $database->execute("
                INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
                VALUES (?, ?, ?, ?, 'EARNED', 'Test transaction', NOW())
            ", [$testId, $testUserId, null, 10]);
            
            echo "<p style='color: green;'>✅ Point transaction insert test PASSED!</p>";
            
            // Clean up test data
            $database->execute("DELETE FROM point_transactions WHERE id = ?", [$testId]);
            echo "<p>Test data cleaned up</p>";
            
        } else {
            echo "<p style='color: orange;'>⚠️ No customer found for testing</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Point transaction insert test FAILED: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>This indicates the database structure still needs fixing.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>📋 Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Test Booking Creation:</strong> Go to <a href='https://flix.co.tz/flix/customer/book/' target='_blank'>customer booking page</a></li>";
echo "<li><strong>Test Staff Suggestions:</strong> Select a service and verify staff appear</li>";
echo "<li><strong>Complete a Booking:</strong> Ensure no database errors occur</li>";
echo "<li><strong>Monitor Logs:</strong> Check for any remaining errors</li>";
echo "</ol>";

echo "<p><a href='test-fixes.php'>🧪 Run Complete Test Suite</a></p>";
echo "<p><a href='debug-staff-issues.php'>← Back to Staff Diagnostic</a></p>";
?>
