<?php
/**
 * DPO Pay Functions
 * Functions for handling DPO Pay payment gateway integration
 */

/**
 * Create DPO payment token
 * @param array $paymentData Payment data including amount, customer info, etc.
 * @return array Response with token or error
 */
function createDpoToken($paymentData) {
    try {
        $companyToken = DPO_COMPANY_TOKEN;
        $serviceType = DPO_SERVICE_TYPE ?? '';
        
        // Extract payment data
        $amount = $paymentData['amount'];
        $currency = $paymentData['currency'] ?? 'TZS';
        $reference = $paymentData['reference'];

        // Handle customer name - split if provided as full name or use separate fields
        $customerName = $paymentData['customer_name'] ?? '';
        if (!empty($customerName)) {
            $nameParts = explode(' ', trim($customerName), 2);
            $customerFirstName = $nameParts[0];
            $customerLastName = isset($nameParts[1]) ? $nameParts[1] : '';
        } else {
            $customerFirstName = $paymentData['customer_first_name'] ?? '';
            $customerLastName = $paymentData['customer_last_name'] ?? '';
        }

        $customerEmail = $paymentData['customer_email'];
        $customerPhone = $paymentData['customer_phone'] ?? '';
        $customerAddress = $paymentData['customer_address'] ?? '';
        $customerCity = $paymentData['customer_city'] ?? '';
        
        // URLs for redirect
        $redirectURL = $paymentData['redirect_url'];
        $backURL = $paymentData['back_url'];
        
        $odate = date('Y/m/d H:i');
        
        // Create XML request
        $postXml = <<<POSTXML
<?xml version="1.0" encoding="utf-8"?>
<API3G>
    <CompanyToken>$companyToken</CompanyToken>
    <Request>createToken</Request>
    <Transaction>
        <PaymentAmount>$amount</PaymentAmount>
        <PaymentCurrency>$currency</PaymentCurrency>
        <CompanyRef>$reference</CompanyRef>
        <RedirectURL>$redirectURL</RedirectURL>
        <BackURL>$backURL</BackURL>
        <CompanyRefUnique>0</CompanyRefUnique>
        <PTL>5</PTL>
    </Transaction>
    <Services>
        <Service>
            <ServiceType>$serviceType</ServiceType>
            <ServiceDescription>Flix Salon & SPA Payment</ServiceDescription>
            <ServiceDate>$odate</ServiceDate>
        </Service>
    </Services>
    <Customer>
        <CustomerFirstName>$customerFirstName</CustomerFirstName>
        <CustomerLastName>$customerLastName</CustomerLastName>
        <CustomerAddress>$customerAddress</CustomerAddress>
        <CustomerCity>$customerCity</CustomerCity>
        <CustomerPhone>$customerPhone</CustomerPhone>
        <CustomerEmail>$customerEmail</CustomerEmail>
    </Customer>
</API3G>
POSTXML;

        // Make API call
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => DPO_API_URL . "/API/v6/",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $postXml,
            CURLOPT_HTTPHEADER => array(
                "cache-control: no-cache",
                "Content-Type: application/xml",
            ),
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);

        if ($response != '') {
            // Check if response is HTML (error page) instead of XML
            if (stripos($response, '<html') !== false || stripos($response, '<body') !== false) {
                error_log("DPO API returned HTML instead of XML. Response: " . substr($response, 0, 500));
                return [
                    'success' => false,
                    'result' => 'HTML_RESPONSE',
                    'resultExplanation' => 'DPO API returned HTML error page instead of XML. Please check your credentials and API URL.',
                    'raw_response' => $response
                ];
            }

            // Check if response looks like XML
            if (stripos($response, '<?xml') === false && stripos($response, '<API3G>') === false) {
                error_log("DPO API returned invalid response format. Response: " . substr($response, 0, 500));
                return [
                    'success' => false,
                    'result' => 'INVALID_FORMAT',
                    'resultExplanation' => 'DPO API returned invalid response format. Expected XML.',
                    'raw_response' => $response
                ];
            }

            try {
                $xml = new SimpleXMLElement($response);
                $result = $xml->xpath('Result')[0]->__toString();
                $resultExplanation = $xml->xpath('ResultExplanation')[0]->__toString();

                $returnResult = [
                    'result' => $result,
                    'resultExplanation' => $resultExplanation,
                ];

                // Check if token was created successfully
                if ($xml->xpath('Result')[0] != '000') {
                    $returnResult['success'] = false;
                    error_log("DPO Token Creation Failed: " . $resultExplanation);
                } else {
                    $transToken = $xml->xpath('TransToken')[0]->__toString();
                    $transRef = $xml->xpath('TransRef')[0]->__toString();
                    $returnResult['success'] = true;
                    $returnResult['transToken'] = $transToken;
                    $returnResult['transRef'] = $transRef;
                    $returnResult['paymentUrl'] = DPO_API_URL . '/payv2.php?ID=' . $transToken;
                }

                return $returnResult;

            } catch (Exception $xmlException) {
                error_log("DPO XML Parsing Error: " . $xmlException->getMessage() . ". Response: " . substr($response, 0, 500));
                return [
                    'success' => false,
                    'result' => 'XML_PARSE_ERROR',
                    'resultExplanation' => 'Failed to parse DPO API response: ' . $xmlException->getMessage(),
                    'raw_response' => $response
                ];
            }
        } else {
            return [
                'success' => false,
                'result' => !empty($error) ? $error : 'Unknown error occurred in token creation',
                'resultExplanation' => !empty($error) ? $error : 'Unknown error occurred in token creation',
            ];
        }
        
    } catch (Exception $e) {
        error_log("DPO Token Creation Exception: " . $e->getMessage());
        return [
            'success' => false,
            'result' => 'Exception occurred',
            'resultExplanation' => $e->getMessage()
        ];
    }
}

/**
 * Verify DPO payment token
 * @param string $transToken Transaction token to verify
 * @return array Verification result
 */
function verifyDpoToken($transToken) {
    try {
        $companyToken = DPO_COMPANY_TOKEN;
        
        $postXml = <<<POSTXML
<?xml version="1.0" encoding="utf-8"?>
<API3G>
    <CompanyToken>$companyToken</CompanyToken>
    <Request>verifyToken</Request>
    <TransactionToken>$transToken</TransactionToken>
</API3G>
POSTXML;

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => DPO_API_URL . "/API/v6/",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => $postXml,
            CURLOPT_HTTPHEADER => array(
                "cache-control: no-cache",
                "Content-Type: application/xml",
            ),
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);

        if ($response != '') {
            // Check if response is HTML (error page) instead of XML
            if (stripos($response, '<html') !== false || stripos($response, '<body') !== false) {
                error_log("DPO Verify API returned HTML instead of XML. Response: " . substr($response, 0, 500));
                return [
                    'success' => false,
                    'verified' => false,
                    'result' => 'HTML_RESPONSE',
                    'resultExplanation' => 'DPO API returned HTML error page instead of XML. Please check your credentials and API URL.',
                    'raw_response' => $response
                ];
            }

            // Check if response looks like XML
            if (stripos($response, '<?xml') === false && stripos($response, '<API3G>') === false) {
                error_log("DPO Verify API returned invalid response format. Response: " . substr($response, 0, 500));
                return [
                    'success' => false,
                    'verified' => false,
                    'result' => 'INVALID_FORMAT',
                    'resultExplanation' => 'DPO API returned invalid response format. Expected XML.',
                    'raw_response' => $response
                ];
            }

            try {
                $xml = new SimpleXMLElement($response);
                $result = $xml->xpath('Result')[0]->__toString();
                $resultExplanation = $xml->xpath('ResultExplanation')[0]->__toString();

                $returnResult = [
                    'result' => $result,
                    'resultExplanation' => $resultExplanation,
                ];

                // Check verification result
                if ($result === '000') {
                    $returnResult['success'] = true;
                    $returnResult['verified'] = true;

                    // Extract transaction details if available
                    if ($xml->xpath('CustomerName')) {
                        $returnResult['customerName'] = $xml->xpath('CustomerName')[0]->__toString();
                    }
                    if ($xml->xpath('CustomerCredit')) {
                        $returnResult['customerCredit'] = $xml->xpath('CustomerCredit')[0]->__toString();
                    }
                    if ($xml->xpath('TransactionApproval')) {
                        $returnResult['transactionApproval'] = $xml->xpath('TransactionApproval')[0]->__toString();
                    }
                    if ($xml->xpath('TransactionCurrency')) {
                        $returnResult['transactionCurrency'] = $xml->xpath('TransactionCurrency')[0]->__toString();
                    }
                    if ($xml->xpath('TransactionAmount')) {
                        $returnResult['transactionAmount'] = $xml->xpath('TransactionAmount')[0]->__toString();
                    }

                } else if ($result === '900') {
                    $returnResult['success'] = true;
                    $returnResult['verified'] = false;
                    $returnResult['status'] = 'pending';
                } else {
                    $returnResult['success'] = false;
                    $returnResult['verified'] = false;
                    error_log("DPO Token Verification Failed: " . $resultExplanation);
                }

                return $returnResult;

            } catch (Exception $xmlException) {
                error_log("DPO Verify XML Parsing Error: " . $xmlException->getMessage() . ". Response: " . substr($response, 0, 500));
                return [
                    'success' => false,
                    'verified' => false,
                    'result' => 'XML_PARSE_ERROR',
                    'resultExplanation' => 'Failed to parse DPO API response: ' . $xmlException->getMessage(),
                    'raw_response' => $response
                ];
            }
        } else {
            return [
                'success' => false,
                'verified' => false,
                'result' => !empty($error) ? $error : 'Unknown error occurred in token verification',
                'resultExplanation' => !empty($error) ? $error : 'Unknown error occurred in token verification',
            ];
        }
        
    } catch (Exception $e) {
        error_log("DPO Token Verification Exception: " . $e->getMessage());
        return [
            'success' => false,
            'verified' => false,
            'result' => 'Exception occurred',
            'resultExplanation' => $e->getMessage()
        ];
    }
}

/**
 * Process DPO payment for a booking
 * @param string $paymentId Payment ID from database
 * @return array Processing result
 */
function processDpoPayment($paymentId) {
    global $database;
    
    try {
        // Get payment details
        $payment = $database->fetch("
            SELECT p.*, b.user_id, b.id as booking_id, u.name, u.email, u.phone
            FROM payments p
            INNER JOIN bookings b ON p.booking_id = b.id
            INNER JOIN users u ON b.user_id = u.id
            WHERE p.id = ? AND p.payment_gateway = 'DPO' AND p.status = 'PENDING'
        ", [$paymentId]);

        if (!$payment) {
            throw new Exception('Payment not found or invalid');
        }

        // Prepare payment data for DPO
        $paymentData = [
            'amount' => $payment['amount'],
            'currency' => 'TZS',
            'reference' => $payment['payment_reference'],
            'customer_name' => $payment['name'],
            'customer_email' => $payment['email'],
            'customer_phone' => $payment['phone'] ?? '',
            'customer_address' => '',
            'customer_city' => '',
            'redirect_url' => getBaseUrl() . '/customer/payments/dpo-return.php?payment_id=' . $paymentId,
            'back_url' => getBaseUrl() . '/customer/payments'
        ];
        
        // Create DPO token
        $tokenResult = createDpoToken($paymentData);
        
        if ($tokenResult['success']) {
            // Update payment with DPO token
            $paymentDataJson = json_encode([
                'dpo_token' => $tokenResult['transToken'],
                'dpo_ref' => $tokenResult['transRef'],
                'payment_url' => $tokenResult['paymentUrl']
            ]);
            
            $database->execute("
                UPDATE payments
                SET payment_data = ?, dpo_token = ?, updated_at = NOW()
                WHERE id = ?
            ", [$paymentDataJson, $tokenResult['transToken'], $paymentId]);
            
            // Log payment event
            if (function_exists('logPaymentEvent')) {
                logPaymentEvent($paymentId, 'PROCESSING', 'DPO', [
                    'dpo_token' => $tokenResult['transToken'],
                    'dpo_ref' => $tokenResult['transRef'],
                    'amount' => $payment['amount'],
                    'currency' => 'TZS'
                ]);
            }
            
            return [
                'success' => true,
                'payment_url' => $tokenResult['paymentUrl'],
                'dpo_token' => $tokenResult['transToken']
            ];
        } else {
            throw new Exception('Failed to create DPO token: ' . $tokenResult['resultExplanation']);
        }
        
    } catch (Exception $e) {
        error_log("DPO Payment Processing Error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}
