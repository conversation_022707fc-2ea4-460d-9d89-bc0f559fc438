<?php
/**
 * Fix Booking Errors Script
 * This script fixes database structure issues that cause booking errors
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Booking Errors Fix Script</h1>";

try {
    echo "<h2>Checking Database Structure</h2>";
    
    // Check point_transactions table structure
    $columns = $database->fetchAll("DESCRIBE point_transactions");
    $hasBookingId = false;
    $hasRewardId = false;
    
    echo "<h3>Current point_transactions table structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'booking_id') {
            $hasBookingId = true;
        }
        if ($column['Field'] === 'reward_id') {
            $hasRewardId = true;
        }
    }
    echo "</table>";
    
    echo "<h2>Required Fixes</h2>";
    $needsFix = false;
    
    if (!$hasBookingId) {
        echo "<p style='color: red;'>❌ Missing booking_id column in point_transactions table</p>";
        $needsFix = true;
    } else {
        echo "<p style='color: green;'>✅ booking_id column exists</p>";
    }
    
    if (!$hasRewardId) {
        echo "<p style='color: orange;'>⚠️ Missing reward_id column in point_transactions table (optional but recommended)</p>";
        $needsFix = true;
    } else {
        echo "<p style='color: green;'>✅ reward_id column exists</p>";
    }
    
    if ($needsFix) {
        echo "<h2>Apply Fixes</h2>";
        echo "<p>Click the button below to apply the necessary database structure fixes:</p>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='fix_structure'>";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fix Database Structure</button>";
        echo "</form>";
    } else {
        echo "<p style='color: green;'>✅ Database structure is correct!</p>";
    }
    
    // Handle the fix action
    if (isset($_POST['action']) && $_POST['action'] === 'fix_structure') {
        echo "<h2>Applying Fixes...</h2>";
        
        try {
            $database->beginTransaction();
            
            if (!$hasBookingId) {
                echo "<p>Adding booking_id column...</p>";
                $database->execute("
                    ALTER TABLE point_transactions 
                    ADD COLUMN booking_id VARCHAR(36) NULL AFTER created_at
                ");
                echo "<p style='color: green;'>✅ Added booking_id column</p>";
            }
            
            if (!$hasRewardId) {
                echo "<p>Adding reward_id column...</p>";
                $database->execute("
                    ALTER TABLE point_transactions 
                    ADD COLUMN reward_id VARCHAR(36) NULL AFTER booking_id
                ");
                echo "<p style='color: green;'>✅ Added reward_id column</p>";
            }
            
            // Add foreign key constraints if they don't exist
            try {
                echo "<p>Adding foreign key constraints...</p>";
                $database->execute("
                    ALTER TABLE point_transactions 
                    ADD CONSTRAINT fk_point_transactions_booking 
                    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL
                ");
                echo "<p style='color: green;'>✅ Added booking foreign key constraint</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                    echo "<p style='color: orange;'>⚠️ Foreign key constraint already exists</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Could not add foreign key constraint: " . $e->getMessage() . "</p>";
                }
            }
            
            $database->commit();
            echo "<p style='color: green;'>✅ All fixes applied successfully!</p>";
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ Error applying fixes: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test booking creation
    echo "<h2>Test Booking Creation</h2>";
    echo "<p>You can now test booking creation to see if the errors are resolved.</p>";
    
    // Check for recent booking errors in logs
    echo "<h2>Recent Error Log Check</h2>";
    $logFile = __DIR__ . '/logs/error.log';
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $lines = explode("\n", $logContent);
        $recentErrors = [];
        
        // Get last 50 lines and filter for booking-related errors
        $lastLines = array_slice($lines, -50);
        foreach ($lastLines as $line) {
            if (stripos($line, 'booking') !== false || stripos($line, 'point_transactions') !== false || stripos($line, "doesn't have a default value") !== false) {
                $recentErrors[] = $line;
            }
        }
        
        if (!empty($recentErrors)) {
            echo "<h3>Recent Booking-Related Errors:</h3>";
            echo "<div style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
            foreach ($recentErrors as $error) {
                echo "<p style='font-family: monospace; font-size: 12px; margin: 2px 0;'>" . htmlspecialchars($error) . "</p>";
            }
            echo "</div>";
        } else {
            echo "<p style='color: green;'>✅ No recent booking-related errors found in logs</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Error log file not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>After applying fixes, test booking creation from the customer panel</li>";
echo "<li>Check that staff suggestions are now working properly</li>";
echo "<li>Monitor error logs for any remaining issues</li>";
echo "<li>Remove this script file after confirming everything works</li>";
echo "</ul>";

echo "<p><a href='debug-staff-issues.php'>← Back to Staff Diagnostic Script</a></p>";
?>
