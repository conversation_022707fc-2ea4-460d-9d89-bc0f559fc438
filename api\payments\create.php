<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

// Check authentication and customer role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Check if payments are enabled
if (!PAYMENT_ENABLED) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Payments are currently disabled']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Read input once and store it
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    // Enhanced debug logging
    error_log('Payment API - Raw input: ' . $rawInput);
    error_log('Payment API - Raw input length: ' . strlen($rawInput));
    error_log('Payment API - JSON decode error: ' . json_last_error_msg());
    error_log('Payment API - Decoded input: ' . json_encode($input));
    error_log('Payment API - Input type: ' . gettype($input));
    error_log('Payment API - Content-Type header: ' . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
    error_log('Payment API - Request method: ' . $_SERVER['REQUEST_METHOD']);

    if (!$input) {
        throw new Exception('Invalid JSON input: ' . json_last_error_msg());
    }

    $bookingId = trim($input['booking_id'] ?? '');
    $amount = intval($input['amount'] ?? 0);
    $gateway = strtoupper($input['gateway'] ?? 'STRIPE');

    // Enhanced debug logging
    error_log('Payment API - Extracted values: booking_id="' . $bookingId . '" (length: ' . strlen($bookingId) . '), amount=' . $amount . ', gateway=' . $gateway);
    error_log('Payment API - booking_id isset: ' . (isset($input['booking_id']) ? 'true' : 'false'));
    error_log('Payment API - booking_id value: ' . var_export($input['booking_id'] ?? 'NOT_SET', true));

    // Validate input with detailed error messages
    if (empty($bookingId)) {
        $errorMsg = 'Booking ID is required. Received: ' . var_export($input['booking_id'] ?? 'NOT_SET', true);
        error_log('Payment API - Validation failed: ' . $errorMsg);
        throw new Exception($errorMsg);
    }
    
    if ($amount <= 0) {
        $errorMsg = 'Invalid amount. Received: ' . var_export($input['amount'] ?? 'NOT_SET', true) . ' (parsed as: ' . $amount . ')';
        error_log('Payment API - Amount validation failed: ' . $errorMsg);
        throw new Exception($errorMsg);
    }
    
    if (!in_array($gateway, ['DPO', 'STRIPE', 'FLUTTERWAVE'])) {
        throw new Exception('Invalid payment gateway');
    }

    // Check if gateway is enabled
    if ($gateway === 'DPO' && !DPO_ENABLED) {
        throw new Exception('DPO Pay is currently disabled');
    }

    if ($gateway === 'STRIPE' && !STRIPE_ENABLED) {
        throw new Exception('Stripe payments are currently disabled');
    }

    if ($gateway === 'FLUTTERWAVE' && !FLUTTERWAVE_ENABLED) {
        throw new Exception('Flutterwave payments are currently disabled');
    }
    
    $userId = $_SESSION['user_id'];

    // Debug logging for user session
    error_log('Payment API - Session user ID: ' . $userId);
    error_log('Payment API - Session data: ' . json_encode($_SESSION));

    // Create payment record
    $result = createPayment($bookingId, $userId, $amount, $gateway);
    
    if (!$result['success']) {
        throw new Exception($result['error']);
    }
    
    echo json_encode([
        'success' => true,
        'payment_id' => $result['payment_id'],
        'payment_reference' => $result['payment_reference'],
        'message' => 'Payment record created successfully'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
