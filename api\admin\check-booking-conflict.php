<?php
/**
 * Booking Conflict Check API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        handleCheckConflict();
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleCheckConflict() {
    global $database;
    
    try {
        // Get input data
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid JSON data']);
            return;
        }
        
        // Validate required fields
        $requiredFields = ['staff_id', 'date', 'start_time', 'end_time'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                http_response_code(400);
                echo json_encode(['error' => "Missing required field: $field"]);
                return;
            }
        }
        
        $staffId = $input['staff_id'];
        $date = $input['date'];
        $startTime = $input['start_time'];
        $endTime = $input['end_time'];
        $excludeBookingId = $input['exclude_booking_id'] ?? null; // For editing existing bookings
        
        // Validate staff exists
        $staff = $database->fetch("
            SELECT id, name FROM users 
            WHERE id = ? AND role = 'STAFF' AND is_active = 1
        ", [$staffId]);
        
        if (!$staff) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid staff member']);
            return;
        }
        
        // Check for conflicts
        $conflictQuery = "
            SELECT 
                b.id,
                b.start_time,
                b.end_time,
                u.name as customer_name,
                s.name as service_name
            FROM bookings b
            LEFT JOIN users u ON b.user_id = u.id
            LEFT JOIN services s ON b.service_id = s.id
            WHERE b.staff_id = ? 
            AND DATE(b.date) = ? 
            AND b.status NOT IN ('CANCELLED', 'NO_SHOW')
            AND (
                (b.start_time < ? AND b.end_time > ?) OR
                (b.start_time < ? AND b.end_time > ?) OR
                (b.start_time >= ? AND b.end_time <= ?)
            )
        ";
        
        $params = [
            $staffId,
            $date,
            $endTime, $startTime,    // Check if new booking starts before existing ends and ends after existing starts
            $endTime, $endTime,      // Check if new booking ends after existing starts and before existing ends
            $startTime, $endTime     // Check if new booking completely contains existing booking
        ];
        
        // Exclude current booking if editing
        if ($excludeBookingId) {
            $conflictQuery .= " AND b.id != ?";
            $params[] = $excludeBookingId;
        }
        
        $conflicts = $database->fetchAll($conflictQuery, $params);
        
        if (empty($conflicts)) {
            // No conflicts found
            echo json_encode([
                'success' => true,
                'has_conflict' => false,
                'message' => 'Time slot is available',
                'staff_name' => $staff['name']
            ]);
        } else {
            // Conflicts found
            $conflictDetails = [];
            foreach ($conflicts as $conflict) {
                $conflictDetails[] = [
                    'booking_id' => $conflict['id'],
                    'customer_name' => $conflict['customer_name'],
                    'service_name' => $conflict['service_name'],
                    'start_time' => $conflict['start_time'],
                    'end_time' => $conflict['end_time'],
                    'time_display' => date('g:i A', strtotime($conflict['start_time'])) . ' - ' . date('g:i A', strtotime($conflict['end_time']))
                ];
            }
            
            echo json_encode([
                'success' => true,
                'has_conflict' => true,
                'message' => $staff['name'] . ' is already booked during this time. Please change the time or date.',
                'staff_name' => $staff['name'],
                'conflicts' => $conflictDetails,
                'suggested_message' => "Specialist " . $staff['name'] . " is already booked this time. Change time or date."
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to check booking conflict: ' . $e->getMessage()]);
    }
}
?>
