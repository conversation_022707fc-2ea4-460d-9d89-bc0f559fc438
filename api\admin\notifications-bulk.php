<?php
/**
 * Admin Notifications Bulk Actions API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    $action = $input['action'] ?? null;
    $notificationIds = $input['notification_ids'] ?? [];
    $category = $input['category'] ?? null;
    
    if (!$action) {
        http_response_code(400);
        echo json_encode(['error' => 'Action is required']);
        exit;
    }
    
    switch ($action) {
        case 'mark_read':
            handleMarkAsRead($notificationIds, $category);
            break;
        case 'mark_unread':
            handleMarkAsUnread($notificationIds, $category);
            break;
        case 'delete':
            handleBulkDelete($notificationIds, $category);
            break;
        case 'delete_read':
            handleDeleteRead($category);
            break;
        case 'delete_old':
            handleDeleteOld($input['days'] ?? 30);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleMarkAsRead($notificationIds, $category) {
    global $database;
    
    $userId = $_SESSION['user_id'];
    
    if (!empty($notificationIds)) {
        // Mark specific notifications as read
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $params = array_merge($notificationIds, [$userId]);
        
        $result = $database->query(
            "UPDATE notifications SET is_read = TRUE, updated_at = NOW()
             WHERE id IN ({$placeholders}) AND user_id = ?",
            $params
        );

        $affected = $result ? count($notificationIds) : 0;
    } else if ($category) {
        // Mark all notifications in category as read
        $result = $database->query(
            "UPDATE notifications SET is_read = TRUE, updated_at = NOW()
             WHERE user_id = ? AND category = ? AND is_read = FALSE",
            [$userId, $category]
        );

        $affected = $result ? 1 : 0;
    } else {
        // Mark all notifications as read
        $result = $database->query(
            "UPDATE notifications SET is_read = TRUE, updated_at = NOW()
             WHERE user_id = ? AND is_read = FALSE",
            [$userId]
        );

        $affected = $result ? 1 : 0;
    }
    
    echo json_encode([
        'success' => true,
        'data' => ['affected_count' => $affected]
    ]);
}

function handleMarkAsUnread($notificationIds, $category) {
    global $database;
    
    $userId = $_SESSION['user_id'];
    
    if (!empty($notificationIds)) {
        // Mark specific notifications as unread
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $params = array_merge($notificationIds, [$userId]);
        
        $result = $database->query(
            "UPDATE notifications SET is_read = FALSE, updated_at = NOW()
             WHERE id IN ({$placeholders}) AND user_id = ?",
            $params
        );

        $affected = $result ? count($notificationIds) : 0;
    } else if ($category) {
        // Mark all notifications in category as unread
        $result = $database->query(
            "UPDATE notifications SET is_read = FALSE, updated_at = NOW()
             WHERE user_id = ? AND category = ? AND is_read = TRUE",
            [$userId, $category]
        );

        $affected = $result ? 1 : 0;
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Cannot mark all notifications as unread']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'data' => ['affected_count' => $affected]
    ]);
}

function handleBulkDelete($notificationIds, $category) {
    global $database;
    
    $userId = $_SESSION['user_id'];
    
    if (!empty($notificationIds)) {
        // Delete specific notifications
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $params = array_merge($notificationIds, [$userId]);
        
        $result = $database->query(
            "DELETE FROM notifications
             WHERE id IN ({$placeholders}) AND user_id = ?",
            $params
        );

        $affected = $result ? count($notificationIds) : 0;
    } else if ($category) {
        // Delete all notifications in category
        $result = $database->query(
            "DELETE FROM notifications
             WHERE user_id = ? AND category = ?",
            [$userId, $category]
        );

        $affected = $result ? 1 : 0;
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Cannot delete all notifications without specific IDs or category']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'data' => ['affected_count' => $affected]
    ]);
}

function handleDeleteRead($category) {
    global $database;
    
    $userId = $_SESSION['user_id'];
    
    if ($category) {
        $result = $database->query(
            "DELETE FROM notifications
             WHERE user_id = ? AND category = ? AND is_read = TRUE",
            [$userId, $category]
        );
    } else {
        $result = $database->query(
            "DELETE FROM notifications
             WHERE user_id = ? AND is_read = TRUE",
            [$userId]
        );
    }

    $affected = $result ? 1 : 0;
    
    echo json_encode([
        'success' => true,
        'data' => ['affected_count' => $affected]
    ]);
}

function handleDeleteOld($days) {
    global $database;
    
    $userId = $_SESSION['user_id'];
    
    $result = $database->query(
        "DELETE FROM notifications
         WHERE user_id = ? AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
        [$userId, $days]
    );

    $affected = $result ? 1 : 0;
    
    echo json_encode([
        'success' => true,
        'data' => ['affected_count' => $affected]
    ]);
}
?>
