<?php
/**
 * Reviews Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/reviews_functions.php';

// Handle review submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isLoggedIn()) {
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception('Invalid security token. Please refresh the page and try again.');
        }

        // Validate required fields
        $requiredFields = ['title', 'comment', 'rating', 'review_type'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Please fill in all required fields.");
            }
        }

        // Validate rating
        $rating = intval($_POST['rating']);
        if ($rating < 1 || $rating > 5) {
            throw new Exception('Please select a valid rating between 1 and 5 stars.');
        }

        // Validate service or package selection
        $serviceId = !empty($_POST['service_id']) ? sanitize($_POST['service_id']) : null;
        $packageId = !empty($_POST['package_id']) ? sanitize($_POST['package_id']) : null;

        if ($_POST['review_type'] === 'service' && !$serviceId) {
            throw new Exception('Please select a service to review.');
        }

        if ($_POST['review_type'] === 'package' && !$packageId) {
            throw new Exception('Please select a package to review.');
        }

        if (!$serviceId && !$packageId) {
            throw new Exception('Please select either a service or package to review.');
        }

        // Validate UUID format for service_id and package_id
        if ($serviceId && !preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $serviceId)) {
            throw new Exception('Invalid service ID format. Please refresh the page and try again.');
        }

        if ($packageId && !preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $packageId)) {
            throw new Exception('Invalid package ID format. Please refresh the page and try again.');
        }

        // Check if customer has already reviewed this service/package
        $existingReview = getCustomerReview($_SESSION['user_id'], $serviceId, $packageId);
        if ($existingReview) {
            throw new Exception('You have already submitted a review for this service/package.');
        }

        // Check if customer can review (has completed booking)
        $canReview = canCustomerReview($_SESSION['user_id'], $serviceId, $packageId);

        // Prepare review data
        $reviewData = [
            'customer_id' => $_SESSION['user_id'],
            'service_id' => $serviceId,
            'package_id' => $packageId,
            'rating' => $rating,
            'title' => trim($_POST['title']),
            'comment' => trim($_POST['comment']),
            'is_verified' => $canReview
        ];

        // Create the review
        $reviewId = createReview($reviewData);

        if ($reviewId) {
            $successMessage = 'Thank you for your review! It has been submitted for moderation and will appear on the site once approved.';
            // Clear form data by redirecting
            header('Location: reviews.php?success=1');
            exit;
        } else {
            throw new Exception('Failed to submit review. Please try again.');
        }

    } catch (Exception $e) {
        $errorMessage = $e->getMessage();
    }
}

// Check for success message from redirect
if (isset($_GET['success'])) {
    $successMessage = 'Thank you for your review! It has been submitted for moderation and will appear on the site once approved.';
}

// Check if this is an AJAX request for more reviews
$isAjaxRequest = isset($_GET['ajax']) && $_GET['ajax'] === '1';

// Get filter parameters
$filters = [
    'rating' => $_GET['rating'] ?? '',
    'service_id' => $_GET['service_id'] ?? '',
    'package_id' => $_GET['package_id'] ?? '',
    'is_verified' => isset($_GET['verified']) ? ($_GET['verified'] === '1') : '',
    'search' => $_GET['search'] ?? ''
];

// Remove empty filters
$filters = array_filter($filters, function($value) {
    return $value !== '' && $value !== null;
});

if ($isAjaxRequest) {
    // AJAX request for more reviews
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 6; // 5 reviews per page for expanded view

    $reviews = getReviews($filters, $page, $limit);
    $totalReviews = getReviewsCount($filters);
    $totalPages = ceil($totalReviews / $limit);

    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'reviews' => $reviews,
        'totalPages' => $totalPages,
        'currentPage' => $page,
        'totalReviews' => $totalReviews
    ]);
    exit;
} else {
    // Initial page load - get featured reviews first
    $featuredReviews = getFeaturedReviews(3);

    // If we have fewer than 3 featured reviews, fill with highest-rated verified reviews
    if (count($featuredReviews) < 3) {
        $needed = 3 - count($featuredReviews);
        $featuredIds = array_column($featuredReviews, 'id');
        $additionalReviews = getTopRatedReviews($needed, $featuredIds);
        $featuredReviews = array_merge($featuredReviews, $additionalReviews);
    }

    // Get all review statistics
    $reviewStats = getReviewStats();
}

// Get services and packages for filters
$services = getServicesForReview();
$packages = getPackagesForReview();

$pageTitle = "Customer Reviews";
include __DIR__ . '/includes/header.php';
?>

<!-- Hero Section -->
<section class="relative bg-salon-black py-24">
    <div class="absolute inset-0 bg-gradient-to-r from-salon-black via-salon-black/90 to-transparent"></div>
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30" 
         style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Customer <span class="text-salon-gold">Reviews</span>
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                See what our valued clients have to say about their experience at Flix Salon & SPA.
            </p>

            <!-- Success/Error Messages -->
            <?php if (isset($successMessage)): ?>
                <div class="mt-6 max-w-2xl mx-auto bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?= htmlspecialchars($successMessage) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errorMessage)): ?>
                <div class="mt-6 max-w-2xl mx-auto bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <?= htmlspecialchars($errorMessage) ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="py-8 bg-secondary-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <form method="GET" class="bg-secondary-800 rounded-2xl p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <!-- Search -->
                <div>
                    <input type="text" name="search" value="<?= htmlspecialchars($filters['search'] ?? '') ?>"
                           placeholder="Search reviews..."
                           class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>

                <!-- Rating Filter -->
                <div>
                    <select name="rating" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">All Ratings</option>
                        <?php for ($i = 5; $i >= 1; $i--): ?>
                            <option value="<?= $i ?>" <?= ($filters['rating'] ?? '') == $i ? 'selected' : '' ?>>
                                <?= $i ?> Star<?= $i > 1 ? 's' : '' ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>

                <!-- Service Filter -->
                <div>
                    <select name="service_id" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">All Services</option>
                        <?php foreach ($services as $service): ?>
                            <option value="<?= $service['id'] ?>" <?= ($filters['service_id'] ?? '') == $service['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($service['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Package Filter -->
                <div>
                    <select name="package_id" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">All Packages</option>
                        <?php foreach ($packages as $package): ?>
                            <option value="<?= $package['id'] ?>" <?= ($filters['package_id'] ?? '') == $package['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($package['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Verified Filter -->
                <div>
                    <select name="verified" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">All Reviews</option>
                        <option value="1" <?= ($filters['is_verified'] ?? '') === true ? 'selected' : '' ?>>Verified Only</option>
                        <option value="0" <?= ($filters['is_verified'] ?? '') === false ? 'selected' : '' ?>>Unverified Only</option>
                    </select>
                </div>
            </div>

            <div class="flex justify-between items-center mt-4">
                <button type="submit" class="bg-salon-gold hover:bg-yellow-500 text-black px-6 py-2 rounded-lg font-semibold transition-colors">
                    Apply Filters
                </button>
                <a href="reviews.php" class="text-gray-400 hover:text-white transition-colors">
                    Clear Filters
                </a>
            </div>
        </form>
    </div>
</section>

<!-- Overall Rating Section -->
<section class="py-8 bg-secondary-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-secondary-800 rounded-2xl p-8 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                    <div class="text-5xl font-bold text-salon-gold mb-2">
                        <?= $reviewStats['average_rating'] ?? '0.0' ?>
                    </div>
                    <div class="flex justify-center space-x-1 mb-2">
                        <?php
                        $avgRating = $reviewStats['average_rating'] ?? 0;
                        for ($i = 1; $i <= 5; $i++):
                        ?>
                            <i class="fas fa-star <?= $i <= $avgRating ? 'text-salon-gold' : 'text-gray-400' ?> text-lg"></i>
                        <?php endfor; ?>
                    </div>
                    <div class="text-gray-300">Average Rating</div>
                </div>
                <div>
                    <div class="text-5xl font-bold text-salon-gold mb-2">
                        <?= number_format($reviewStats['total_reviews'] ?? 0) ?>
                    </div>
                    <div class="text-gray-300">Total Reviews</div>
                </div>
                <div>
                    <div class="text-5xl font-bold text-salon-gold mb-2">
                        <?= $reviewStats['recommendation_rate'] ?? 0 ?>%
                    </div>
                    <div class="text-gray-300">Recommend Us</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Reviews Grid -->
<section class="py-16 bg-secondary-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Featured Reviews Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-white mb-6 text-center">Featured Reviews</h2>

            <?php if (empty($featuredReviews)): ?>
                <div class="text-center py-12">
                    <div class="text-gray-400 text-lg mb-4">No reviews available yet.</div>
                    <p class="text-gray-500">Be the first to share your experience!</p>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="featured-reviews">
                    <?php foreach ($featuredReviews as $review): ?>
                        <div class="bg-secondary-800 rounded-lg p-6 hover:shadow-xl transition-shadow duration-300 <?= $review['is_featured'] ? 'ring-2 ring-salon-gold/30' : '' ?>">
                            <!-- Featured Badge -->
                            <?php if ($review['is_featured']): ?>
                                <div class="mb-3">
                                    <span class="bg-salon-gold text-black text-xs px-2 py-1 rounded-full font-semibold">
                                        <i class="fas fa-star mr-1"></i>Featured Review
                                    </span>
                                </div>
                            <?php endif; ?>

                            <!-- Header -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                                        <span class="text-salon-gold font-semibold">
                                            <?= strtoupper(substr($review['customer_name'], 0, 1)) ?>
                                        </span>
                                    </div>
                                    <div>
                                        <h3 class="text-white font-semibold"><?= htmlspecialchars($review['customer_name']) ?></h3>
                                        <p class="text-gray-400 text-sm">
                                            <?= htmlspecialchars($review['service_name'] ?? $review['package_name'] ?? 'General Service') ?>
                                        </p>
                                    </div>
                                </div>
                                <?php if ($review['is_verified']): ?>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-check mr-1"></i>Verified
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- Rating and Title -->
                            <div class="mb-4">
                                <div class="flex items-center space-x-1 mb-2">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star <?= $i <= $review['rating'] ? 'text-salon-gold' : 'text-gray-400' ?>"></i>
                                    <?php endfor; ?>
                                    <span class="text-gray-400 text-sm ml-2"><?= date('M j, Y', strtotime($review['created_at'])) ?></span>
                                </div>
                                <h4 class="text-white font-semibold text-lg"><?= htmlspecialchars($review['title']) ?></h4>
                            </div>

                            <!-- Review Text -->
                            <p class="text-gray-300 leading-relaxed mb-4">
                                "<?= htmlspecialchars($review['comment']) ?>"
                            </p>

                            <!-- Actions -->
                            <div class="flex items-center justify-between text-sm">
                                <button class="text-salon-gold hover:text-yellow-400 transition-colors">
                                    <i class="fas fa-thumbs-up mr-1"></i>Helpful
                                </button>
                                <button class="text-gray-400 hover:text-gray-300 transition-colors">
                                    <i class="fas fa-share mr-1"></i>Share
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- View More Button -->
                <div class="text-center mt-12">
                    <button id="view-more-btn" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fas fa-chevron-down mr-2"></i>
                        View More Reviews
                    </button>
                </div>
            <?php endif; ?>
        </div>

        <!-- Expanded Reviews Section (Hidden by default) -->
        <div id="expanded-reviews" class="hidden">
            <!-- Filters Section for Expanded View -->
            <div class="bg-secondary-800 rounded-2xl p-6 mb-8">
                <h3 class="text-xl font-bold text-white mb-4">Filter Reviews</h3>
                <form id="filter-form" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <!-- Search -->
                    <div>
                        <input type="text" name="search" id="search-input" placeholder="Search reviews..."
                               class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>

                    <!-- Rating Filter -->
                    <div>
                        <select name="rating" id="rating-filter" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">All Ratings</option>
                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                <option value="<?= $i ?>"><?= $i ?> Star<?= $i > 1 ? 's' : '' ?></option>
                            <?php endfor; ?>
                        </select>
                    </div>

                    <!-- Service Filter -->
                    <div>
                        <select name="service_id" id="service-filter" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">All Services</option>
                            <?php foreach ($services as $service): ?>
                                <option value="<?= $service['id'] ?>"><?= htmlspecialchars($service['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Package Filter -->
                    <div>
                        <select name="package_id" id="package-filter" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">All Packages</option>
                            <?php foreach ($packages as $package): ?>
                                <option value="<?= $package['id'] ?>"><?= htmlspecialchars($package['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Verified Filter -->
                    <div>
                        <select name="verified" id="verified-filter" class="w-full px-4 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                            <option value="">All Reviews</option>
                            <option value="1">Verified Only</option>
                            <option value="0">Unverified Only</option>
                        </select>
                    </div>
                </form>
            </div>

            <!-- Loading Indicator -->
            <div id="loading-indicator" class="hidden text-center py-8">
                <div class="inline-flex items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-salon-gold mr-3"></div>
                    <span class="text-white">Loading reviews...</span>
                </div>
            </div>

            <!-- Reviews Container -->
            <div id="reviews-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                <!-- Reviews will be loaded here via AJAX -->
            </div>

            <!-- Pagination -->
            <div id="pagination-container" class="hidden">
                <!-- Pagination will be loaded here via AJAX -->
            </div>
        </div>
    </div>
</section>

<!-- Write Review Section -->
<section class="py-20 bg-secondary-900">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <div class="bg-gradient-to-r from-salon-gold/10 to-transparent border border-salon-gold/20 rounded-3xl p-12">
            <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-6">
                Share Your <span class="text-salon-gold">Experience</span>
            </h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Your feedback helps us maintain the highest standards of luxury and excellence. We'd love to hear about your visit to Flix Salon & SPA!
            </p>

            <?php if (!isLoggedIn()): ?>
                <div class="mb-8">
                    <p class="text-gray-300 mb-6">Please login to share your experience with us.</p>
                    <a href="<?= getBasePath() ?>/auth/login.php?redirect=<?= urlencode('/reviews.php') ?>"
                       class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login to Write Review
                    </a>
                </div>
            <?php endif; ?>

            <?php if (isLoggedIn()): ?>
                <form method="POST" class="text-left space-y-6" id="reviewForm">
                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">

                    <!-- Title and Service Type Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-white font-semibold mb-2">Review Title</label>
                            <input type="text" name="title" required
                                   class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                   placeholder="Give your review a title">
                        </div>
                        <div>
                            <label class="block text-white font-semibold mb-2">Service Type</label>
                            <select name="review_type" id="reviewType" required
                                    class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select type</option>
                                <option value="service">Individual Service</option>
                                <option value="package">Service Package</option>
                            </select>
                        </div>
                    </div>

                    <!-- Service/Package Selection -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div id="serviceSelect" style="display: none;">
                            <label class="block text-white font-semibold mb-2">Service</label>
                            <select name="service_id"
                                    class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select a service</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?= $service['id'] ?>"><?= htmlspecialchars($service['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div id="packageSelect" style="display: none;">
                            <label class="block text-white font-semibold mb-2">Package</label>
                            <select name="package_id"
                                    class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">Select a package</option>
                                <?php foreach ($packages as $package): ?>
                                    <option value="<?= $package['id'] ?>"><?= htmlspecialchars($package['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Rating Section -->
                    <div>
                        <label class="block text-white font-semibold mb-2">Rating</label>
                        <div class="flex space-x-2">
                            <input type="hidden" name="rating" id="ratingInput" required>
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <button type="button" class="star-rating text-2xl text-gray-400 hover:text-salon-gold transition-colors" data-rating="<?= $i ?>">
                                    <i class="fas fa-star"></i>
                                </button>
                            <?php endfor; ?>
                        </div>
                        <p class="text-gray-400 text-sm mt-1">Click to rate your experience</p>
                    </div>

                    <!-- Comment Section -->
                    <div>
                        <label class="block text-white font-semibold mb-2">Your Review</label>
                        <textarea name="comment" rows="5" required
                                  class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                  placeholder="Tell us about your experience in detail..."></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Submit Review
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Review Guidelines -->
<section class="py-16 bg-secondary-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-white mb-4">Review Guidelines</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-salon-gold mb-4">What to Include</h3>
                <ul class="space-y-2 text-gray-300">
                    <li class="flex items-start">
                        <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                        Specific details about your service
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                        Staff member who served you
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                        Overall experience and atmosphere
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                        Results and satisfaction level
                    </li>
                </ul>
            </div>
            
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-salon-gold mb-4">Review Standards</h3>
                <ul class="space-y-2 text-gray-300">
                    <li class="flex items-start">
                        <i class="fas fa-info-circle text-salon-gold mr-2 mt-1"></i>
                        Be honest and constructive
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-info-circle text-salon-gold mr-2 mt-1"></i>
                        Keep language respectful
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-info-circle text-salon-gold mr-2 mt-1"></i>
                        Focus on your personal experience
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-info-circle text-salon-gold mr-2 mt-1"></i>
                        Reviews are moderated before posting
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<script>
// Star rating functionality
document.querySelectorAll('.star-rating').forEach(star => {
    star.addEventListener('click', function() {
        const rating = parseInt(this.dataset.rating);
        const stars = document.querySelectorAll('.star-rating');

        // Update visual state
        stars.forEach((s, index) => {
            if (index < rating) {
                s.classList.remove('text-gray-400');
                s.classList.add('text-salon-gold');
            } else {
                s.classList.remove('text-salon-gold');
                s.classList.add('text-gray-400');
            }
        });

        // Update hidden input
        const ratingInput = document.getElementById('ratingInput');
        if (ratingInput) {
            ratingInput.value = rating;
        }
    });
});

// Review type selection functionality
document.addEventListener('DOMContentLoaded', function() {
    const reviewType = document.getElementById('reviewType');
    const serviceSelect = document.getElementById('serviceSelect');
    const packageSelect = document.getElementById('packageSelect');

    if (reviewType) {
        reviewType.addEventListener('change', function() {
            const selectedType = this.value;

            if (selectedType === 'service') {
                serviceSelect.style.display = 'block';
                packageSelect.style.display = 'none';
                serviceSelect.querySelector('select').required = true;
                packageSelect.querySelector('select').required = false;
            } else if (selectedType === 'package') {
                serviceSelect.style.display = 'none';
                packageSelect.style.display = 'block';
                serviceSelect.querySelector('select').required = false;
                packageSelect.querySelector('select').required = true;
            } else {
                serviceSelect.style.display = 'none';
                packageSelect.style.display = 'none';
                serviceSelect.querySelector('select').required = false;
                packageSelect.querySelector('select').required = false;
            }
        });
    }

    // Form submission handling
    const reviewForm = document.getElementById('reviewForm');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            const rating = document.getElementById('ratingInput').value;
            if (!rating) {
                e.preventDefault();
                alert('Please select a rating before submitting your review.');
                return false;
            }
        });
    }
});

// Two-tier review system functionality
document.addEventListener('DOMContentLoaded', function() {
    const viewMoreBtn = document.getElementById('view-more-btn');
    const expandedReviews = document.getElementById('expanded-reviews');
    const reviewsContainer = document.getElementById('reviews-container');
    const paginationContainer = document.getElementById('pagination-container');
    const loadingIndicator = document.getElementById('loading-indicator');
    const filterForm = document.getElementById('filter-form');

    let currentPage = 1;
    let isLoading = false;

    // View More button click handler
    if (viewMoreBtn) {
        viewMoreBtn.addEventListener('click', function() {
            expandedReviews.classList.remove('hidden');
            viewMoreBtn.style.display = 'none';
            loadReviews(1);
        });
    }

    // Filter change handlers
    if (filterForm) {
        const filterInputs = filterForm.querySelectorAll('input, select');
        filterInputs.forEach(input => {
            input.addEventListener('change', function() {
                currentPage = 1;
                loadReviews(1);
            });
        });

        // Search input with debounce
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentPage = 1;
                    loadReviews(1);
                }, 500);
            });
        }
    }

    // Load reviews function
    function loadReviews(page) {
        if (isLoading) return;

        isLoading = true;
        loadingIndicator.classList.remove('hidden');

        // Get filter values
        const formData = new FormData(filterForm);
        const params = new URLSearchParams();
        params.append('ajax', '1');
        params.append('page', page);

        for (let [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        fetch('reviews.php?' + params.toString())
            .then(response => response.json())
            .then(data => {
                displayReviews(data.reviews);
                displayPagination(data.currentPage, data.totalPages, data.totalReviews);
                currentPage = data.currentPage;
            })
            .catch(error => {
                console.error('Error loading reviews:', error);
                reviewsContainer.innerHTML = '<div class="col-span-full text-center text-red-400">Error loading reviews. Please try again.</div>';
            })
            .finally(() => {
                isLoading = false;
                loadingIndicator.classList.add('hidden');
            });
    }

    // Display reviews function
    function displayReviews(reviews) {
        if (reviews.length === 0) {
            reviewsContainer.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="text-gray-400 text-lg mb-4">No reviews found matching your criteria.</div>
                    <button onclick="clearFilters()" class="text-salon-gold hover:text-yellow-400 transition-colors">
                        Clear filters
                    </button>
                </div>
            `;
            paginationContainer.classList.add('hidden');
            return;
        }

        reviewsContainer.innerHTML = reviews.map(review => `
            <div class="bg-secondary-800 rounded-lg p-6 hover:shadow-xl transition-shadow duration-300 ${review.is_featured ? 'ring-2 ring-salon-gold/30' : ''}">
                ${review.is_featured ? `
                    <div class="mb-3">
                        <span class="bg-salon-gold text-black text-xs px-2 py-1 rounded-full font-semibold">
                            <i class="fas fa-star mr-1"></i>Featured Review
                        </span>
                    </div>
                ` : ''}

                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                            <span class="text-salon-gold font-semibold">
                                ${review.customer_name.charAt(0).toUpperCase()}
                            </span>
                        </div>
                        <div>
                            <h3 class="text-white font-semibold">${escapeHtml(review.customer_name)}</h3>
                            <p class="text-gray-400 text-sm">
                                ${escapeHtml(review.service_name || review.package_name || 'General Service')}
                            </p>
                        </div>
                    </div>
                    ${review.is_verified ? `
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            <i class="fas fa-check mr-1"></i>Verified
                        </span>
                    ` : ''}
                </div>

                <div class="mb-4">
                    <div class="flex items-center space-x-1 mb-2">
                        ${Array.from({length: 5}, (_, i) => `
                            <i class="fas fa-star ${i < review.rating ? 'text-salon-gold' : 'text-gray-400'}"></i>
                        `).join('')}
                        <span class="text-gray-400 text-sm ml-2">${formatDate(review.created_at)}</span>
                    </div>
                    <h4 class="text-white font-semibold text-lg">${escapeHtml(review.title)}</h4>
                </div>

                <p class="text-gray-300 leading-relaxed mb-4">
                    "${escapeHtml(review.comment)}"
                </p>

                <div class="flex items-center justify-between text-sm">
                    <button class="text-salon-gold hover:text-yellow-400 transition-colors">
                        <i class="fas fa-thumbs-up mr-1"></i>Helpful
                    </button>
                    <button class="text-gray-400 hover:text-gray-300 transition-colors">
                        <i class="fas fa-share mr-1"></i>Share
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Display pagination function
    function displayPagination(currentPage, totalPages, totalReviews) {
        if (totalPages <= 1) {
            paginationContainer.classList.add('hidden');
            return;
        }

        paginationContainer.classList.remove('hidden');

        let paginationHTML = `
            <div class="flex justify-center items-center space-x-4 mt-12">
                ${currentPage > 1 ? `
                    <button onclick="loadReviews(${currentPage - 1})" class="bg-secondary-800 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-chevron-left mr-2"></i>Previous
                    </button>
                ` : ''}

                <div class="flex space-x-2">
        `;

        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button onclick="loadReviews(${i})" class="px-3 py-2 rounded-lg transition-colors ${i === currentPage ? 'bg-salon-gold text-black' : 'bg-secondary-800 hover:bg-secondary-700 text-white'}">
                    ${i}
                </button>
            `;
        }

        paginationHTML += `
                </div>

                ${currentPage < totalPages ? `
                    <button onclick="loadReviews(${currentPage + 1})" class="bg-secondary-800 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Next<i class="fas fa-chevron-right ml-2"></i>
                    </button>
                ` : ''}
            </div>

            <div class="text-center mt-4 text-gray-400 text-sm">
                Showing ${((currentPage - 1) * 5) + 1} to ${Math.min(currentPage * 5, totalReviews)} of ${totalReviews} reviews
            </div>
        `;

        paginationContainer.innerHTML = paginationHTML;
    }

    // Make loadReviews globally accessible for pagination buttons
    window.loadReviews = loadReviews;
});

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function clearFilters() {
    document.getElementById('search-input').value = '';
    document.getElementById('rating-filter').value = '';
    document.getElementById('service-filter').value = '';
    document.getElementById('package-filter').value = '';
    document.getElementById('verified-filter').value = '';
    loadReviews(1);
}
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
