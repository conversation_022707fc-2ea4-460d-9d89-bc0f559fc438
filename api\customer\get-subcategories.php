<?php
/**
 * Get Subcategories API
 * Flix Salonce - PHP Version
 * Returns subcategories for a given category
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    $categoryName = $_GET['category'] ?? '';
    
    if (empty($categoryName)) {
        echo json_encode([
            'success' => true,
            'subcategories' => []
        ]);
        exit;
    }

    // Get subcategories for the selected category
    $subcategories = $database->fetchAll("
        SELECT ss.* 
        FROM service_subcategories ss
        JOIN service_categories sc ON ss.category_id = sc.id
        WHERE sc.name = ? AND ss.is_active = 1 AND sc.is_active = 1
        ORDER BY ss.sort_order, ss.name
    ", [$categoryName]);

    echo json_encode([
        'success' => true,
        'subcategories' => $subcategories
    ]);

} catch (Exception $e) {
    error_log("Get subcategories error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?> 