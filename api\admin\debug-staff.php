<?php
/**
 * Debug Staff API - Check what staff members exist
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

try {
    global $database;
    
    // Get all users with role STAFF
    $staffUsers = $database->fetchAll("SELECT id, name, email, role, is_active, created_at FROM users WHERE role = 'STAFF'");
    
    // Get all records from staff table (if it exists)
    $staffTable = [];
    try {
        $staffTable = $database->fetchAll("SELECT id, name, email, role, is_active, created_at FROM staff LIMIT 10");
    } catch (Exception $e) {
        $staffTable = ['error' => 'Staff table does not exist or is empty: ' . $e->getMessage()];
    }
    
    // Get all users (to see what's in the database)
    $allUsers = $database->fetchAll("SELECT id, name, email, role, is_active, created_at FROM users ORDER BY role, name LIMIT 20");
    
    // Get some sample bookings to see staff_id references
    $sampleBookings = [];
    try {
        $sampleBookings = $database->fetchAll("SELECT id, staff_id, date, status FROM bookings LIMIT 10");
    } catch (Exception $e) {
        $sampleBookings = ['error' => 'Bookings table issue: ' . $e->getMessage()];
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'staff_users' => $staffUsers,
            'staff_table' => $staffTable,
            'all_users' => $allUsers,
            'sample_bookings' => $sampleBookings,
            'counts' => [
                'staff_users' => count($staffUsers),
                'all_users' => count($allUsers)
            ]
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Debug failed: ' . $e->getMessage()]);
}
?>
