<?php
/**
 * API endpoint to find which page contains a specific service
 * Used for pagination-aware service preselection
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in as customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access'
    ]);
    exit;
}

// Get service ID from query parameter
$serviceId = $_GET['service_id'] ?? null;

if (!$serviceId) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Service ID is required'
    ]);
    exit;
}

try {
    global $database;
    
    // First, verify the service exists and is active
    $service = $database->fetchAll(
        "SELECT id, name FROM services WHERE id = ? AND is_active = 1", 
        [$serviceId]
    );
    
    if (empty($service)) {
        echo json_encode([
            'success' => false,
            'error' => 'Service not found or inactive'
        ]);
        exit;
    }
    
    // Pagination settings (must match the ones in customer/book/index.php)
    $servicesPerPage = 10;
    
    // Get all active services ordered the same way as in the booking page
    $allServices = $database->fetchAll(
        "SELECT id FROM services WHERE is_active = 1 ORDER BY name ASC"
    );
    
    // Find the position of our target service
    $servicePosition = null;
    foreach ($allServices as $index => $svc) {
        if ($svc['id'] == $serviceId) {
            $servicePosition = $index;
            break;
        }
    }
    
    if ($servicePosition === null) {
        echo json_encode([
            'success' => false,
            'error' => 'Service not found in active services list'
        ]);
        exit;
    }
    
    // Calculate which page the service is on
    $page = floor($servicePosition / $servicesPerPage) + 1;
    
    echo json_encode([
        'success' => true,
        'page' => $page,
        'position' => $servicePosition + 1,
        'total_services' => count($allServices),
        'service_name' => $service[0]['name']
    ]);

} catch (Exception $e) {
    error_log("Error in find-service-page.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error'
    ]);
}
?>
