<?php
require_once __DIR__ . '/../../config/app.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Check if Stripe is enabled
if (!STRIPE_ENABLED) {
    redirect('/customer/payments?error=stripe_disabled');
}

$paymentId = $_GET['payment_id'] ?? '';
if (empty($paymentId)) {
    redirect('/customer/payments?error=invalid_payment');
}

global $database;

// Get payment details and verify ownership
$payment = $database->fetch("
    SELECT p.*, b.user_id, b.id as booking_id,
           s.name as service_name, pkg.name as package_name,
           st.name as staff_name
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages pkg ON b.package_id = pkg.id
    LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
    WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'STRIPE'
", [$paymentId, $_SESSION['user_id']]);

if (!$payment) {
    redirect('/customer/payments?error=payment_not_found');
}

// Check if payment is already completed
if ($payment['status'] === 'COMPLETED') {
    redirect('/customer/payments?success=payment_completed');
}

$serviceName = $payment['service_name'] ?: $payment['package_name'] ?: 'Unknown Service';
$pageTitle = "Stripe Payment - " . $serviceName;

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Stripe Payment Page -->
<div class="min-h-screen bg-secondary-900 py-8">
    <div class="max-w-md mx-auto px-4">
        <!-- Payment Header -->
        <div class="bg-secondary-800 rounded-lg p-6 mb-6">
            <div class="text-center mb-4">
                <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
                    </svg>
                </div>
                <h2 class="text-xl font-semibold text-white">Secure Payment with Stripe</h2>
                <p class="text-gray-400 text-sm mt-2">Your payment information is encrypted and secure</p>
            </div>
            
            <!-- Payment Details -->
            <div class="bg-secondary-700 rounded-lg p-4">
                <h3 class="text-white font-medium mb-2"><?= htmlspecialchars($serviceName) ?></h3>
                <p class="text-gray-400 text-sm mb-1">Staff: <?= htmlspecialchars($payment['staff_name']) ?></p>
                <p class="text-gray-400 text-sm mb-3">Reference: <?= htmlspecialchars($payment['payment_reference']) ?></p>
                <div class="flex justify-between items-center">
                    <span class="text-gray-400">Amount:</span>
                    <span class="text-salon-gold text-xl font-semibold"><?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></span>
                </div>
            </div>
        </div>

        <!-- Stripe Payment Form -->
        <div class="bg-secondary-800 rounded-lg p-6">
            <form id="payment-form">
                <div id="payment-element">
                    <!-- Stripe Elements will create form elements here -->
                </div>
                
                <div class="mt-6">
                    <button id="submit-button" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="button-text">Pay <?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></span>
                        <div id="spinner" class="hidden">
                            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>
                        </div>
                    </button>
                </div>
                
                <div id="payment-message" class="hidden mt-4 p-3 rounded-lg"></div>
            </form>
        </div>

        <!-- Security Info -->
        <div class="mt-6 text-center">
            <p class="text-gray-400 text-sm">
                <i class="fas fa-lock mr-1"></i>
                Secured by 256-bit SSL encryption
            </p>
            <div class="flex justify-center items-center mt-2 space-x-4">
                <i class="fab fa-cc-visa text-2xl text-gray-400"></i>
                <i class="fab fa-cc-mastercard text-2xl text-gray-400"></i>
                <i class="fab fa-cc-amex text-2xl text-gray-400"></i>
                <i class="fab fa-apple-pay text-2xl text-gray-400"></i>
                <i class="fab fa-google-pay text-2xl text-gray-400"></i>
            </div>
        </div>

        <!-- Cancel Button -->
        <div class="mt-6 text-center">
            <a href="<?= getBasePath() ?>/customer/payments" class="text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Cancel and go back
            </a>
        </div>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
const stripe = Stripe('<?= STRIPE_PUBLIC_KEY ?>');

let elements;
let paymentElement;

initialize();

async function initialize() {
    try {
        console.log('Initializing Stripe payment...');

        const requestData = {
            payment_id: '<?= $paymentId ?>',
            amount: <?= $payment['amount'] ?>,
            currency: '<?= strtolower(CURRENCY_CODE) ?>'
        };

        console.log('Request data:', requestData);

        // Create payment intent
        const response = await fetch('<?= getBasePath() ?>/api/payments/stripe/create-intent.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`API Error: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log('API Response:', result);

        if (!result.client_secret) {
            console.error('No client_secret in response:', result);
            throw new Error('No client secret received from server');
        }

        console.log('Creating Stripe elements with client_secret:', result.client_secret);

        elements = stripe.elements({ clientSecret: result.client_secret });
        paymentElement = elements.create('payment');
        paymentElement.mount('#payment-element');

        console.log('Stripe payment form initialized successfully');
        showMessage('Payment form loaded successfully', 'success');

    } catch (error) {
        console.error('Initialization error:', error);
        showMessage('Failed to initialize payment form: ' + error.message, 'error');

        // Show additional debug info
        console.error('Debug info:', {
            paymentId: '<?= $paymentId ?>',
            amount: <?= $payment['amount'] ?>,
            currency: '<?= strtolower(CURRENCY_CODE) ?>',
            stripePublicKey: '<?= substr(STRIPE_PUBLIC_KEY, 0, 20) ?>...'
        });
    }
}

document.getElementById('payment-form').addEventListener('submit', handleSubmit);

async function handleSubmit(e) {
    e.preventDefault();
    setLoading(true);

    const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
            return_url: '<?= getBaseUrl() ?>/customer/payments/stripe-return.php?payment_id=<?= $paymentId ?>',
        },
    });

    if (error) {
        if (error.type === "card_error" || error.type === "validation_error") {
            showMessage(error.message, 'error');
        } else {
            showMessage("An unexpected error occurred.", 'error');
        }
    }

    setLoading(false);
}

function showMessage(messageText, type = 'info') {
    const messageContainer = document.querySelector("#payment-message");
    messageContainer.classList.remove('hidden', 'bg-red-100', 'text-red-700', 'bg-green-100', 'text-green-700', 'bg-blue-100', 'text-blue-700');
    
    if (type === 'error') {
        messageContainer.classList.add('bg-red-100', 'text-red-700');
    } else if (type === 'success') {
        messageContainer.classList.add('bg-green-100', 'text-green-700');
    } else {
        messageContainer.classList.add('bg-blue-100', 'text-blue-700');
    }
    
    messageContainer.textContent = messageText;
}

function setLoading(isLoading) {
    const submitButton = document.querySelector("#submit-button");
    const spinner = document.querySelector("#spinner");
    const buttonText = document.querySelector("#button-text");

    if (isLoading) {
        submitButton.disabled = true;
        spinner.classList.remove("hidden");
        buttonText.classList.add("hidden");
    } else {
        submitButton.disabled = false;
        spinner.classList.add("hidden");
        buttonText.classList.remove("hidden");
    }
}
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
