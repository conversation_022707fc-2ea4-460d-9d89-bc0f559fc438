<?php
/**
 * Get Custom Service API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get service ID from query parameter
$serviceId = $_GET['id'] ?? '';

if (empty($serviceId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Service ID is required']);
    exit;
}

try {
    $service = getCustomServiceById($serviceId);
    
    if (!$service) {
        http_response_code(404);
        echo json_encode(['error' => 'Custom service not found']);
        exit;
    }
    
    // Return service data
    echo json_encode($service);
    
} catch (Exception $e) {
    error_log("Get custom service API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>