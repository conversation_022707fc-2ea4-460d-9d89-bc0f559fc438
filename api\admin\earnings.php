<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/earnings_functions.php';

// Check authentication and admin role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetEarnings();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetEarnings() {
    $range = $_GET['range'] ?? 'month';
    $startDate = $_GET['start_date'] ?? null;
    $endDate = $_GET['end_date'] ?? null;
    
    // Validate date range
    if ($startDate && $endDate) {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        
        if ($start > $end) {
            http_response_code(400);
            echo json_encode(['error' => 'Start date must be before end date']);
            return;
        }
        
        $startDate = $start->format('Y-m-d 00:00:00');
        $endDate = $end->format('Y-m-d 23:59:59');
    }
    
    $earnings = getEarningsData($range, $startDate, $endDate);
    
    // Format response
    $response = [
        'success' => true,
        'data' => [
            'totalRevenue' => round($earnings['totalRevenue'], 2),
            'periodRevenue' => round($earnings['periodRevenue'], 2),
            'dailyRevenue' => round($earnings['dailyRevenue'], 2),
            'revenueGrowth' => round($earnings['revenueGrowth'], 2),
            'totalTransactions' => $earnings['totalTransactions'],
            'averageOrderValue' => round($earnings['averageOrderValue'], 2),
            'topServices' => array_map(function($service) {
                return [
                    'name' => $service['name'],
                    'revenue' => round($service['revenue'], 2),
                    'bookings' => $service['bookings']
                ];
            }, $earnings['topServices']),
            'monthlyData' => array_map(function($data) {
                return [
                    'month' => $data['month'],
                    'revenue' => round($data['revenue'], 2),
                    'bookings' => $data['bookings']
                ];
            }, $earnings['monthlyData']),
            'recentTransactions' => array_map(function($transaction) {
                return [
                    'id' => $transaction['id'],
                    'date' => $transaction['date'],
                    'customerName' => $transaction['customerName'],
                    'serviceName' => $transaction['serviceName'],
                    'amount' => round($transaction['amount'], 2),
                    'paymentMethod' => $transaction['paymentMethod'],
                    'status' => $transaction['status']
                ];
            }, $earnings['recentTransactions']),
            'paymentMethods' => array_map(function($method) {
                return [
                    'method' => $method['method'],
                    'amount' => round($method['amount'], 2),
                    'percentage' => $method['percentage'],
                    'transactions' => round($method['transactions'])
                ];
            }, $earnings['paymentMethods']),
            'staffEarnings' => array_map(function($staff) {
                return [
                    'staffName' => $staff['staffName'],
                    'totalBookings' => $staff['totalBookings'],
                    'completedBookings' => $staff['completedBookings'],
                    'totalRevenue' => round($staff['totalRevenue'], 2),
                    'staffCommission' => round($staff['staffCommission'], 2)
                ];
            }, $earnings['staffEarnings'])
        ],
        'meta' => [
            'range' => $range,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'generatedAt' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response);
}

// Additional endpoint for custom reports
if (isset($_GET['action']) && $_GET['action'] === 'custom-report') {
    handleCustomReport();
}

function handleCustomReport() {
    $startDate = $_GET['start_date'] ?? null;
    $endDate = $_GET['end_date'] ?? null;
    $filters = [];
    
    if (!empty($_GET['service_id'])) {
        $filters['service_id'] = $_GET['service_id'];
    }
    
    if (!empty($_GET['staff_id'])) {
        $filters['staff_id'] = $_GET['staff_id'];
    }
    
    if (!$startDate || !$endDate) {
        http_response_code(400);
        echo json_encode(['error' => 'Start date and end date are required for custom reports']);
        return;
    }
    
    $report = getCustomRevenueReport($startDate, $endDate, $filters);
    
    echo json_encode([
        'success' => true,
        'data' => $report,
        'filters' => $filters,
        'dateRange' => [
            'startDate' => $startDate,
            'endDate' => $endDate
        ]
    ]);
}

// Statistics endpoint
if (isset($_GET['action']) && $_GET['action'] === 'statistics') {
    handleStatistics();
}

function handleStatistics() {
    $stats = getEarningsStatistics();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'totalRevenue' => round($stats['total_revenue'], 2),
            'monthlyRevenue' => round($stats['monthly_revenue'], 2),
            'dailyRevenue' => round($stats['daily_revenue'], 2),
            'totalTransactions' => $stats['total_transactions'],
            'averageOrderValue' => round($stats['average_order_value'], 2),
            'monthlyGrowth' => round($stats['monthly_growth'], 2)
        ]
    ]);
}
?>
