<?php
/**
 * Package View Page
 * Flix Salonce - PHP Version
 */

session_start();
require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get package ID from URL
$packageId = $_GET['id'] ?? '';
if (empty($packageId)) {
    $_SESSION['error'] = 'Package ID is required';
    redirect('/admin/packages');
}

// Get package data
$package = getPackageById($packageId);
if (!$package) {
    $_SESSION['error'] = 'Package not found';
    redirect('/admin/packages');
}

// Get package services
$packageServices = getPackageServices($packageId);
$originalPrice = array_sum(array_column($packageServices, 'price'));
$savings = $originalPrice - $package['price'];
$discountPercent = $originalPrice > 0 ? round(($savings / $originalPrice) * 100) : 0;

// Get package performance
$performance = getPackagePerformance($packageId);

$pageTitle = "View Package - " . $package['name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white"><?= htmlspecialchars($package['name']) ?></h1>
                                <p class="mt-1 text-sm text-gray-300">Package Details and Performance</p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-3">
                                <a href="<?= getBasePath() ?>/admin/packages" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                    Back to Packages
                                </a>
                                <button onclick="editPackage('<?= $package['id'] ?>')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                    Edit Package
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Package Details -->
                        <div class="lg:col-span-2">
                            <!-- Package Image -->
                            <?php if (!empty($package['image'])): ?>
                                <?php
                                // Determine image URL - check if it's a URL or uploaded file
                                $imageUrl = filter_var($package['image'], FILTER_VALIDATE_URL)
                                    ? $package['image']
                                    : getBasePath() . '/uploads/packages/' . $package['image'];
                                ?>
                                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                    <h2 class="text-lg font-semibold text-white mb-4">Package Image</h2>
                                    <div class="rounded-lg overflow-hidden">
                                        <img src="<?= htmlspecialchars($imageUrl) ?>"
                                             alt="<?= htmlspecialchars($package['name']) ?>"
                                             class="w-full h-64 object-cover">
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h2 class="text-lg font-semibold text-white mb-4">Package Information</h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-1">Package Name</label>
                                        <p class="text-white"><?= htmlspecialchars($package['name']) ?></p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $package['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                            <?= $package['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </div>
                                    
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                                        <p class="text-gray-300"><?= $package['description'] ? htmlspecialchars($package['description']) : 'No description provided' ?></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing Information -->
                            <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                                <h2 class="text-lg font-semibold text-white mb-4">Pricing Details</h2>
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-salon-gold"><?= formatCurrency($package['price']) ?></div>
                                        <div class="text-sm text-gray-300">Package Price</div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-400 line-through"><?= formatCurrency($originalPrice) ?></div>
                                        <div class="text-sm text-gray-300">Original Price</div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-400"><?= formatCurrency($savings) ?></div>
                                        <div class="text-sm text-gray-300">Savings (<?= $discountPercent ?>%)</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Included Services -->
                            <div class="bg-secondary-800 shadow rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-white mb-4">Included Services</h2>
                                
                                <div class="space-y-4">
                                    <?php foreach ($packageServices as $service): ?>
                                        <div class="flex items-center justify-between p-4 bg-secondary-700 rounded-lg">
                                            <div class="flex-1">
                                                <h3 class="text-white font-medium"><?= htmlspecialchars($service['name']) ?></h3>
                                                <p class="text-sm text-gray-400"><?= $service['duration'] ?> minutes</p>
                                                <?php if ($service['description']): ?>
                                                    <p class="text-sm text-gray-300 mt-1"><?= htmlspecialchars($service['description']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-lg font-semibold text-salon-gold"><?= formatCurrency($service['price']) ?></div>
                                                <div class="text-xs text-gray-400">Individual Price</div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Stats -->
                        <div class="lg:col-span-1">
                            <div class="bg-secondary-800 shadow rounded-lg p-6">
                                <h2 class="text-lg font-semibold text-white mb-4">Performance</h2>
                                
                                <div class="space-y-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-white"><?= number_format($performance['total_bookings']) ?></div>
                                        <div class="text-sm text-gray-300">Total Bookings</div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-green-400"><?= number_format($performance['completed_bookings']) ?></div>
                                        <div class="text-sm text-gray-300">Completed</div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-salon-gold"><?= formatCurrency($performance['total_revenue']) ?></div>
                                        <div class="text-sm text-gray-300">Total Revenue</div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-blue-400"><?= $performance['conversion_rate'] ?>%</div>
                                        <div class="text-sm text-gray-300">Conversion Rate</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<script>
function editPackage(packageId) {
    window.location.href = `<?= getBasePath() ?>/admin/packages?edit=${packageId}`;
}
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
