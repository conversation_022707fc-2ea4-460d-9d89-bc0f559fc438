<?php
/**
 * Fix Customer Portal Currency Issue
 * Specifically targets the 262145 currency symbol issue in customer portal
 */

require_once __DIR__ . '/config/app.php';

header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Customer Currency Issue - Flix Salon & SPA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .section {
            background: #2a2a2a;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #d4af37;
        }
        .error {
            border-left-color: #dc3545;
            background: #3a1e1e;
        }
        .warning {
            border-left-color: #ffc107;
            background: #3a3a1e;
        }
        .success {
            border-left-color: #28a745;
            background: #1e3a1e;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #d4af37;
            color: #000;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #b8941f;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .code {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Customer Portal Currency Issue</h1>
            <p><strong>Issue:</strong> Currency symbol showing "262145" instead of "TSH" in customer portal</p>
            <p><strong>Target:</strong> https://flix.co.tz/flix/customer/</p>
            <p><strong>Current Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
        </div>

        <!-- Current Status Check -->
        <div class="section">
            <h2>📊 Current Status Check</h2>
            <?php
            try {
                // Check database settings
                $currencySymbol = getSetting('business', 'currency_symbol', 'NOT_FOUND');
                $currencyCode = getSetting('business', 'currency_code', 'NOT_FOUND');
                
                echo "<h3>Database Settings:</h3>";
                echo "<p><strong>currency_symbol:</strong> " . htmlspecialchars($currencySymbol) . "</p>";
                echo "<p><strong>currency_code:</strong> " . htmlspecialchars($currencyCode) . "</p>";
                
                // Check for corrupted values
                $corruptedCount = $database->fetchColumn("
                    SELECT COUNT(*) 
                    FROM settings 
                    WHERE setting_value = '262145' OR setting_value = 262145
                ");
                
                if ($corruptedCount > 0) {
                    echo "<div class='error'>❌ Found $corruptedCount corrupted settings with value 262145</div>";
                } else {
                    echo "<div class='success'>✅ No corrupted 262145 values found in settings</div>";
                }
                
                // Test formatCurrency function
                echo "<h3>formatCurrency() Test:</h3>";
                $testAmount = 50000;
                $formatted = formatCurrency($testAmount);
                echo "<p>formatCurrency($testAmount) = <strong>$formatted</strong></p>";
                
                if (strpos($formatted, '262145') !== false) {
                    echo "<div class='error'>❌ formatCurrency still showing 262145!</div>";
                } else {
                    echo "<div class='success'>✅ formatCurrency working correctly</div>";
                }
                
                // Test constants
                echo "<h3>PHP Constants:</h3>";
                echo "<p>CURRENCY_SYMBOL = " . htmlspecialchars(CURRENCY_SYMBOL) . "</p>";
                echo "<p>CURRENCY_CODE = " . htmlspecialchars(CURRENCY_CODE) . "</p>";
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error checking status: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
            ?>
        </div>

        <!-- Apply Fix -->
        <div class="section">
            <h2>🔧 Apply Customer Portal Fix</h2>
            
            <?php
            $applyFix = isset($_POST['apply_customer_fix']) && $_POST['apply_customer_fix'] === '1';
            
            if ($applyFix) {
                echo "<h3>Applying Customer Portal Currency Fix...</h3>";
                
                try {
                    $database->beginTransaction();
                    $fixesApplied = 0;
                    
                    // Fix 1: Update any corrupted currency settings
                    $corruptedSettings = $database->fetchAll("
                        SELECT id, category, setting_key, setting_value 
                        FROM settings 
                        WHERE setting_value = '262145' OR setting_value = 262145
                    ");
                    
                    foreach ($corruptedSettings as $setting) {
                        $newValue = 'TSH'; // Default to TSH for currency symbols
                        
                        // Determine correct value based on setting key
                        if (stripos($setting['setting_key'], 'code') !== false) {
                            $newValue = 'TZS';
                        }
                        
                        $database->execute("
                            UPDATE settings 
                            SET setting_value = ?, updated_at = NOW() 
                            WHERE id = ?
                        ", [$newValue, $setting['id']]);
                        
                        echo "<p>✅ Fixed {$setting['category']}.{$setting['setting_key']}: 262145 → $newValue</p>";
                        $fixesApplied++;
                    }
                    
                    // Fix 2: Ensure currency_symbol exists and is correct
                    $currencySymbolSetting = $database->fetch("
                        SELECT id, setting_value 
                        FROM settings 
                        WHERE category = 'business' AND setting_key = 'currency_symbol'
                    ");
                    
                    if ($currencySymbolSetting) {
                        if ($currencySymbolSetting['setting_value'] !== 'TSH') {
                            $database->execute("
                                UPDATE settings 
                                SET setting_value = 'TSH', updated_at = NOW() 
                                WHERE category = 'business' AND setting_key = 'currency_symbol'
                            ");
                            echo "<p>✅ Updated business.currency_symbol: {$currencySymbolSetting['setting_value']} → TSH</p>";
                            $fixesApplied++;
                        } else {
                            echo "<p>✅ business.currency_symbol is already correct: TSH</p>";
                        }
                    } else {
                        // Insert the setting if it doesn't exist
                        $database->execute("
                            INSERT INTO settings (id, category, setting_key, setting_value, created_at, updated_at) 
                            VALUES (?, 'business', 'currency_symbol', 'TSH', NOW(), NOW())
                        ", [generateUUID()]);
                        echo "<p>✅ Created business.currency_symbol: TSH</p>";
                        $fixesApplied++;
                    }
                    
                    // Fix 3: Ensure currency_code exists and is correct
                    $currencyCodeSetting = $database->fetch("
                        SELECT id, setting_value 
                        FROM settings 
                        WHERE category = 'business' AND setting_key = 'currency_code'
                    ");
                    
                    if ($currencyCodeSetting) {
                        if ($currencyCodeSetting['setting_value'] !== 'TZS') {
                            $database->execute("
                                UPDATE settings 
                                SET setting_value = 'TZS', updated_at = NOW() 
                                WHERE category = 'business' AND setting_key = 'currency_code'
                            ");
                            echo "<p>✅ Updated business.currency_code: {$currencyCodeSetting['setting_value']} → TZS</p>";
                            $fixesApplied++;
                        } else {
                            echo "<p>✅ business.currency_code is already correct: TZS</p>";
                        }
                    } else {
                        // Insert the setting if it doesn't exist
                        $database->execute("
                            INSERT INTO settings (id, category, setting_key, setting_value, created_at, updated_at) 
                            VALUES (?, 'business', 'currency_code', 'TZS', NOW(), NOW())
                        ", [generateUUID()]);
                        echo "<p>✅ Created business.currency_code: TZS</p>";
                        $fixesApplied++;
                    }
                    
                    // Fix 4: Clear any cached values
                    if (function_exists('opcache_reset')) {
                        opcache_reset();
                        echo "<p>✅ Cleared OPCache</p>";
                    }
                    
                    $database->commit();
                    
                    echo "<div class='success'>";
                    echo "<h3>✅ Customer Portal Currency Fix Applied Successfully!</h3>";
                    echo "<p>Total fixes applied: $fixesApplied</p>";
                    echo "<p>The customer portal should now display currency as TSH instead of 262145.</p>";
                    echo "</div>";
                    
                    // Test again after fix
                    echo "<h3>Post-Fix Verification:</h3>";
                    $testAmount = 50000;
                    $formatted = formatCurrency($testAmount);
                    echo "<p>formatCurrency($testAmount) = <strong>$formatted</strong></p>";
                    
                    $newSymbol = getSetting('business', 'currency_symbol', 'ERROR');
                    echo "<p>Database currency_symbol = <strong>$newSymbol</strong></p>";
                    
                    if (strpos($formatted, '262145') !== false || $newSymbol === '262145') {
                        echo "<div class='error'>❌ Issue still persists - may need additional investigation</div>";
                    } else {
                        echo "<div class='success'>✅ Customer portal currency fix is working correctly!</div>";
                    }
                    
                } catch (Exception $e) {
                    $database->rollback();
                    echo "<div class='error'>";
                    echo "<h3>❌ Error applying customer portal fix:</h3>";
                    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "</div>";
                }
            }
            ?>
            
            <?php if (!$applyFix): ?>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="apply_customer_fix" value="1">
                <button type="submit" class="btn btn-danger" onclick="return confirm('Apply customer portal currency fix? This will update corrupted currency settings in the database.')">
                    🔧 Apply Customer Portal Fix
                </button>
            </form>
            <?php endif; ?>
        </div>

        <!-- Test Customer Portal -->
        <div class="section">
            <h2>🧪 Test Customer Portal</h2>
            <p>After applying the fix, test these customer portal pages:</p>
            
            <a href="/customer/" class="btn">Customer Dashboard</a>
            <a href="/customer/bookings/" class="btn">Customer Bookings</a>
            <a href="/customer/book/" class="btn">Book Service</a>
            <a href="/customer/payments/" class="btn">Payments</a>
            
            <p><strong>Expected Result:</strong> All currency displays should show "TSH 50,000" format, not "262145 50,000"</p>
            
            <h3>Live Test Results:</h3>
            <div class="code">
Current formatCurrency tests:
formatCurrency(35000) = <?= formatCurrency(35000) ?>

formatCurrency(50000) = <?= formatCurrency(50000) ?>

formatCurrency(100000) = <?= formatCurrency(100000) ?>

Direct constant test: <?= CURRENCY_SYMBOL ?> 50,000
            </div>
        </div>
    </div>
</body>
</html>
