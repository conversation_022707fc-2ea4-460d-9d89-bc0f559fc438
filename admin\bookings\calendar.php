<?php
/**
 * Admin Bookings Calendar View
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get current month and year
$currentMonth = (int)($_GET['month'] ?? date('n'));
$currentYear = (int)($_GET['year'] ?? date('Y'));

// Validate month and year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = date('n');
}
if ($currentYear < 2020 || $currentYear > 2030) {
    $currentYear = date('Y');
}

// Get first and last day of the month
$firstDay = mktime(0, 0, 0, $currentMonth, 1, $currentYear);
$lastDay = mktime(0, 0, 0, $currentMonth + 1, 0, $currentYear);
$daysInMonth = date('t', $firstDay);
$startDayOfWeek = date('w', $firstDay); // 0 = Sunday

// Get bookings for the month
$bookings = $database->fetchAll(
    "SELECT b.*, u.name as customer_name, s.name as service_name, st.name as staff_name
     FROM bookings b
     LEFT JOIN users u ON b.user_id = u.id
     LEFT JOIN services s ON b.service_id = s.id
     LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
     WHERE YEAR(b.date) = ? AND MONTH(b.date) = ?
     GROUP BY b.id
     ORDER BY b.date, b.start_time",
    [$currentYear, $currentMonth]
);

// Group bookings by date
$bookingsByDate = [];
foreach ($bookings as $booking) {
    $day = (int)date('j', strtotime($booking['date']));
    if (!isset($bookingsByDate[$day])) {
        $bookingsByDate[$day] = [];
    }
    $bookingsByDate[$day][] = $booking;
}

$pageTitle = "Bookings Calendar";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>
            
            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-white">Bookings Calendar</h1>
                                <p class="mt-1 text-sm text-gray-300"><?= date('F Y', $firstDay) ?></p>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-2">
                                <a href="<?= getBasePath() ?>/admin/bookings" 
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    List View
                                </a>
                                <a href="<?= getBasePath() ?>/admin/bookings/create" 
                                   class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    New Booking
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Calendar Navigation -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <a href="?month=<?= $currentMonth == 1 ? 12 : $currentMonth - 1 ?>&year=<?= $currentMonth == 1 ? $currentYear - 1 : $currentYear ?>" 
                                   class="bg-secondary-700 text-white px-3 py-2 rounded-lg hover:bg-secondary-600 transition-colors">
                                    ← Previous
                                </a>
                                <h2 class="text-xl font-semibold text-white"><?= date('F Y', $firstDay) ?></h2>
                                <a href="?month=<?= $currentMonth == 12 ? 1 : $currentMonth + 1 ?>&year=<?= $currentMonth == 12 ? $currentYear + 1 : $currentYear ?>" 
                                   class="bg-secondary-700 text-white px-3 py-2 rounded-lg hover:bg-secondary-600 transition-colors">
                                    Next →
                                </a>
                            </div>
                            <div class="flex items-center gap-2">
                                <a href="?month=<?= date('n') ?>&year=<?= date('Y') ?>" 
                                   class="bg-salon-gold text-black px-3 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                    Today
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Calendar -->
                    <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                        <!-- Calendar Header -->
                        <div class="grid grid-cols-7 bg-secondary-700">
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Sun</div>
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Mon</div>
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Tue</div>
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Wed</div>
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Thu</div>
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Fri</div>
                            <div class="p-4 text-center text-sm font-medium text-gray-300">Sat</div>
                        </div>

                        <!-- Calendar Body -->
                        <div class="grid grid-cols-7">
                            <?php
                            // Add empty cells for days before the first day of the month
                            for ($i = 0; $i < $startDayOfWeek; $i++) {
                                echo '<div class="h-32 bg-secondary-900 border-r border-b border-secondary-700"></div>';
                            }

                            // Add cells for each day of the month
                            for ($day = 1; $day <= $daysInMonth; $day++) {
                                $isToday = ($day == date('j') && $currentMonth == date('n') && $currentYear == date('Y'));
                                $dayBookings = $bookingsByDate[$day] ?? [];
                                $dateString = sprintf('%04d-%02d-%02d', $currentYear, $currentMonth, $day);

                                echo '<div class="h-32 bg-secondary-800 border-r border-b border-secondary-700 p-2 relative overflow-hidden cursor-pointer hover:bg-secondary-700 transition-colors duration-200" onclick="openDayView(\'' . $dateString . '\')">';
                                echo '<div class="flex justify-between items-start mb-1">';
                                echo '<span class="text-sm font-medium text-white' . ($isToday ? ' bg-salon-gold text-black px-2 py-1 rounded' : '') . '">' . $day . '</span>';
                                if (count($dayBookings) > 0) {
                                    echo '<span class="text-xs bg-blue-600 text-white px-1 py-0.5 rounded font-medium">' . count($dayBookings) . '</span>';
                                }
                                echo '</div>';
                                
                                // Show bookings for this day (max 2 for better visibility)
                                $displayedBookings = array_slice($dayBookings, 0, 2);
                                foreach ($displayedBookings as $booking) {
                                    $statusColor = '';
                                    $statusIcon = '';
                                    switch ($booking['status']) {
                                        case 'PENDING':
                                            $statusColor = 'bg-yellow-600 hover:bg-yellow-500';
                                            $statusIcon = '⏳';
                                            break;
                                        case 'CONFIRMED':
                                            $statusColor = 'bg-blue-600 hover:bg-blue-500';
                                            $statusIcon = '✓';
                                            break;
                                        case 'COMPLETED':
                                            $statusColor = 'bg-green-600 hover:bg-green-500';
                                            $statusIcon = '✅';
                                            break;
                                        case 'CANCELLED':
                                            $statusColor = 'bg-red-600 hover:bg-red-500';
                                            $statusIcon = '❌';
                                            break;
                                        case 'NO_SHOW':
                                            $statusColor = 'bg-gray-600 hover:bg-gray-500';
                                            $statusIcon = '👻';
                                            break;
                                        case 'IN_PROGRESS':
                                            $statusColor = 'bg-purple-600 hover:bg-purple-500';
                                            $statusIcon = '🔄';
                                            break;
                                        case 'EXPIRED':
                                            $statusColor = 'bg-orange-600 hover:bg-orange-500';
                                            $statusIcon = '⏰';
                                            break;
                                    }

                                    echo '<div class="text-xs ' . $statusColor . ' text-white p-1.5 rounded-md mb-1 cursor-pointer transition-all duration-200 transform hover:scale-105 shadow-sm" onclick="event.stopPropagation(); viewBooking(\'' . $booking['id'] . '\')" title="Click to view booking details">';
                                    echo '<div class="flex items-center justify-between">';
                                    echo '<span class="font-medium">' . date('g:i A', strtotime($booking['start_time'])) . '</span>';
                                    echo '<span class="text-xs">' . $statusIcon . '</span>';
                                    echo '</div>';
                                    echo '<div class="truncate font-medium">' . htmlspecialchars($booking['customer_name']) . '</div>';
                                    if ($booking['service_name']) {
                                        echo '<div class="truncate text-xs opacity-90">' . htmlspecialchars($booking['service_name']) . '</div>';
                                    }
                                    echo '</div>';
                                }

                                // Show "more" indicator if there are more bookings
                                if (count($dayBookings) > 2) {
                                    echo '<div class="text-xs text-salon-gold font-medium bg-secondary-700 rounded px-2 py-1 text-center hover:bg-secondary-600 transition-colors">+' . (count($dayBookings) - 2) . ' more</div>';
                                }
                                
                                echo '</div>';
                            }

                            // Add empty cells to complete the last week
                            $totalCells = $startDayOfWeek + $daysInMonth;
                            $remainingCells = 7 - ($totalCells % 7);
                            if ($remainingCells < 7) {
                                for ($i = 0; $i < $remainingCells; $i++) {
                                    echo '<div class="h-32 bg-secondary-900 border-r border-b border-secondary-700"></div>';
                                }
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Legend -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mt-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Status Legend & Instructions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-300 mb-3">Booking Status</h4>
                                <div class="flex flex-wrap gap-3">
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-yellow-600 rounded"></div>
                                        <span class="text-sm text-gray-300">⏳ Pending</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-blue-600 rounded"></div>
                                        <span class="text-sm text-gray-300">✓ Confirmed</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-purple-600 rounded"></div>
                                        <span class="text-sm text-gray-300">🔄 In Progress</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-green-600 rounded"></div>
                                        <span class="text-sm text-gray-300">✅ Completed</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-red-600 rounded"></div>
                                        <span class="text-sm text-gray-300">❌ Cancelled</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-gray-600 rounded"></div>
                                        <span class="text-sm text-gray-300">👻 No Show</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <div class="w-4 h-4 bg-orange-600 rounded"></div>
                                        <span class="text-sm text-gray-300">⏰ Expired</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-300 mb-3">How to Use</h4>
                                <ul class="text-sm text-gray-300 space-y-1">
                                    <li>• <strong>Click on any date</strong> to view all bookings for that day</li>
                                    <li>• <strong>Click on a booking</strong> to view detailed information</li>
                                    <li>• <strong>Hover over dates</strong> to see interactive highlights</li>
                                    <li>• Numbers in blue show total bookings per day</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Day View Modal -->
<div id="dayViewModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        <div class="flex justify-between items-center p-6 border-b border-secondary-700">
            <div>
                <h2 class="text-xl font-bold text-white" id="dayViewTitle">Bookings for Date</h2>
                <p class="text-sm text-gray-300" id="dayViewSubtitle">Loading...</p>
            </div>
            <button onclick="closeDayView()" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]" id="dayViewContent">
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-salon-gold mx-auto"></div>
                <p class="text-gray-300 mt-2">Loading bookings...</p>
            </div>
        </div>

        <div class="flex justify-between items-center p-6 border-t border-secondary-700 bg-secondary-700">
            <div class="flex gap-2">
                <button onclick="createNewBooking()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    New Booking
                </button>
            </div>
            <button onclick="closeDayView()" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Modern Notification System -->
<div id="notificationContainer" class="fixed top-4 right-4 z-[80] space-y-2"></div>

<script>
// Global variables
let currentViewDate = null;

// Modern Notification System
function showNotification(message, type = 'success', duration = 5000) {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');

    // Set notification styles based on type
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-green-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>`;
            break;
        case 'error':
            bgColor = 'bg-red-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>`;
            break;
        case 'warning':
            bgColor = 'bg-yellow-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>`;
            break;
        case 'info':
            bgColor = 'bg-blue-600';
            textColor = 'text-white';
            icon = `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>`;
            break;
        default:
            bgColor = 'bg-gray-600';
            textColor = 'text-white';
            icon = '';
    }

    notification.className = `${bgColor} ${textColor} px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 min-w-80 max-w-md transform transition-all duration-300 ease-in-out translate-x-full opacity-0`;
    notification.innerHTML = `
        <div class="flex-shrink-0">${icon}</div>
        <div class="flex-1">
            <p class="font-medium">${message}</p>
        </div>
        <button onclick="removeNotification(this.parentElement)" class="flex-shrink-0 ml-4 text-white hover:text-gray-200">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

    container.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
        notification.classList.remove('translate-x-full', 'opacity-0');
        notification.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Auto remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification);
        }, duration);
    }

    return notification;
}

function removeNotification(notification) {
    notification.classList.add('translate-x-full', 'opacity-0');
    setTimeout(() => {
        if (notification.parentElement) {
            notification.parentElement.removeChild(notification);
        }
    }, 300);
}

function viewBooking(bookingId) {
    window.location.href = `<?= getBasePath() ?>/admin/bookings/view.php?id=${bookingId}`;
}

function createNewBooking() {
    const url = new URL('<?= getBasePath() ?>/admin/bookings/create', window.location.origin);
    if (currentViewDate) {
        url.searchParams.set('date', currentViewDate);
    }
    window.location.href = url.toString();
}

// Day View Modal Functions
function openDayView(date) {
    console.log('📅 Opening day view for:', date);
    currentViewDate = date;

    // Show modal
    document.getElementById('dayViewModal').classList.remove('hidden');

    // Update title
    const dateObj = new Date(date + 'T00:00:00');
    const formattedDate = dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    document.getElementById('dayViewTitle').textContent = `Bookings for ${formattedDate}`;
    document.getElementById('dayViewSubtitle').textContent = 'Loading bookings...';

    // Load bookings for the day
    loadDayBookings(date);
}

function closeDayView() {
    document.getElementById('dayViewModal').classList.add('hidden');
    currentViewDate = null;
}

function loadDayBookings(date) {
    fetch(`<?= getBasePath() ?>/api/admin/day-bookings.php?date=${date}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDayBookings(data.bookings, data.summary, date);
            } else {
                document.getElementById('dayViewContent').innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-red-400">Error loading bookings: ${data.error}</p>
                        <button onclick="loadDayBookings('${date}')" class="mt-4 bg-salon-gold text-black px-4 py-2 rounded-lg">Retry</button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('dayViewContent').innerHTML = `
                <div class="text-center py-8">
                    <p class="text-red-400">Error loading bookings</p>
                    <button onclick="loadDayBookings('${date}')" class="mt-4 bg-salon-gold text-black px-4 py-2 rounded-lg">Retry</button>
                </div>
            `;
        });
}

function displayDayBookings(bookings, summary, date) {
    console.log('📋 Displaying bookings for', date, ':', bookings.length, 'bookings');

    // Update subtitle with summary
    const revenueText = summary.completed_bookings > 0
        ? `Revenue from ${summary.completed_bookings} completed: ${formatCurrency(summary.total_revenue)}`
        : 'No completed bookings yet';
    const subtitle = `${summary.total_bookings} booking${summary.total_bookings !== 1 ? 's' : ''} • ${revenueText}`;
    document.getElementById('dayViewSubtitle').textContent = subtitle;

    if (bookings.length === 0) {
        document.getElementById('dayViewContent').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-secondary-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-white mb-2">No bookings for this day</h3>
                <p class="text-gray-400 mb-6">This day is completely free. Perfect time to schedule new appointments!</p>
                <button onclick="createNewBooking()" class="bg-salon-gold text-black px-6 py-3 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create First Booking
                </button>
            </div>
        `;
        return;
    }

    // Create summary cards
    let summaryHtml = `
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-secondary-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-300">Total Bookings</p>
                        <p class="text-lg font-semibold text-white">${summary.total_bookings}</p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-300">Total Revenue</p>
                        <p class="text-lg font-semibold text-white">${formatCurrency(summary.total_revenue)}</p>
                        ${summary.completed_bookings > 0 ? `<p class="text-xs text-gray-400">${summary.completed_bookings} completed booking${summary.completed_bookings !== 1 ? 's' : ''}</p>` : '<p class="text-xs text-gray-400">No completed bookings</p>'}
                    </div>
                </div>
            </div>

            <div class="bg-secondary-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-600 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-300">Pending</p>
                        <p class="text-lg font-semibold text-white">${summary.status_counts.PENDING || 0}</p>
                    </div>
                </div>
            </div>

            <div class="bg-secondary-700 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-300">Completed</p>
                        <p class="text-lg font-semibold text-white">${summary.status_counts.COMPLETED || 0}</p>
                        <p class="text-xs text-gray-400">Revenue generating</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Create bookings list
    let bookingsHtml = '<div class="space-y-4">';

    bookings.forEach(booking => {
        const statusInfo = getStatusInfo(booking.status);
        const serviceName = booking.service_name || booking.package_name || 'Unknown Service';
        const serviceType = booking.service_name ? 'Service' : 'Package';

        bookingsHtml += `
            <div class="bg-secondary-700 rounded-lg p-4 hover:bg-secondary-600 transition-colors cursor-pointer" onclick="viewBooking('${booking.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 ${statusInfo.bgColor} rounded-lg flex items-center justify-center">
                                <span class="text-white font-medium">${statusInfo.icon}</span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2">
                                <h3 class="text-lg font-medium text-white truncate">${booking.customer_name}</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.badgeClass}">
                                    ${booking.status}
                                </span>
                            </div>
                            <div class="mt-1 flex items-center space-x-4 text-sm text-gray-300">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    ${formatTime(booking.start_time)} - ${formatTime(booking.end_time)}
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    ${booking.staff_name || 'Unassigned'}
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    ${serviceName} (${serviceType})
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <div class="text-lg font-semibold text-salon-gold">${formatCurrency(booking.total_amount)}</div>
                            ${booking.points_used > 0 ? `<div class="text-xs text-gray-400">${booking.points_used} points used</div>` : ''}
                        </div>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
                ${booking.notes ? `<div class="mt-3 text-sm text-gray-400 bg-secondary-800 rounded p-2"><strong>Notes:</strong> ${booking.notes}</div>` : ''}
            </div>
        `;
    });

    bookingsHtml += '</div>';

    document.getElementById('dayViewContent').innerHTML = summaryHtml + bookingsHtml;
}

// Helper functions
function formatCurrency(amount) {
    return 'TSH ' + parseInt(amount).toLocaleString();
}

function formatTime(timeString) {
    const time = new Date('2000-01-01 ' + timeString);
    return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function getStatusInfo(status) {
    const statusMap = {
        'PENDING': {
            icon: '⏳',
            bgColor: 'bg-yellow-600',
            badgeClass: 'bg-yellow-100 text-yellow-800'
        },
        'CONFIRMED': {
            icon: '✓',
            bgColor: 'bg-blue-600',
            badgeClass: 'bg-blue-100 text-blue-800'
        },
        'IN_PROGRESS': {
            icon: '🔄',
            bgColor: 'bg-purple-600',
            badgeClass: 'bg-purple-100 text-purple-800'
        },
        'COMPLETED': {
            icon: '✅',
            bgColor: 'bg-green-600',
            badgeClass: 'bg-green-100 text-green-800'
        },
        'CANCELLED': {
            icon: '❌',
            bgColor: 'bg-red-600',
            badgeClass: 'bg-red-100 text-red-800'
        },
        'NO_SHOW': {
            icon: '👻',
            bgColor: 'bg-gray-600',
            badgeClass: 'bg-gray-100 text-gray-800'
        },
        'EXPIRED': {
            icon: '⏰',
            bgColor: 'bg-orange-600',
            badgeClass: 'bg-orange-100 text-orange-800'
        }
    };

    return statusMap[status] || {
        icon: '❓',
        bgColor: 'bg-gray-600',
        badgeClass: 'bg-gray-100 text-gray-800'
    };
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDayView();
    }
});

// Close modal on backdrop click
document.getElementById('dayViewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDayView();
    }
});
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
