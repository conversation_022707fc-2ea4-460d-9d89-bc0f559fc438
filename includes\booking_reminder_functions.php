<?php
/**
 * Booking Reminder Functions
 * Comprehensive reminder system with tracking and fail-safes
 */

/**
 * Create reminder tables if they don't exist
 */
function createReminderTables() {
    global $database;

    try {
        // Check if tables already exist
        $reminderTableExists = $database->fetch("SHOW TABLES LIKE 'booking_reminders'");
        $logTableExists = $database->fetch("SHOW TABLES LIKE 'reminder_logs'");

        if (!$reminderTableExists) {
            // Create booking_reminders table
            $database->query("
                CREATE TABLE booking_reminders (
                    id VARCHAR(36) PRIMARY KEY,
                    booking_id VARCHAR(36) NOT NULL,
                    reminder_type ENUM('24_HOURS', '1_DAY', '30_MINUTES', 'AT_TIME') NOT NULL,
                    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
                    status ENUM('PENDING', 'SENT', 'FAILED', 'SKIPPED') NOT NULL DEFAULT 'PENDING',
                    scheduled_time DATETIME NOT NULL,
                    sent_time DATETIME NULL,
                    attempts INT DEFAULT 0,
                    max_attempts INT DEFAULT 3,
                    customer_email_sent BOOLEAN DEFAULT FALSE,
                    staff_email_sent BOOLEAN DEFAULT FALSE,
                    customer_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
                    staff_email_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING',
                    error_message TEXT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                    INDEX idx_booking_id (booking_id),
                    INDEX idx_reminder_type (reminder_type),
                    INDEX idx_status (status),
                    INDEX idx_priority (priority),
                    INDEX idx_scheduled_time (scheduled_time),
                    INDEX idx_pending_reminders (status, scheduled_time),
                    INDEX idx_booking_reminder_type (booking_id, reminder_type),

                    UNIQUE KEY unique_booking_reminder (booking_id, reminder_type)
                )
            ");
            error_log("Created booking_reminders table");
        }

        if (!$logTableExists) {
            // Create reminder_logs table
            $database->query("
                CREATE TABLE reminder_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    reminder_id VARCHAR(36) NOT NULL,
                    booking_id VARCHAR(36) NOT NULL,
                    action ENUM('CREATED', 'SENT', 'FAILED', 'RETRY', 'SKIPPED') NOT NULL,
                    recipient_type ENUM('CUSTOMER', 'STAFF', 'BOTH') NOT NULL,
                    recipient_email VARCHAR(255),
                    details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                    INDEX idx_reminder_id (reminder_id),
                    INDEX idx_booking_id (booking_id),
                    INDEX idx_action (action),
                    INDEX idx_created_at (created_at)
                )
            ");
            error_log("Created reminder_logs table");
        }

        return true;
    } catch (Exception $e) {
        error_log("Failed to create reminder tables: " . $e->getMessage());
        return false;
    }
}

/**
 * Schedule reminders for a booking
 */
function scheduleBookingReminders($bookingId) {
    global $database;
    
    try {
        // Get booking details
        $booking = $database->fetch(
            "SELECT * FROM bookings WHERE id = ? AND status IN ('CONFIRMED', 'PENDING')",
            [$bookingId]
        );
        
        if (!$booking) {
            return false;
        }
        
        $appointmentDateTime = $booking['date'] . ' ' . $booking['start_time'];
        $appointmentTimestamp = strtotime($appointmentDateTime);
        
        // Define reminder schedules
        $reminders = [
            [
                'type' => '24_HOURS',
                'priority' => 'MEDIUM',
                'hours_before' => 24,
                'max_attempts' => 2
            ],
            [
                'type' => '30_MINUTES', 
                'priority' => 'HIGH',
                'hours_before' => 0.5, // 30 minutes
                'max_attempts' => 3
            ],
            [
                'type' => 'AT_TIME',
                'priority' => 'URGENT', 
                'hours_before' => 0,
                'max_attempts' => 2
            ]
        ];
        
        foreach ($reminders as $reminder) {
            $scheduledTime = date('Y-m-d H:i:s', $appointmentTimestamp - ($reminder['hours_before'] * 3600));
            
            // Skip if scheduled time is in the past
            if (strtotime($scheduledTime) < time()) {
                continue;
            }
            
            $reminderId = generateUUID();
            
            // Insert reminder (ignore if duplicate)
            $database->query(
                "INSERT IGNORE INTO booking_reminders 
                 (id, booking_id, reminder_type, priority, scheduled_time, max_attempts)
                 VALUES (?, ?, ?, ?, ?, ?)",
                [
                    $reminderId,
                    $bookingId,
                    $reminder['type'],
                    $reminder['priority'],
                    $scheduledTime,
                    $reminder['max_attempts']
                ]
            );
            
            // Log reminder creation
            logReminderActivity($reminderId, $bookingId, 'CREATED', 'BOTH', 
                "Reminder scheduled for {$reminder['type']} at {$scheduledTime}");
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Failed to schedule reminders for booking {$bookingId}: " . $e->getMessage());
        return false;
    }
}

/**
 * Process pending reminders with improved duplicate prevention
 * FIXED: Better query conditions and logging to prevent duplicates
 */
function processPendingReminders() {
    global $database;

    $results = [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'skipped' => 0
    ];

    try {
        // IMPROVED QUERY: More restrictive conditions to prevent duplicate processing
        $pendingReminders = $database->fetchAll(
            "SELECT br.*, b.date, b.start_time, b.status as booking_status
             FROM booking_reminders br
             JOIN bookings b ON br.booking_id = b.id
             WHERE br.status = 'PENDING'
             AND br.scheduled_time <= DATE_ADD(NOW(), INTERVAL 5 MINUTE)
             AND br.attempts < br.max_attempts
             AND b.status IN ('CONFIRMED', 'PENDING')
             AND CONCAT(b.date, ' ', b.start_time) > NOW()
             ORDER BY br.priority DESC, br.scheduled_time ASC
             LIMIT 50"
        );

        error_log("Found " . count($pendingReminders) . " pending reminders to process");

        foreach ($pendingReminders as $reminder) {
            $results['processed']++;

            // Double-check booking status (defensive programming)
            if (!in_array($reminder['booking_status'], ['CONFIRMED', 'PENDING'])) {
                markReminderSkipped($reminder['id'], 'Booking status changed to ' . $reminder['booking_status']);
                $results['skipped']++;
                error_log("Skipped reminder {$reminder['id']} - booking status: {$reminder['booking_status']}");
                continue;
            }

            // Check if appointment time has passed
            $appointmentTime = strtotime($reminder['date'] . ' ' . $reminder['start_time']);
            if ($appointmentTime <= time()) {
                markReminderSkipped($reminder['id'], 'Appointment time has passed');
                $results['skipped']++;
                error_log("Skipped reminder {$reminder['id']} - appointment time passed");
                continue;
            }

            error_log("Processing reminder {$reminder['id']} for booking {$reminder['booking_id']} (type: {$reminder['reminder_type']}, attempts: {$reminder['attempts']}/{$reminder['max_attempts']})");

            // Process the reminder
            $success = processReminder($reminder);

            if ($success) {
                $results['sent']++;
                error_log("Successfully processed reminder {$reminder['id']}");
            } else {
                $results['failed']++;
                error_log("Failed to process reminder {$reminder['id']}");
            }
        }

        error_log("Reminder processing completed: {$results['processed']} processed, {$results['sent']} sent, {$results['failed']} failed, {$results['skipped']} skipped");
        return $results;

    } catch (Exception $e) {
        error_log("Failed to process pending reminders: " . $e->getMessage());
        return $results;
    }
}

/**
 * Process a single reminder with atomic transaction to prevent duplicates
 * FIXED: Added atomic processing and duplicate prevention
 */
function processReminder($reminder) {
    global $database;

    try {
        // CRITICAL FIX: Start transaction for atomic processing
        $database->getConnection()->beginTransaction();

        // First, atomically claim this reminder to prevent concurrent processing
        $database->query(
            "UPDATE booking_reminders
             SET attempts = attempts + 1, updated_at = NOW()
             WHERE id = ? AND status = 'PENDING' AND attempts < max_attempts",
            [$reminder['id']]
        );

        // Check if we successfully claimed the reminder
        $stmt = $database->getConnection()->prepare("SELECT attempts FROM booking_reminders WHERE id = ?");
        $stmt->execute([$reminder['id']]);
        $updatedReminder = $stmt->fetch(PDO::FETCH_ASSOC);
        $affectedRows = $updatedReminder ? 1 : 0;
        if ($affectedRows === 0) {
            // Reminder was already processed by another instance
            $database->getConnection()->rollback();
            error_log("Reminder {$reminder['id']} already processed by another instance - skipping");
            return false;
        }

        error_log("Processing reminder {$reminder['id']} for booking {$reminder['booking_id']} (type: {$reminder['reminder_type']})");

        // Get booking details with customer and staff info
        $booking = $database->fetch(
            "SELECT b.*, u.name as customer_name, u.email as customer_email,
                    s.name as service_name, p.name as package_name,
                    st.name as staff_name, st.email as staff_email
             FROM bookings b
             LEFT JOIN users u ON b.user_id = u.id
             LEFT JOIN services s ON b.service_id = s.id
             LEFT JOIN packages p ON b.package_id = p.id
             LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
             WHERE b.id = ?",
            [$reminder['booking_id']]
        );

        if (!$booking) {
            $database->getConnection()->rollback();
            markReminderFailed($reminder['id'], 'Booking not found');
            return false;
        }

        $customerEmailSent = false;
        $staffEmailSent = false;
        $errors = [];

        // Send customer reminder
        if ($booking['customer_email']) {
            error_log("Sending customer reminder email to: {$booking['customer_email']}");
            $customerEmailSent = sendBookingReminderEmail($reminder['booking_id'], $reminder['reminder_type']);
            if ($customerEmailSent) {
                error_log("Customer reminder email sent successfully");
            } else {
                $errors[] = 'Failed to send customer email';
                error_log("Failed to send customer reminder email");
            }
        } else {
            $errors[] = 'No customer email available';
        }

        // Send staff reminder
        if ($booking['staff_email']) {
            error_log("Sending staff reminder email to: {$booking['staff_email']}");
            $staffEmailSent = sendStaffBookingNotificationEmail($reminder['booking_id'], 'BOOKING_REMINDER');
            if ($staffEmailSent) {
                error_log("Staff reminder email sent successfully");
            } else {
                $errors[] = 'Failed to send staff email';
                error_log("Failed to send staff reminder email");
            }
        } else {
            $errors[] = 'No staff email available';
        }

        // Update reminder status
        $overallSuccess = $customerEmailSent || $staffEmailSent;

        if ($overallSuccess) {
            // Mark as sent (this will increment attempts again, but that's OK for tracking)
            $database->query(
                "UPDATE booking_reminders
                 SET status = 'SENT',
                     sent_time = NOW(),
                     customer_email_sent = ?,
                     staff_email_sent = ?,
                     customer_email_status = ?,
                     staff_email_status = ?,
                     updated_at = NOW()
                 WHERE id = ?",
                [
                    $customerEmailSent ? 1 : 0,
                    $staffEmailSent ? 1 : 0,
                    $customerEmailSent ? 'SENT' : 'FAILED',
                    $staffEmailSent ? 'SENT' : 'FAILED',
                    $reminder['id']
                ]
            );

            error_log("Reminder {$reminder['id']} marked as SENT successfully");

            // For HIGH priority reminders, schedule additional attempts
            if ($reminder['priority'] === 'HIGH' && $reminder['reminder_type'] === '30_MINUTES') {
                scheduleAdditionalHighPriorityReminder($reminder);
            }
        } else {
            // Mark as failed
            $database->query(
                "UPDATE booking_reminders
                 SET status = 'FAILED',
                     error_message = ?,
                     updated_at = NOW()
                 WHERE id = ?",
                [implode('; ', $errors), $reminder['id']]
            );

            error_log("Reminder {$reminder['id']} marked as FAILED: " . implode('; ', $errors));
        }

        // Commit the transaction
        $database->getConnection()->commit();

        // Log the activity
        $recipientType = ($customerEmailSent && $staffEmailSent) ? 'BOTH' :
                        ($customerEmailSent ? 'CUSTOMER' : 'STAFF');

        logReminderActivity(
            $reminder['id'],
            $reminder['booking_id'],
            $overallSuccess ? 'SENT' : 'FAILED',
            $recipientType,
            $overallSuccess ? 'Reminder sent successfully' : implode('; ', $errors)
        );

        return $overallSuccess;

    } catch (Exception $e) {
        // Rollback transaction on error
        if ($database->getConnection()->inTransaction()) {
            $database->getConnection()->rollback();
        }

        error_log("Failed to process reminder {$reminder['id']}: " . $e->getMessage());
        markReminderFailed($reminder['id'], $e->getMessage());
        return false;
    }
}

/**
 * Mark reminder as sent (Legacy function - kept for compatibility)
 * NOTE: This function is now integrated into processReminder() for atomic processing
 */
function markReminderSent($reminderId, $customerEmailSent = false, $staffEmailSent = false) {
    global $database;

    try {
        // CRITICAL FIX: Increment attempts counter to prevent duplicate processing
        $database->query(
            "UPDATE booking_reminders
             SET status = 'SENT',
                 sent_time = NOW(),
                 attempts = attempts + 1,
                 customer_email_sent = ?,
                 staff_email_sent = ?,
                 customer_email_status = ?,
                 staff_email_status = ?,
                 updated_at = NOW()
             WHERE id = ? AND status = 'PENDING'",
            [
                $customerEmailSent ? 1 : 0,
                $staffEmailSent ? 1 : 0,
                $customerEmailSent ? 'SENT' : 'FAILED',
                $staffEmailSent ? 'SENT' : 'FAILED',
                $reminderId
            ]
        );

        // Log the successful update for debugging
        $stmt = $database->getConnection()->prepare("SELECT status FROM booking_reminders WHERE id = ?");
        $stmt->execute([$reminderId]);
        $currentStatus = $stmt->fetchColumn();

        if ($currentStatus === 'SENT') {
            error_log("Successfully marked reminder {$reminderId} as SENT (attempts incremented)");
        } else {
            error_log("Warning: Reminder {$reminderId} status is {$currentStatus} - may have been already processed");
        }

    } catch (Exception $e) {
        error_log("Failed to mark reminder as sent: " . $e->getMessage());
    }
}

/**
 * Mark reminder as failed
 */
function markReminderFailed($reminderId, $errorMessage = '') {
    global $database;

    try {
        $database->query(
            "UPDATE booking_reminders
             SET status = 'FAILED',
                 attempts = attempts + 1,
                 error_message = ?,
                 updated_at = NOW()
             WHERE id = ?",
            [$errorMessage, $reminderId]
        );
    } catch (Exception $e) {
        error_log("Failed to mark reminder as failed: " . $e->getMessage());
    }
}

/**
 * Mark reminder as skipped
 */
function markReminderSkipped($reminderId, $reason = '') {
    global $database;

    try {
        $database->query(
            "UPDATE booking_reminders
             SET status = 'SKIPPED',
                 error_message = ?,
                 updated_at = NOW()
             WHERE id = ?",
            [$reason, $reminderId]
        );
    } catch (Exception $e) {
        error_log("Failed to mark reminder as skipped: " . $e->getMessage());
    }
}

/**
 * Schedule additional high priority reminder
 */
function scheduleAdditionalHighPriorityReminder($originalReminder) {
    global $database;

    try {
        // Schedule another reminder 15 minutes later for HIGH priority
        $additionalTime = date('Y-m-d H:i:s', strtotime($originalReminder['scheduled_time']) + 15 * 60);

        // Only schedule if it's still before the appointment time
        $booking = $database->fetch(
            "SELECT date, start_time FROM bookings WHERE id = ?",
            [$originalReminder['booking_id']]
        );

        if ($booking) {
            $appointmentTime = strtotime($booking['date'] . ' ' . $booking['start_time']);
            if (strtotime($additionalTime) < $appointmentTime) {
                $additionalReminderId = generateUUID();

                $database->query(
                    "INSERT INTO booking_reminders
                     (id, booking_id, reminder_type, priority, scheduled_time, max_attempts)
                     VALUES (?, ?, ?, ?, ?, ?)",
                    [
                        $additionalReminderId,
                        $originalReminder['booking_id'],
                        '30_MINUTES_FOLLOWUP',
                        'HIGH',
                        $additionalTime,
                        2
                    ]
                );

                logReminderActivity(
                    $additionalReminderId,
                    $originalReminder['booking_id'],
                    'CREATED',
                    'BOTH',
                    'Additional high priority reminder scheduled'
                );
            }
        }
    } catch (Exception $e) {
        error_log("Failed to schedule additional reminder: " . $e->getMessage());
    }
}

/**
 * Log reminder activity
 */
function logReminderActivity($reminderId, $bookingId, $action, $recipientType, $details = '', $recipientEmail = '') {
    global $database;

    try {
        $database->query(
            "INSERT INTO reminder_logs
             (reminder_id, booking_id, action, recipient_type, recipient_email, details)
             VALUES (?, ?, ?, ?, ?, ?)",
            [$reminderId, $bookingId, $action, $recipientType, $recipientEmail, $details]
        );
    } catch (Exception $e) {
        error_log("Failed to log reminder activity: " . $e->getMessage());
    }
}

/**
 * Check for missed reminders and send them
 */
function checkMissedReminders() {
    global $database;

    $results = [
        'checked' => 0,
        'missed_found' => 0,
        'sent' => 0,
        'failed' => 0
    ];

    try {
        // Find bookings that should have had reminders but don't
        $missedBookings = $database->fetchAll(
            "SELECT b.id, b.date, b.start_time, b.status
             FROM bookings b
             WHERE b.status IN ('CONFIRMED', 'PENDING')
             AND b.date >= CURDATE()
             AND b.id NOT IN (
                 SELECT DISTINCT booking_id
                 FROM booking_reminders
                 WHERE status IN ('SENT', 'PENDING')
             )"
        );

        $results['checked'] = count($missedBookings);

        foreach ($missedBookings as $booking) {
            $results['missed_found']++;

            // Schedule reminders for this booking
            if (scheduleBookingReminders($booking['id'])) {
                $results['sent']++;

                // Log the missed reminder recovery
                error_log("Recovered missed reminders for booking: {$booking['id']}");
            } else {
                $results['failed']++;
            }
        }

        // Also check for failed reminders that can be retried
        $failedReminders = $database->fetchAll(
            "SELECT br.*, b.date, b.start_time
             FROM booking_reminders br
             JOIN bookings b ON br.booking_id = b.id
             WHERE br.status = 'FAILED'
             AND br.attempts < br.max_attempts
             AND b.status IN ('CONFIRMED', 'PENDING')
             AND CONCAT(b.date, ' ', b.start_time) > NOW()"
        );

        foreach ($failedReminders as $reminder) {
            // Reset to pending for retry
            $database->query(
                "UPDATE booking_reminders
                 SET status = 'PENDING', updated_at = NOW()
                 WHERE id = ?",
                [$reminder['id']]
            );

            logReminderActivity(
                $reminder['id'],
                $reminder['booking_id'],
                'RETRY',
                'BOTH',
                'Retry scheduled for failed reminder'
            );
        }

        return $results;

    } catch (Exception $e) {
        error_log("Failed to check missed reminders: " . $e->getMessage());
        return $results;
    }
}

/**
 * Get reminder statistics
 */
function getReminderStats($days = 30) {
    global $database;

    try {
        $stats = $database->fetch(
            "SELECT
                COUNT(*) as total_reminders,
                SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) as sent_reminders,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_reminders,
                SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_reminders,
                SUM(CASE WHEN status = 'SKIPPED' THEN 1 ELSE 0 END) as skipped_reminders,
                SUM(CASE WHEN priority = 'HIGH' AND status = 'SENT' THEN 1 ELSE 0 END) as high_priority_sent,
                SUM(CASE WHEN customer_email_sent = 1 THEN 1 ELSE 0 END) as customer_emails_sent,
                SUM(CASE WHEN staff_email_sent = 1 THEN 1 ELSE 0 END) as staff_emails_sent
             FROM booking_reminders
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
            [$days]
        );

        return $stats ?: [
            'total_reminders' => 0,
            'sent_reminders' => 0,
            'failed_reminders' => 0,
            'pending_reminders' => 0,
            'skipped_reminders' => 0,
            'high_priority_sent' => 0,
            'customer_emails_sent' => 0,
            'staff_emails_sent' => 0
        ];
    } catch (Exception $e) {
        error_log("Failed to get reminder stats: " . $e->getMessage());
        return [
            'total_reminders' => 0,
            'sent_reminders' => 0,
            'failed_reminders' => 0,
            'pending_reminders' => 0,
            'skipped_reminders' => 0,
            'high_priority_sent' => 0,
            'customer_emails_sent' => 0,
            'staff_emails_sent' => 0
        ];
    }
}

/**
 * Cancel reminders for a booking
 */
function cancelBookingReminders($bookingId, $reason = 'Booking cancelled') {
    global $database;

    try {
        $database->query(
            "UPDATE booking_reminders
             SET status = 'SKIPPED',
                 error_message = ?,
                 updated_at = NOW()
             WHERE booking_id = ?
             AND status = 'PENDING'",
            [$reason, $bookingId]
        );

        // Log the cancellation
        $reminders = $database->fetchAll(
            "SELECT id FROM booking_reminders WHERE booking_id = ?",
            [$bookingId]
        );

        foreach ($reminders as $reminder) {
            logReminderActivity(
                $reminder['id'],
                $bookingId,
                'SKIPPED',
                'BOTH',
                $reason
            );
        }

        return true;
    } catch (Exception $e) {
        error_log("Failed to cancel reminders for booking {$bookingId}: " . $e->getMessage());
        return false;
    }
}

/**
 * Get upcoming reminders
 */
function getUpcomingReminders($hours = 24) {
    global $database;

    try {
        return $database->fetchAll(
            "SELECT br.*, b.date, b.start_time,
                    u.name as customer_name, u.email as customer_email,
                    s.name as service_name, p.name as package_name,
                    st.name as staff_name, st.email as staff_email
             FROM booking_reminders br
             JOIN bookings b ON br.booking_id = b.id
             LEFT JOIN users u ON b.user_id = u.id
             LEFT JOIN services s ON b.service_id = s.id
             LEFT JOIN packages p ON b.package_id = p.id
             LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
             WHERE br.status = 'PENDING'
             AND br.scheduled_time <= DATE_ADD(NOW(), INTERVAL ? HOUR)
             ORDER BY br.priority DESC, br.scheduled_time ASC",
            [$hours]
        );
    } catch (Exception $e) {
        error_log("Failed to get upcoming reminders: " . $e->getMessage());
        return [];
    }
}

/**
 * Initialize reminder system (create tables and set up)
 */
function initializeReminderSystem() {
    // Create tables
    $tablesCreated = createReminderTables();

    if ($tablesCreated) {
        // Schedule reminders for existing confirmed bookings that don't have them
        $missedResults = checkMissedReminders();
        error_log("Reminder system initialized. Missed reminders recovered: " . $missedResults['sent']);
    }

    return $tablesCreated;
}

// Note: Call initializeReminderSystem() manually when needed
// Don't auto-initialize to avoid issues during file inclusion
