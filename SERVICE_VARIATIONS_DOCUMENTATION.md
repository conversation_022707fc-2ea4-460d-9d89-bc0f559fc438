# Service Variations System Documentation

## Overview

The Service Variations System allows services in the Flix-PHP booking platform to have multiple pricing and duration options. For example, a massage service can offer 60-minute, 90-minute, and 120-minute variations with different prices.

## Features

### 1. Database Schema
- **service_variations table**: Stores variation data with proper relationships
- **Updated bookings table**: Includes `service_variation_id` column for tracking
- **Backward compatibility**: Existing services without variations continue to work

### 2. Admin Panel Management
- **Variations Management Modal**: Accessible from admin/services/ page
- **CRUD Operations**: Create, read, update, delete variations
- **Status Management**: Activate/deactivate variations
- **Sort Order**: Organize variation display order

### 3. Customer Experience
- **Automatic Detection**: System checks if service has variations
- **Modal Selection**: Clean modal interface for choosing variations
- **Seamless Integration**: Works with existing booking flow
- **Responsive Design**: Maintains black theme and glass effects

### 4. Booking Integration
- **Variation Tracking**: Bookings store selected variation ID
- **Price Calculation**: Uses variation price instead of base service price
- **Duration Handling**: Uses variation duration for scheduling

## File Structure

```
├── database/
│   └── migrations.sql                     # Updated with service_variations table
├── includes/
│   ├── service_variation_functions.php    # Core variation functions
│   ├── service_functions.php             # Updated to include variations
│   └── customer_panel_functions.php      # Updated booking functions
├── api/
│   ├── admin/
│   │   └── service_variations.php        # Admin API endpoints
│   └── customer/
│       └── service_variations.php        # Customer API endpoints
├── admin/
│   └── services/
│       └── index.php                     # Updated with variations management
├── customer/
│   └── book/
│       └── index.php                     # Updated booking form
├── services.php                          # Updated public services page
├── test_service_variations.php           # System testing script
├── sample_service_variations.php         # Sample data script
└── SERVICE_VARIATIONS_DOCUMENTATION.md   # This documentation
```

## Database Schema

### service_variations Table
```sql
CREATE TABLE IF NOT EXISTS service_variations (
    id VARCHAR(36) PRIMARY KEY,
    service_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL COMMENT 'Duration in minutes',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);
```

### Updated bookings Table
```sql
ALTER TABLE bookings ADD COLUMN service_variation_id VARCHAR(36) NULL;
ALTER TABLE bookings ADD FOREIGN KEY (service_variation_id) REFERENCES service_variations(id) ON DELETE SET NULL;
```

## API Endpoints

### Admin API (`/api/admin/service_variations.php`)
- **GET**: Retrieve variations for a service or all services
- **POST**: Create new variation
- **PUT**: Update existing variation
- **PATCH**: Toggle variation status
- **DELETE**: Delete variation (with booking validation)

### Customer API (`/api/customer/service_variations.php`)
- **GET**: Retrieve active variations for public display

## Core Functions

### Service Variation Management
```php
// Create variation
createServiceVariation($serviceId, $data)

// Update variation
updateServiceVariation($variationId, $data)

// Delete variation
deleteServiceVariation($variationId)

// Get variations for service
getServiceVariations($serviceId, $activeOnly = false)

// Get all services with variations
getServicesWithVariations($activeOnly = false)

// Toggle variation status
toggleServiceVariationStatus($variationId)
```

## Usage Examples

### Admin: Adding Variations
1. Go to Admin → Services
2. Click "Variations" button on any service
3. Fill in variation details:
   - Name (e.g., "60 Minutes")
   - Price in TSH
   - Duration in minutes
   - Description (optional)
4. Click "Add Variation"

### Customer: Booking with Variations
1. Visit Services page
2. Click "Book Now" on service with variations
3. Select desired variation from modal
4. Complete booking as normal

### Developer: Creating Variations Programmatically
```php
$variationData = [
    'name' => '90 Minutes',
    'description' => 'Extended massage session',
    'price' => 75000,
    'duration' => 90,
    'is_active' => true
];

$result = createServiceVariation($serviceId, $variationData);
if ($result['success']) {
    echo "Variation created with ID: " . $result['id'];
}
```

## Testing

### Automated Tests
Run the test script to validate system functionality:
```
http://your-domain/flix-php/test_service_variations.php
```

### Sample Data
Add sample variations for testing:
```
http://your-domain/flix-php/sample_service_variations.php
```

### Manual Testing Checklist
- [ ] Admin can create/edit/delete variations
- [ ] Customer sees variation selection modal
- [ ] Booking captures correct variation and price
- [ ] Services without variations work normally
- [ ] Variation status toggle works
- [ ] Sort order functions properly

## Design Standards

### Visual Design
- **Black Color Palette**: Maintains existing theme
- **Glass Header Effects**: Consistent with site design
- **TSH Currency**: Uses project currency standards
- **Responsive Design**: Works on all device sizes

### Code Standards
- **Backward Compatibility**: Existing functionality preserved
- **Error Handling**: Comprehensive error checking
- **Security**: Input validation and sanitization
- **Performance**: Efficient database queries

## Troubleshooting

### Common Issues

1. **Variations not showing in modal**
   - Check if service has active variations
   - Verify API endpoints are accessible
   - Check browser console for JavaScript errors

2. **Booking fails with variations**
   - Ensure service_variation_id column exists in bookings table
   - Verify variation belongs to selected service
   - Check variation is active

3. **Admin panel not showing variations button**
   - Clear browser cache
   - Check if admin services page was updated
   - Verify user has admin permissions

### Debug Mode
Enable debug mode in config to see detailed error messages:
```php
define('DEBUG_MODE', true);
```

## Future Enhancements

### Potential Improvements
- **Variation Images**: Add images for each variation
- **Bulk Operations**: Mass update variations
- **Variation Categories**: Group variations by type
- **Price Rules**: Dynamic pricing based on time/demand
- **Variation Analytics**: Track popular variations

### Integration Opportunities
- **Package Variations**: Extend to service packages
- **Staff Specializations**: Link variations to staff skills
- **Seasonal Pricing**: Time-based variation pricing
- **Loyalty Discounts**: Member-specific variation prices

## Support

For technical support or questions about the Service Variations System:
1. Check this documentation first
2. Run the test script to identify issues
3. Review error logs in the application
4. Contact the development team with specific error messages

---

**Version**: 1.0  
**Last Updated**: 2025-06-18  
**Compatible With**: Flix-PHP V3 Black Theme
