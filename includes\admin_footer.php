    <!-- Admin Footer -->
    <footer class="bg-secondary-900/80 backdrop-blur-sm border-t border-secondary-700 mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6 flex flex-col sm:flex-row justify-between items-center">
                <div class="text-sm text-gray-400">
                    &copy; <?= date('Y') ?> <?= APP_NAME ?> Admin Panel. All rights reserved.
                </div>
                <div class="flex items-center space-x-6 mt-2 sm:mt-0">
                    <span class="text-xs text-gray-500">Version <?= APP_VERSION ?></span>
                    <div class="flex items-center text-xs text-gray-500">
                        <div class="h-2 w-2 bg-salon-gold rounded-full mr-2"></div>
                        System Online
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Toast Notifications Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Common Admin Scripts -->
    <script>
        // Toast notification system
        function showToast(message, type = 'success') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = type === 'success' ? 'bg-green-500' : 
                           type === 'error' ? 'bg-red-500' : 
                           type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            `;
            
            container.appendChild(toast);
            
            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // Confirm dialog
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // Format currency for TSH (no decimal places)
        function formatCurrency(amount) {
            return 'TSH ' + parseInt(amount).toLocaleString();
        }

        // Format date
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // AJAX helper
        function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            return fetch(url, { ...defaultOptions, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('Request failed:', error);

                    // Only show toast if function exists
                    if (typeof showToast === 'function') {
                        showToast('Request failed. Please try again.', 'error');
                    }

                    throw error;
                });
        }

        // Auto-refresh data every 30 seconds for dashboard
        if (window.location.pathname === '/admin' || window.location.pathname === '/admin/') {
            setInterval(() => {
                // Refresh dashboard stats
                const basePath = '<?= getBasePath() ?>';
                makeRequest(`${basePath}/api/admin/dashboard-stats.php`)
                    .then(data => {
                        // Update stats if elements exist
                        const elements = {
                            'total-customers': data.customers,
                            'total-bookings': data.bookings,
                            'total-revenue': formatCurrency(data.revenue),
                            'pending-bookings': data.pending_bookings
                        };

                        Object.entries(elements).forEach(([id, value]) => {
                            const element = document.getElementById(id);
                            if (element) element.textContent = value;
                        });
                    })
                    .catch(() => {
                        // Silently fail for auto-refresh
                    });
            }, 30000);
        }

        // Notification System
        let notificationDropdownOpen = false;
        let currentNotificationFilter = 'all';
        let notifications = [];
        let lastNotificationCount = 0;
        let lastNotificationCheck = Date.now();

        // Initialize notifications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();

            // Auto-refresh notifications every 60 seconds with enhanced features
            setInterval(loadNotificationsWithToast, 60000);

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('#notificationButton') && !e.target.closest('#notificationDropdown')) {
                    closeNotifications();
                }
            });
        });

        function toggleNotifications() {
            const dropdown = document.getElementById('notificationDropdown');

            if (notificationDropdownOpen) {
                closeNotifications();
            } else {
                dropdown.classList.remove('hidden');
                notificationDropdownOpen = true;
                loadNotifications();
            }
        }

        function closeNotifications() {
            const dropdown = document.getElementById('notificationDropdown');
            dropdown.classList.add('hidden');
            notificationDropdownOpen = false;
        }

        function loadNotifications() {
            const basePath = '<?= getBasePath() ?>';
            const url = currentNotificationFilter === 'all'
                ? `${basePath}/api/admin/notifications.php?limit=10&unread_only=true`
                : `${basePath}/api/admin/notifications.php?limit=10&category=${currentNotificationFilter}&unread_only=true`;

            console.log('🔄 Loading notifications from:', url);
            makeRequest(url)
                .then(data => {
                    if (data && data.data) {
                        notifications = data.data.notifications || [];
                        const currentCount = data.data.counts ? data.data.counts.unread : 0;

                        // Initialize lastNotificationCount on first load
                        if (lastNotificationCount === 0) {
                            lastNotificationCount = currentCount;
                        }

                        updateNotificationCounter(currentCount);
                        updateCategoryCounts(data.data.counts ? data.data.counts.categories : {});
                        renderNotifications();
                    } else {
                        console.warn('⚠️ Invalid response format:', data);
                        handleNotificationError('Invalid response format');
                    }
                })
                .catch(error => {
                    console.error('❌ Failed to load notifications:', error);
                    handleNotificationError(error.message);
                });
        }

        function loadNotificationsWithToast() {
            const basePath = '<?= getBasePath() ?>';
            const url = currentNotificationFilter === 'all'
                ? `${basePath}/api/admin/notifications.php?limit=10&unread_only=true`
                : `${basePath}/api/admin/notifications.php?limit=10&category=${currentNotificationFilter}&unread_only=true`;

            makeRequest(url)
                .then(data => {
                    if (data && data.data) {
                        notifications = data.data.notifications || [];
                        const currentCount = data.data.counts ? data.data.counts.unread : 0;

                        // Check for new notifications
                        if (currentCount > lastNotificationCount) {
                            const newNotifications = currentCount - lastNotificationCount;
                            showHeaderToast(`You have ${newNotifications} new notification${newNotifications > 1 ? 's' : ''}`, 'info');

                            // Add visual indicator to notification button
                            const notificationButton = document.getElementById('notificationButton');
                            if (notificationButton) {
                                notificationButton.classList.add('animate-pulse');
                                setTimeout(() => {
                                    notificationButton.classList.remove('animate-pulse');
                                }, 3000);
                            }
                        }

                        lastNotificationCount = currentCount;
                        updateNotificationCounter(currentCount);
                        updateCategoryCounts(data.data.counts ? data.data.counts.categories : {});
                        renderNotifications();
                    } else {
                        console.warn('⚠️ Invalid response format:', data);
                        handleNotificationError('Invalid response format');
                    }
                })
                .catch(error => {
                    console.error('❌ Failed to load notifications:', error);
                    handleNotificationError(error.message);
                });
        }

        function handleNotificationError(errorMessage) {
            // Set empty state
            notifications = [];
            updateNotificationCounter(0);
            updateCategoryCounts({});

            // Show error in dropdown
            const container = document.getElementById('notificationsList');
            if (container) {
                if (errorMessage.includes('notifications') && errorMessage.includes('exist')) {
                    // Table doesn't exist - show migration message
                    container.innerHTML = `
                        <div class="p-4 text-center text-yellow-400">
                            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="font-medium">Notification System Not Set Up</p>
                            <p class="text-sm mt-1">Run the migration to enable notifications</p>
                            <a href="${'<?= getBasePath() ?>'}/admin/notifications/migrate.php"
                               class="inline-block mt-2 px-3 py-1 bg-yellow-600 text-black rounded text-xs hover:bg-yellow-500">
                                Run Migration
                            </a>
                        </div>
                    `;
                } else {
                    // Other error
                    container.innerHTML = `
                        <div class="p-4 text-center text-red-400">
                            <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="font-medium">Error Loading Notifications</p>
                            <p class="text-sm mt-1">${errorMessage}</p>
                            <button onclick="loadNotifications()"
                                    class="inline-block mt-2 px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-500">
                                Retry
                            </button>
                        </div>
                    `;
                }
            }
        }

        function updateNotificationCounter(count) {
            const counter = document.getElementById('notificationCounter');
            const button = document.getElementById('notificationButton');

            if (count > 0) {
                counter.textContent = count > 99 ? '99+' : count;
                counter.classList.remove('hidden');
                button.classList.add('text-salon-gold');
            } else {
                counter.classList.add('hidden');
                button.classList.remove('text-salon-gold');
            }
        }

        function updateCategoryCounts(categoryCounts) {
            const totalUnread = Object.values(categoryCounts).reduce((sum, count) => sum + count, 0);

            document.getElementById('count-all').textContent = totalUnread;
            document.getElementById('count-BOOKING').textContent = categoryCounts.BOOKING || 0;
            document.getElementById('count-CUSTOMER').textContent = categoryCounts.CUSTOMER || 0;
            document.getElementById('count-SYSTEM').textContent = categoryCounts.SYSTEM || 0;
        }

        function renderNotifications() {
            const container = document.getElementById('notificationsList');

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="p-4 text-center text-gray-400">
                        <svg class="w-8 h-8 mx-auto mb-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                        </svg>
                        <p>No new notifications</p>
                    </div>
                `;
                return;
            }

            const html = notifications.map(notification => {
                const categoryInfo = getNotificationCategoryInfo(notification.category);
                const priorityClass = getPriorityClass(notification.priority);

                return `
                    <div class="notification-item p-3 border-b border-secondary-700 hover:bg-secondary-700 cursor-pointer transition-colors ${notification.is_read ? 'opacity-60' : ''}"
                         onclick="handleNotificationClick('${notification.id}', '${notification.action_url || ''}')">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 rounded-full ${categoryInfo.bgColor} flex items-center justify-center">
                                    ${categoryInfo.icon}
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <p class="text-sm font-medium text-white truncate">${notification.title}</p>
                                    <div class="flex items-center space-x-1">
                                        ${notification.priority !== 'MEDIUM' ? `<span class="w-2 h-2 rounded-full ${priorityClass}"></span>` : ''}
                                        <span class="text-xs text-gray-400">${notification.time_ago}</span>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-300 mt-1 line-clamp-2">${notification.message}</p>
                                <div class="flex items-center justify-between mt-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${categoryInfo.badgeClass}">
                                        ${notification.category}
                                    </span>
                                    ${!notification.is_read ? '<div class="w-2 h-2 bg-salon-gold rounded-full"></div>' : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        function getNotificationCategoryInfo(category) {
            const categoryMap = {
                'BOOKING': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
                    bgColor: 'bg-blue-600',
                    badgeClass: 'bg-blue-100 text-blue-800'
                },
                'CUSTOMER': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>',
                    bgColor: 'bg-green-600',
                    badgeClass: 'bg-green-100 text-green-800'
                },
                'STAFF': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>',
                    bgColor: 'bg-purple-600',
                    badgeClass: 'bg-purple-100 text-purple-800'
                },
                'PAYMENT': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path></svg>',
                    bgColor: 'bg-yellow-600',
                    badgeClass: 'bg-yellow-100 text-yellow-800'
                },
                'SYSTEM': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
                    bgColor: 'bg-gray-600',
                    badgeClass: 'bg-gray-100 text-gray-800'
                },
                'MARKETING': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path></svg>',
                    bgColor: 'bg-pink-600',
                    badgeClass: 'bg-pink-100 text-pink-800'
                },
                'FEEDBACK': {
                    icon: '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path></svg>',
                    bgColor: 'bg-indigo-600',
                    badgeClass: 'bg-indigo-100 text-indigo-800'
                }
            };

            return categoryMap[category] || categoryMap['SYSTEM'];
        }

        function getPriorityClass(priority) {
            const priorityMap = {
                'URGENT': 'bg-red-500',
                'HIGH': 'bg-orange-500',
                'MEDIUM': 'bg-yellow-500',
                'LOW': 'bg-green-500'
            };

            return priorityMap[priority] || priorityMap['MEDIUM'];
        }

        function filterNotifications(category) {
            // Update active tab
            document.querySelectorAll('.notification-tab').forEach(tab => {
                tab.classList.remove('active', 'bg-salon-gold', 'text-black', 'font-medium');
                tab.classList.add('text-gray-400', 'hover:text-white');
            });

            const activeTab = event.target;
            activeTab.classList.add('active', 'bg-salon-gold', 'text-black', 'font-medium');
            activeTab.classList.remove('text-gray-400', 'hover:text-white');

            currentNotificationFilter = category;
            loadNotifications();
        }

        function handleNotificationClick(notificationId, actionUrl) {
            // Mark as read
            markNotificationAsRead(notificationId);

            // Navigate to action URL if provided
            if (actionUrl) {
                const basePath = '<?= getBasePath() ?>';

                // If actionUrl starts with /admin/, ensure it's properly resolved from the base path
                if (actionUrl.startsWith('/admin/')) {
                    window.location.href = basePath + actionUrl;
                } else if (actionUrl.startsWith('admin/')) {
                    // Handle case where actionUrl doesn't start with /
                    window.location.href = basePath + '/' + actionUrl;
                } else if (actionUrl.startsWith('http://') || actionUrl.startsWith('https://') || actionUrl.startsWith('/')) {
                    // Handle absolute URLs or root-relative URLs
                    window.location.href = actionUrl;
                } else {
                    // Handle relative URLs by prepending base path
                    window.location.href = basePath + '/' + actionUrl;
                }
            }
        }

        function markNotificationAsRead(notificationId) {
            const basePath = '<?= getBasePath() ?>';
            makeRequest(`${basePath}/api/admin/notifications.php?id=${notificationId}`, {
                method: 'PUT',
                body: JSON.stringify({ is_read: true })
            }).then(() => {
                loadNotifications();
            }).catch(error => {
                console.error('Failed to mark notification as read:', error);
            });
        }

        function markAllAsRead() {
            const basePath = '<?= getBasePath() ?>';
            const category = currentNotificationFilter === 'all' ? null : currentNotificationFilter;

            makeRequest(`${basePath}/api/admin/notifications-bulk.php`, {
                method: 'POST',
                body: JSON.stringify({
                    action: 'mark_read',
                    category: category
                })
            }).then(() => {
                loadNotifications();
                showToast('All notifications marked as read', 'success');
            }).catch(error => {
                console.error('Failed to mark all as read:', error);
                showToast('Failed to mark notifications as read', 'error');
            });
        }

        function openNotificationsPage() {
            const basePath = '<?= getBasePath() ?>';
            window.location.href = `${basePath}/admin/notifications/`;
        }

        // Enhanced toast notification function for header notifications
        function showHeaderToast(message, type = 'info', duration = 4000) {
            // Create container if it doesn't exist
            let container = document.getElementById('headerToastContainer');
            if (!container) {
                container = document.createElement('div');
                container.id = 'headerToastContainer';
                container.className = 'fixed top-4 right-4 z-[70] space-y-2';
                document.body.appendChild(container);
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = `flex items-center p-4 rounded-lg shadow-lg border transform transition-all duration-300 ease-in-out translate-x-full opacity-0 max-w-sm`;

            // Set colors and icon based on type
            let bgClass, borderClass, textClass, iconHTML;

            switch(type) {
                case 'success':
                    bgClass = 'bg-green-800';
                    borderClass = 'border-green-600';
                    textClass = 'text-green-100';
                    iconHTML = `<svg class="w-5 h-5 text-green-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>`;
                    break;
                case 'error':
                    bgClass = 'bg-red-800';
                    borderClass = 'border-red-600';
                    textClass = 'text-red-100';
                    iconHTML = `<svg class="w-5 h-5 text-red-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>`;
                    break;
                case 'warning':
                    bgClass = 'bg-yellow-800';
                    borderClass = 'border-yellow-600';
                    textClass = 'text-yellow-100';
                    iconHTML = `<svg class="w-5 h-5 text-yellow-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>`;
                    break;
                case 'info':
                default:
                    bgClass = 'bg-blue-800';
                    borderClass = 'border-blue-600';
                    textClass = 'text-blue-100';
                    iconHTML = `<svg class="w-5 h-5 text-blue-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v2.25H2.25v-2.25L4.5 12V9.75a6 6 0 0 1 6-6z" />
                    </svg>`;
                    break;
            }

            toast.classList.add(bgClass, borderClass, textClass);

            toast.innerHTML = `
                ${iconHTML}
                <div class="flex-1">
                    <p class="font-medium text-sm">${message}</p>
                </div>
                <button onclick="this.parentElement.remove()" class="ml-3 text-gray-400 hover:text-white transition-colors">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;

            container.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
            }, 100);

            // Auto remove after duration
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.classList.add('translate-x-full', 'opacity-0');
                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, duration);
        }

        // Handle form submissions with loading states
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form[data-ajax]');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.textContent;
                    
                    // Show loading state
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    `;
                    
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData.entries());
                    
                    makeRequest(form.action, {
                        method: form.method || 'POST',
                        body: JSON.stringify(data)
                    })
                    .then(response => {
                        if (response.success) {
                            showToast(response.message || 'Operation completed successfully');
                            if (response.redirect) {
                                window.location.href = response.redirect;
                            } else if (form.dataset.reload) {
                                window.location.reload();
                            }
                        } else {
                            showToast(response.error || 'Operation failed', 'error');
                        }
                    })
                    .catch(() => {
                        showToast('An error occurred. Please try again.', 'error');
                    })
                    .finally(() => {
                        // Restore button state
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    });
                });
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[type="search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal:not(.hidden)');
                modals.forEach(modal => modal.classList.add('hidden'));
            }
        });
    </script>
</body>
</html>
