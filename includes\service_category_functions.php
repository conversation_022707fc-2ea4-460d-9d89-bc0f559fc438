<?php
/**
 * Service Category Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new service category
 */
function createServiceCategory($data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Category name is required'];
        }
        
        $name = trim($data['name']);
        
        // Check if category already exists
        $existingCategory = $database->fetch(
            "SELECT id FROM service_categories WHERE name = ?",
            [$name]
        );
        
        if ($existingCategory) {
            return ['success' => false, 'error' => 'Category with this name already exists'];
        }
        
        $categoryId = generateUUID();
        
        $database->query(
            "INSERT INTO service_categories (id, name, description, is_active, created_at, updated_at) 
             VALUES (?, ?, ?, ?, NOW(), NOW())",
            [
                $categoryId,
                sanitize($name),
                sanitize($data['description'] ?? ''),
                isset($data['is_active']) ? 1 : 0
            ]
        );
        
        return ['success' => true, 'id' => $categoryId];
        
    } catch (Exception $e) {
        error_log("Service category creation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create category'];
    }
}

/**
 * Update an existing service category
 */
function updateServiceCategory($categoryId, $data) {
    global $database;
    
    try {
        // Validate required fields
        if (empty($data['name'])) {
            return ['success' => false, 'error' => 'Category name is required'];
        }
        
        $name = trim($data['name']);
        
        // Check if category exists
        $currentCategory = $database->fetch("SELECT * FROM service_categories WHERE id = ?", [$categoryId]);
        if (!$currentCategory) {
            return ['success' => false, 'error' => 'Category not found'];
        }
        
        // Check if another category with the same name exists (excluding current)
        $existingCategory = $database->fetch(
            "SELECT id FROM service_categories WHERE name = ? AND id != ?",
            [$name, $categoryId]
        );
        
        if ($existingCategory) {
            return ['success' => false, 'error' => 'Category with this name already exists'];
        }
        
        $database->query(
            "UPDATE service_categories SET name = ?, description = ?, is_active = ?, updated_at = NOW() 
             WHERE id = ?",
            [
                sanitize($name),
                sanitize($data['description'] ?? ''),
                isset($data['is_active']) ? 1 : 0,
                $categoryId
            ]
        );
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service category update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update category'];
    }
}

/**
 * Delete a service category
 */
function deleteServiceCategory($categoryId) {
    global $database;
    
    try {
        // Check if category exists
        $category = $database->fetch("SELECT * FROM service_categories WHERE id = ?", [$categoryId]);
        if (!$category) {
            return ['success' => false, 'error' => 'Category not found'];
        }
        
        // Check if category is being used by any services
        $servicesCount = $database->fetch(
            "SELECT COUNT(*) as count FROM services WHERE category = ?",
            [$category['name']]
        )['count'];
        
        if ($servicesCount > 0) {
            // Get service names for better error message
            $services = $database->fetchAll(
                "SELECT name FROM services WHERE category = ? LIMIT 5",
                [$category['name']]
            );
            $serviceNames = array_column($services, 'name');
            $moreText = $servicesCount > 5 ? " and " . ($servicesCount - 5) . " more" : "";
            
            return [
                'success' => false,
                'error' => 'Cannot delete category that is being used by services',
                'details' => "This category is currently used by $servicesCount service(s). Please reassign or delete these services first.",
                'services' => array_map(function($name) { return "• $name"; }, $serviceNames),
                'suggestions' => [
                    'Reassign services to a different category',
                    'Delete the services that use this category',
                    'Deactivate the category instead of deleting it'
                ]
            ];
        }
        
        // Delete the category
        $result = $database->query("DELETE FROM service_categories WHERE id = ?", [$categoryId]);
        
        if ($result === false) {
            return ['success' => false, 'error' => 'Failed to delete category from database'];
        }
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Service category deletion error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => 'Database error occurred',
            'details' => 'An unexpected error occurred while deleting the category. Please try again or contact support.',
            'technical_error' => $e->getMessage()
        ];
    }
}

/**
 * Get service category by ID
 */
function getServiceCategoryById($categoryId) {
    global $database;
    
    return $database->fetch("SELECT * FROM service_categories WHERE id = ?", [$categoryId]);
}

/**
 * Get all service categories
 */
function getAllServiceCategories($activeOnly = false) {
    global $database;
    
    $whereClause = $activeOnly ? "WHERE is_active = 1" : "";
    return $database->fetchAll("SELECT * FROM service_categories $whereClause ORDER BY name");
}

/**
 * Get active service categories
 */
function getActiveServiceCategories() {
    return getAllServiceCategories(true);
}

/**
 * Get service categories with service count
 */
function getServiceCategoriesWithCount() {
    global $database;
    
    return $database->fetchAll("
        SELECT 
            sc.*,
            COUNT(s.id) as service_count
        FROM service_categories sc
        LEFT JOIN services s ON sc.name = s.category
        GROUP BY sc.id, sc.name, sc.description, sc.is_active, sc.created_at, sc.updated_at
        ORDER BY sc.name
    ");
}

/**
 * Toggle service category status
 */
function toggleServiceCategoryStatus($categoryId) {
    global $database;
    
    try {
        $category = $database->fetch("SELECT * FROM service_categories WHERE id = ?", [$categoryId]);
        if (!$category) {
            return ['success' => false, 'error' => 'Category not found'];
        }
        
        $newStatus = $category['is_active'] ? 0 : 1;
        
        $database->query(
            "UPDATE service_categories SET is_active = ?, updated_at = NOW() WHERE id = ?",
            [$newStatus, $categoryId]
        );
        
        return ['success' => true, 'new_status' => $newStatus];
        
    } catch (Exception $e) {
        error_log("Service category status toggle error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update category status'];
    }
}

/**
 * Check if category name is available
 */
function isServiceCategoryNameAvailable($name, $excludeId = null) {
    global $database;
    
    $params = [trim($name)];
    $whereClause = "WHERE name = ?";
    
    if ($excludeId) {
        $whereClause .= " AND id != ?";
        $params[] = $excludeId;
    }
    
    $existing = $database->fetch("SELECT id FROM service_categories $whereClause", $params);
    return !$existing;
}
?>
