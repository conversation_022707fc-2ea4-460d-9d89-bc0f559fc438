<?php
/**
 * URGENT: Fix system_settings table AUTO_INCREMENT issue
 * This script specifically fixes the system_settings table that's causing UUID insertion failures
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>🚨 URGENT: Fix system_settings Table</h1>";

try {
    echo "<h2>1. Current system_settings Table Structure</h2>";
    
    // Check current structure
    $columns = $database->fetchAll("DESCRIBE system_settings");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasAutoIncrement = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'id' && strpos($column['Extra'], 'auto_increment') !== false) {
            $hasAutoIncrement = true;
        }
    }
    echo "</table>";
    
    if ($hasAutoIncrement) {
        echo "<p style='color: red; font-size: 16px;'>❌ PROBLEM CONFIRMED: system_settings.id has AUTO_INCREMENT</p>";
    } else {
        echo "<p style='color: green; font-size: 16px;'>✅ system_settings.id already uses UUID</p>";
    }
    
    echo "<h2>2. Current Data in system_settings</h2>";
    $currentData = $database->fetchAll("SELECT * FROM system_settings ORDER BY id");
    
    if (!empty($currentData)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Setting Key</th><th>Setting Value</th><th>Updated At</th></tr>";
        foreach ($currentData as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['setting_key']}</td>";
            echo "<td>" . htmlspecialchars(substr($row['setting_value'], 0, 50)) . (strlen($row['setting_value']) > 50 ? '...' : '') . "</td>";
            echo "<td>{$row['updated_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Total records:</strong> " . count($currentData) . "</p>";
    } else {
        echo "<p>No data found in system_settings table.</p>";
    }
    
    echo "<h2>3. Apply Fix</h2>";
    
    if (isset($_POST['apply_fix']) && $hasAutoIncrement) {
        echo "<h3>🔧 Applying Fix...</h3>";
        
        try {
            $database->beginTransaction();
            
            echo "<p>Step 1: Creating new table structure...</p>";
            
            // Create new table with UUID structure
            $database->execute("
                CREATE TABLE system_settings_new (
                    id VARCHAR(36) PRIMARY KEY,
                    setting_key VARCHAR(100) NOT NULL,
                    setting_value TEXT DEFAULT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_setting_key (setting_key)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ");
            
            echo "<p style='color: green;'>✅ New table structure created</p>";
            
            echo "<p>Step 2: Migrating existing data...</p>";
            
            // Migrate existing data with new UUIDs
            foreach ($currentData as $row) {
                $newId = generateUUID();
                $database->execute(
                    "INSERT INTO system_settings_new (id, setting_key, setting_value, updated_at) VALUES (?, ?, ?, ?)",
                    [$newId, $row['setting_key'], $row['setting_value'], $row['updated_at']]
                );
                echo "<p>✓ Migrated: {$row['setting_key']} (old ID: {$row['id']} → new ID: $newId)</p>";
            }
            
            echo "<p>Step 3: Replacing old table...</p>";
            
            // Replace old table
            $database->execute("DROP TABLE system_settings");
            $database->execute("RENAME TABLE system_settings_new TO system_settings");
            
            echo "<p style='color: green;'>✅ Table replacement completed</p>";
            
            $database->commit();
            
            echo "<h3 style='color: green; font-size: 20px;'>🎉 SUCCESS! system_settings table fixed!</h3>";
            
            // Verify the fix
            echo "<h3>4. Verification Test</h3>";
            try {
                $testId = generateUUID();
                $database->execute(
                    "INSERT INTO system_settings (id, setting_key, setting_value) VALUES (?, ?, ?)",
                    [$testId, 'test_setting_' . time(), 'test_value']
                );
                
                $insertedRecord = $database->fetch("SELECT * FROM system_settings WHERE id = ?", [$testId]);
                if ($insertedRecord) {
                    echo "<p style='color: green;'>✅ VERIFICATION PASSED: UUID insertion works correctly</p>";
                    echo "<p><strong>Test record:</strong> ID = {$insertedRecord['id']}, Key = {$insertedRecord['setting_key']}</p>";
                    
                    // Clean up test record
                    $database->execute("DELETE FROM system_settings WHERE id = ?", [$testId]);
                    echo "<p>✓ Test record cleaned up</p>";
                } else {
                    echo "<p style='color: red;'>❌ VERIFICATION FAILED: Could not retrieve inserted record</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ VERIFICATION FAILED: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ ERROR: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p style='color: blue;'>ℹ️ Transaction rolled back. No changes were made.</p>";
        }
    }
    
    echo "<h2>4. Manual Verification</h2>";
    
    if (isset($_POST['test_insert'])) {
        echo "<h3>Testing UUID Insertion...</h3>";
        
        try {
            $testId = generateUUID();
            $testKey = 'manual_test_' . time();
            $testValue = 'Manual test value - ' . date('Y-m-d H:i:s');
            
            $database->execute(
                "INSERT INTO system_settings (id, setting_key, setting_value) VALUES (?, ?, ?)",
                [$testId, $testKey, $testValue]
            );
            
            echo "<p style='color: green;'>✅ SUCCESS: UUID insertion works!</p>";
            echo "<p><strong>Inserted:</strong> ID = $testId, Key = $testKey</p>";
            
            // Clean up
            $database->execute("DELETE FROM system_settings WHERE id = ?", [$testId]);
            echo "<p>✓ Test record cleaned up</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ FAILED: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Critical error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🛠️ Action Buttons</h2>";
?>

<?php if ($hasAutoIncrement): ?>
<div style="background: #ffebee; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #f44336;">
    <h3>🚨 URGENT FIX REQUIRED</h3>
    <p><strong>The system_settings table has AUTO_INCREMENT which is incompatible with UUID.</strong></p>
    <p>This is causing the "Data truncated for column 'id'" error.</p>
    <form method="post">
        <input type="hidden" name="apply_fix" value="1">
        <button type="submit" style="background: #f44336; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; font-weight: bold;">
            🔧 FIX system_settings TABLE NOW
        </button>
    </form>
</div>
<?php else: ?>
<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #4caf50;">
    <h3>✅ Table Structure is Correct</h3>
    <p>The system_settings table is already using UUID. You can test insertion below.</p>
</div>
<?php endif; ?>

<div style="background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>🧪 Test UUID Insertion</h3>
    <p>Test if UUID insertion works correctly in the system_settings table.</p>
    <form method="post">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit" style="background: #2196f3; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
            Test UUID Insertion
        </button>
    </form>
</div>

<div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>📋 After Fixing</h3>
    <ol>
        <li>Run the fix above to convert the table</li>
        <li>Test UUID insertion to verify it works</li>
        <li>Go back to <a href="production-test-suite.php">production-test-suite.php</a> and run the database integrity test again</li>
        <li>All database operations should now work correctly</li>
    </ol>
</div>
