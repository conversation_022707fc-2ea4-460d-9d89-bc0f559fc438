-- Simple Categories Migration Script
-- Flix Salon & SPA Database Schema Update
-- This script creates a simple subcategory table approach

USE flix_salonce2;

-- Start transaction for data integrity
START TRANSACTION;

-- Step 1: Revert service_categories table to original simple structure
-- First, let's backup the current data
CREATE TABLE IF NOT EXISTS service_categories_backup AS SELECT * FROM service_categories;

-- Drop the complex constraints and columns
ALTER TABLE service_categories 
DROP FOREIGN KEY IF EXISTS fk_service_categories_parent,
DROP INDEX IF EXISTS idx_parent_id,
DROP INDEX IF EXISTS idx_level,
DROP INDEX IF EXISTS idx_sort_order,
DROP INDEX IF EXISTS unique_name_per_parent;

-- Remove the hierarchical columns
ALTER TABLE service_categories 
DROP COLUMN IF EXISTS parent_id,
DROP COLUMN IF EXISTS level,
DROP COLUMN IF EXISTS sort_order;

-- Restore simple unique constraint on name
ALTER TABLE service_categories 
ADD CONSTRAINT unique_category_name UNIQUE (name);

-- Step 2: Create simple service_subcategories table
CREATE TABLE IF NOT EXISTS service_subcategories (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category_id VARCHAR(36) NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_subcategory_per_category (name, category_id),
    INDEX idx_category_id (category_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
);

-- Step 3: Update services table to support both category and subcategory
-- Add subcategory_id column if it doesn't exist
ALTER TABLE services 
ADD COLUMN IF NOT EXISTS subcategory_id VARCHAR(36) NULL AFTER category_id;

-- Add foreign key for subcategory
ALTER TABLE services 
ADD CONSTRAINT fk_services_subcategory 
FOREIGN KEY (subcategory_id) REFERENCES service_subcategories(id) ON DELETE SET NULL;

-- Add index for subcategory_id
ALTER TABLE services 
ADD INDEX IF NOT EXISTS idx_services_subcategory_id (subcategory_id);

-- Step 4: Clean up existing categories to be main categories only
-- Remove any test categories from previous implementation
DELETE FROM service_categories WHERE name LIKE 'Test%';

-- Reset sort order for main categories
SET @row_number = 0;
UPDATE service_categories 
SET sort_order = (@row_number := @row_number + 1)
WHERE sort_order IS NOT NULL
ORDER BY name;

-- Step 5: Create some sample subcategories for existing categories
-- Hair subcategories
INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Hair Cutting' as name,
    'Professional hair cutting services' as description,
    sc.id as category_id,
    1 as sort_order
FROM service_categories sc WHERE sc.name = 'Hair';

INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Hair Coloring' as name,
    'Hair coloring and highlighting services' as description,
    sc.id as category_id,
    2 as sort_order
FROM service_categories sc WHERE sc.name = 'Hair';

INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Hair Styling' as name,
    'Hair styling and treatment services' as description,
    sc.id as category_id,
    3 as sort_order
FROM service_categories sc WHERE sc.name = 'Hair';

-- Beauty subcategories
INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Facial Treatments' as name,
    'Professional facial and skin care treatments' as description,
    sc.id as category_id,
    1 as sort_order
FROM service_categories sc WHERE sc.name = 'Beauty';

INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Skin Care' as name,
    'Advanced skin care treatments' as description,
    sc.id as category_id,
    2 as sort_order
FROM service_categories sc WHERE sc.name = 'Beauty';

-- Nails subcategories
INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Manicure' as name,
    'Professional manicure services' as description,
    sc.id as category_id,
    1 as sort_order
FROM service_categories sc WHERE sc.name = 'Nails';

INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Pedicure' as name,
    'Professional pedicure services' as description,
    sc.id as category_id,
    2 as sort_order
FROM service_categories sc WHERE sc.name = 'Nails';

-- Massage subcategories
INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Relaxation Massage' as name,
    'Relaxing massage treatments' as description,
    sc.id as category_id,
    1 as sort_order
FROM service_categories sc WHERE sc.name = 'Massage';

INSERT IGNORE INTO service_subcategories (id, name, description, category_id, sort_order) 
SELECT 
    UUID() as id,
    'Therapeutic Massage' as name,
    'Therapeutic and deep tissue massage' as description,
    sc.id as category_id,
    2 as sort_order
FROM service_categories sc WHERE sc.name = 'Massage';

-- Commit the transaction
COMMIT;

-- Verification queries
SELECT 'Migration completed successfully. Verifying table structures...' as status;

-- Show updated table structures
SELECT 'Main Categories:' as info;
SELECT id, name, description, is_active 
FROM service_categories 
ORDER BY name;

SELECT 'Subcategories:' as info;
SELECT sc.name as category, ss.name as subcategory, ss.description, ss.sort_order
FROM service_subcategories ss
JOIN service_categories sc ON ss.category_id = sc.id
ORDER BY sc.name, ss.sort_order;

SELECT 'Services with Categories:' as info;
SELECT s.name as service, sc.name as category, ss.name as subcategory
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
LEFT JOIN service_subcategories ss ON s.subcategory_id = ss.id
LIMIT 10;

SELECT 'Migration completed successfully!' as final_status;
