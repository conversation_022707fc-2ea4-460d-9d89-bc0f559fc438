<?php
/**
 * Careers Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Sample job openings
$jobOpenings = [
    [
        'id' => 1,
        'title' => 'Senior Hair Stylist',
        'department' => 'Hair Services',
        'type' => 'Full-time',
        'experience' => '3+ years',
        'description' => 'We are seeking an experienced hair stylist to join our team. Must have expertise in cutting, coloring, and styling.',
        'requirements' => [
            'Valid cosmetology license',
            '3+ years of salon experience',
            'Expertise in color techniques',
            'Excellent customer service skills',
            'Ability to work in a team environment'
        ],
        'benefits' => [
            'Competitive commission structure',
            'Health insurance',
            'Paid time off',
            'Continuing education opportunities',
            'Product discounts'
        ]
    ],
    [
        'id' => 2,
        'title' => 'Makeup Artist',
        'department' => 'Beauty Services',
        'type' => 'Part-time',
        'experience' => '2+ years',
        'description' => 'Looking for a talented makeup artist specializing in bridal and special event makeup.',
        'requirements' => [
            'Professional makeup certification',
            '2+ years of makeup experience',
            'Bridal makeup expertise',
            'Own professional makeup kit',
            'Flexible schedule including weekends'
        ],
        'benefits' => [
            'Flexible scheduling',
            'Commission-based pay',
            'Networking opportunities',
            'Access to premium products',
            'Professional development'
        ]
    ],
    [
        'id' => 3,
        'title' => 'Receptionist',
        'department' => 'Front Desk',
        'type' => 'Full-time',
        'experience' => 'Entry level',
        'description' => 'Seeking a friendly and organized individual to manage front desk operations and customer service.',
        'requirements' => [
            'High school diploma or equivalent',
            'Excellent communication skills',
            'Computer proficiency',
            'Customer service experience preferred',
            'Professional appearance'
        ],
        'benefits' => [
            'Hourly wage + tips',
            'Health insurance',
            'Paid time off',
            'Service discounts',
            'Growth opportunities'
        ]
    ]
];

$pageTitle = "Careers";
include __DIR__ . '/includes/header.php';
?>

<!-- Hero Section -->
<section class="relative bg-salon-black py-24">
    <div class="absolute inset-0 bg-gradient-to-r from-salon-black via-salon-black/90 to-transparent"></div>
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30" 
         style="background-image: url('https://images.unsplash.com/photo-**********-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-3xl">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Join Our <span class="text-salon-gold">Team</span>
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                Build your career in beauty with Flix Salonce. We're always looking for talented, passionate professionals to join our growing team.
            </p>
        </div>
    </div>
</section>

<!-- Why Work With Us -->
<section class="py-20 bg-secondary-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-white mb-8">Why Work at Flix Salonce?</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-line text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Career Growth</h3>
                <p class="text-gray-300 text-sm">Opportunities for advancement and professional development in a growing company.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-graduation-cap text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Continuing Education</h3>
                <p class="text-gray-300 text-sm">Regular training sessions and workshops to keep your skills current and sharp.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Team Environment</h3>
                <p class="text-gray-300 text-sm">Work alongside talented professionals in a supportive, collaborative atmosphere.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-heart text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Great Benefits</h3>
                <p class="text-gray-300 text-sm">Competitive compensation, health benefits, and employee discounts.</p>
            </div>
        </div>
    </div>
</section>

<!-- Current Openings -->
<section class="py-20 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-white mb-8">Current Openings</h2>
            <p class="text-gray-300 max-w-2xl mx-auto">
                Explore our current job opportunities and find the perfect role to advance your beauty career.
            </p>
        </div>
        
        <div class="space-y-8">
            <?php foreach ($jobOpenings as $job): ?>
                <div class="bg-secondary-800 rounded-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                        <div>
                            <h3 class="text-2xl font-bold text-white mb-2"><?= htmlspecialchars($job['title']) ?></h3>
                            <div class="flex flex-wrap gap-4 text-sm">
                                <span class="bg-salon-gold/20 text-salon-gold px-3 py-1 rounded-full">
                                    <?= htmlspecialchars($job['department']) ?>
                                </span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                                    <?= htmlspecialchars($job['type']) ?>
                                </span>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                                    <?= htmlspecialchars($job['experience']) ?>
                                </span>
                            </div>
                        </div>
                        <button onclick="toggleJobDetails(<?= $job['id'] ?>)" class="mt-4 lg:mt-0 bg-salon-gold hover:bg-yellow-500 text-black px-6 py-2 rounded-lg font-semibold transition-colors">
                            View Details
                        </button>
                    </div>
                    
                    <p class="text-gray-300 mb-6"><?= htmlspecialchars($job['description']) ?></p>
                    
                    <div id="jobDetails<?= $job['id'] ?>" class="hidden">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold text-salon-gold mb-3">Requirements</h4>
                                <ul class="space-y-2">
                                    <?php foreach ($job['requirements'] as $requirement): ?>
                                        <li class="flex items-start text-gray-300">
                                            <i class="fas fa-check text-salon-gold mr-2 mt-1"></i>
                                            <?= htmlspecialchars($requirement) ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <div>
                                <h4 class="text-lg font-semibold text-salon-gold mb-3">Benefits</h4>
                                <ul class="space-y-2">
                                    <?php foreach ($job['benefits'] as $benefit): ?>
                                        <li class="flex items-start text-gray-300">
                                            <i class="fas fa-star text-salon-gold mr-2 mt-1"></i>
                                            <?= htmlspecialchars($benefit) ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-6 pt-6 border-t border-secondary-700">
                            <button onclick="openApplicationModal('<?= htmlspecialchars($job['title']) ?>')" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold transition-colors">
                                Apply Now
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Application Process -->
<section class="py-20 bg-secondary-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-white mb-8">Application Process</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-salon-gold font-bold text-xl">1</span>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Apply Online</h3>
                <p class="text-gray-300 text-sm">Submit your application and resume through our online form.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-salon-gold font-bold text-xl">2</span>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Initial Review</h3>
                <p class="text-gray-300 text-sm">Our team reviews your application and qualifications.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-salon-gold font-bold text-xl">3</span>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Interview</h3>
                <p class="text-gray-300 text-sm">Meet with our team for an in-person or virtual interview.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-salon-gold font-bold text-xl">4</span>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Welcome Aboard</h3>
                <p class="text-gray-300 text-sm">Join our team and start your journey with Flix Salonce!</p>
            </div>
        </div>
    </div>
</section>

<!-- General Application -->
<section class="py-20 bg-salon-black">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-secondary-800 rounded-lg p-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-white mb-4">Don't See the Right Position?</h2>
                <p class="text-gray-300">
                    We're always looking for talented individuals. Submit a general application and we'll keep you in mind for future opportunities.
                </p>
            </div>
            
            <div class="text-center">
                <button onclick="openApplicationModal('General Application')" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-4 rounded-lg font-bold text-lg transition-colors">
                    Submit General Application
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Application Modal -->
<div id="applicationModal" class="fixed inset-0 bg-black/50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-white" id="modalTitle">Apply for Position</h3>
                <button onclick="closeApplicationModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form id="applicationForm" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-white font-semibold mb-2">First Name *</label>
                        <input type="text" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                    <div>
                        <label class="block text-white font-semibold mb-2">Last Name *</label>
                        <input type="text" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-white font-semibold mb-2">Email *</label>
                        <input type="email" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                    <div>
                        <label class="block text-white font-semibold mb-2">Phone *</label>
                        <input type="tel" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">Position Applying For</label>
                    <input type="text" id="positionField" readonly class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white">
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">Years of Experience</label>
                    <select class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option>Less than 1 year</option>
                        <option>1-2 years</option>
                        <option>3-5 years</option>
                        <option>5+ years</option>
                    </select>
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">Resume/CV *</label>
                    <input type="file" accept=".pdf,.doc,.docx" required class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-salon-gold file:text-black file:font-semibold hover:file:bg-yellow-500">
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">Cover Letter</label>
                    <textarea rows="4" class="w-full px-4 py-3 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold" placeholder="Tell us why you'd be a great fit for our team..."></textarea>
                </div>

                <div class="flex gap-4">
                    <button type="button" onclick="closeApplicationModal()" class="flex-1 bg-secondary-700 hover:bg-secondary-600 text-white py-3 rounded-lg font-semibold transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="flex-1 bg-salon-gold hover:bg-yellow-500 text-black py-3 rounded-lg font-semibold transition-colors">
                        Submit Application
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleJobDetails(jobId) {
    const details = document.getElementById('jobDetails' + jobId);
    details.classList.toggle('hidden');
}

function openApplicationModal(position) {
    document.getElementById('modalTitle').textContent = 'Apply for ' + position;
    document.getElementById('positionField').value = position;
    document.getElementById('applicationModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeApplicationModal() {
    document.getElementById('applicationModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Form submission
document.getElementById('applicationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('Application submitted successfully! We will review your application and contact you soon.');
    closeApplicationModal();
});

// Close modal on backdrop click
document.getElementById('applicationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApplicationModal();
    }
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
