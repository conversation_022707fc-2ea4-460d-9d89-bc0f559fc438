<?php
/**
 * Staff Schedule Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Get all active staff members from users table
 */
function getActiveStaffMembers() {
    global $database;
    
    try {
        return $database->fetchAll(
            "SELECT id, name, email, phone FROM users WHERE role = 'STAFF' ORDER BY name ASC"
        );
    } catch (Exception $e) {
        error_log("Error getting staff members: " . $e->getMessage());
        return [];
    }
}

/**
 * Get staff member details from staff_schedules table
 */
function getStaffDetails($userId) {
    global $database;

    try {
        return $database->fetch(
            "SELECT * FROM staff_schedules WHERE user_id = ?",
            [$userId]
        );
    } catch (Exception $e) {
        error_log("Error getting staff details: " . $e->getMessage());
        return null;
    }
}

/**
 * Create staff schedule record in staff_schedules table from users table
 */
function createStaffRecord($userId, $data = []) {
    global $database;

    try {
        // Get user details
        $user = $database->fetch(
            "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
            [$userId]
        );

        if (!$user) {
            return ['success' => false, 'error' => 'User not found or not a staff member'];
        }

        // Check if staff schedule record already exists
        $existingStaff = $database->fetch(
            "SELECT id FROM staff_schedules WHERE user_id = ?",
            [$userId]
        );

        if ($existingStaff) {
            return ['success' => false, 'error' => 'Staff schedule record already exists'];
        }
        
        // Default schedule (9 AM to 6 PM, Monday to Saturday)
        $defaultSchedule = [
            'monday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
            'tuesday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
            'wednesday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
            'thursday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
            'friday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
            'saturday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => true, 'available' => true, 'start' => '09:00', 'end' => '18:00'],
            'sunday' => ['start_time' => '09:00', 'end_time' => '18:00', 'is_working' => false, 'available' => false, 'start' => '09:00', 'end' => '18:00']
        ];
        
        // Insert staff schedule record
        $scheduleId = 'schedule-' . $userId;
        $database->query(
            "INSERT INTO staff_schedules (id, user_id, role, specialties, hourly_rate, schedule, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $scheduleId,
                $userId,
                $data['role'] ?? 'Staff Member',
                json_encode($data['specialties'] ?? []),
                $data['hourly_rate'] ?? 50.00,
                json_encode($defaultSchedule)
            ]
        );

        return ['success' => true, 'id' => $scheduleId];
        
    } catch (Exception $e) {
        error_log("Error creating staff record: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create staff record'];
    }
}

/**
 * Update staff schedule
 */
function updateStaffSchedule($userId, $schedule) {
    global $database;

    try {
        // Check if staff_schedules record exists
        $existingSchedule = $database->fetch(
            "SELECT id, role, hourly_rate FROM staff_schedules WHERE user_id = ? LIMIT 1",
            [$userId]
        );

        if ($existingSchedule) {
            // Update existing record, preserving role and hourly_rate
            $database->query(
                "UPDATE staff_schedules SET schedule = ?, updated_at = NOW() WHERE user_id = ?",
                [json_encode($schedule), $userId]
            );
        } else {
            // Create new record with default values
            $scheduleId = generateUUID();
            $database->query(
                "INSERT INTO staff_schedules (id, user_id, role, hourly_rate, schedule, created_at, updated_at)
                 VALUES (?, ?, 'Staff Member', 500000, ?, NOW(), NOW())",
                [$scheduleId, $userId, json_encode($schedule)]
            );
        }

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Error updating staff schedule: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update schedule'];
    }
}

/**
 * Get staff availability for a specific date and time
 */
function getStaffAvailability($userId, $date, $startTime, $endTime) {
    global $database;

    try {
        // First check if user is a staff member
        $user = $database->fetch(
            "SELECT id FROM users WHERE id = ? AND role = 'STAFF'",
            [$userId]
        );

        if (!$user) {
            return false;
        }

        // Get staff schedule - try to get from staff_schedules table
        $staff = $database->fetch(
            "SELECT schedule FROM staff_schedules WHERE user_id = ?",
            [$userId]
        );

        // If no schedule record exists, create one with default schedule
        if (!$staff) {
            $result = createStaffRecord($userId);
            if ($result['success']) {
                $staff = $database->fetch(
                    "SELECT schedule FROM staff_schedules WHERE user_id = ?",
                    [$userId]
                );
            }
        }

        if (!$staff || !$staff['schedule']) {
            // If still no schedule, assume basic availability (9 AM to 6 PM, Mon-Sat)
            $dayOfWeek = strtolower(date('l', strtotime($date)));
            if ($dayOfWeek === 'sunday') {
                return false;
            }

            // Basic time check for default hours
            if ($startTime < '09:00' || $endTime > '18:00') {
                return false;
            }
        } else {
            $schedule = json_decode($staff['schedule'], true);
            $dayOfWeek = strtolower(date('l', strtotime($date)));

            // Check if staff works on this day (handle both formats)
            if (!isset($schedule[$dayOfWeek])) {
                return false;
            }

            $daySchedule = $schedule[$dayOfWeek];

            // Check availability (handle both 'is_working' and 'available' formats)
            $isWorking = $daySchedule['is_working'] ?? $daySchedule['available'] ?? false;
            if (!$isWorking) {
                return false;
            }

            // Get the correct time field names (handle both formats for compatibility)
            $workStart = $daySchedule['start_time'] ?? $daySchedule['start'] ?? '09:00';
            $workEnd = $daySchedule['end_time'] ?? $daySchedule['end'] ?? '18:00';

            // Check if requested time is within working hours
            if ($startTime < $workStart || $endTime > $workEnd) {
                return false;
            }
        }

        // Check for existing bookings (use user_id since that's what we have)
        $existingBookings = $database->fetchAll(
            "SELECT start_time, end_time FROM bookings
             WHERE staff_id = ? AND date = ? AND status IN ('CONFIRMED', 'IN_PROGRESS')
             AND ((start_time <= ? AND end_time > ?) OR (start_time < ? AND end_time >= ?))",
            [$userId, $date, $startTime, $startTime, $endTime, $endTime]
        );

        return empty($existingBookings);

    } catch (Exception $e) {
        error_log("Error checking staff availability: " . $e->getMessage());
        return false;
    }
}

/**
 * Assign staff to booking
 */
function assignStaffToBooking($bookingId, $userId) {
    global $database;

    try {
        // Get booking details
        $booking = $database->fetch(
            "SELECT date, start_time, end_time FROM bookings WHERE id = ?",
            [$bookingId]
        );

        if (!$booking) {
            return ['success' => false, 'error' => 'Booking not found'];
        }

        // Check staff availability
        if (!getStaffAvailability($userId, $booking['date'], $booking['start_time'], $booking['end_time'])) {
            return ['success' => false, 'error' => 'Staff member is not available at this time'];
        }

        // Get current booking details for notification
        $currentBooking = $database->fetch(
            "SELECT staff_id FROM bookings WHERE id = ?",
            [$bookingId]
        );

        // Update booking with staff assignment
        $database->query(
            "UPDATE bookings SET staff_id = ?, updated_at = NOW() WHERE id = ?",
            [$userId, $bookingId]
        );

        // Create notification for staff assignment
        require_once __DIR__ . '/notification_triggers.php';

        // Get admin ID from session (assuming this is called from admin panel)
        $adminId = $_SESSION['user_id'] ?? null;

        if ($adminId) {
            // Notify old staff member if they were unassigned
            if ($currentBooking['staff_id'] && $currentBooking['staff_id'] !== $userId) {
                createAdminBookingNotificationForStaff($bookingId, 'ADMIN_STAFF_UNASSIGNED', $adminId, [
                    'old_staff_id' => $currentBooking['staff_id']
                ]);
            }

            // Notify new staff member
            createAdminBookingNotificationForStaff($bookingId, 'ADMIN_STAFF_ASSIGNED', $adminId);
        }

        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Error assigning staff to booking: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to assign staff'];
    }
}

/**
 * Get available staff for a specific date and time
 */
function getAvailableStaff($date, $startTime, $endTime) {
    global $database;
    
    try {
        $availableStaff = [];
        $allStaff = getActiveStaffMembers();
        
        foreach ($allStaff as $staff) {
            if (getStaffAvailability($staff['id'], $date, $startTime, $endTime)) {
                $staffDetails = getStaffDetails($staff['id']);
                $availableStaff[] = [
                    'id' => $staff['id'],
                    'name' => $staff['name'],
                    'email' => $staff['email'],
                    'role' => $staffDetails['role'] ?? 'Staff Member',
                    'hourly_rate' => $staffDetails['hourly_rate'] ?? 0
                ];
            }
        }
        
        return $availableStaff;
        
    } catch (Exception $e) {
        error_log("Error getting available staff: " . $e->getMessage());
        return [];
    }
}

/**
 * Get staff schedule for display
 */
function getStaffScheduleForDisplay($userId) {
    global $database;

    try {
        $staff = $database->fetch(
            "SELECT schedule FROM staff_schedules WHERE user_id = ?",
            [$userId]
        );

        if (!$staff) {
            return null;
        }

        return json_decode($staff['schedule'], true);
        
    } catch (Exception $e) {
        error_log("Error getting staff schedule: " . $e->getMessage());
        return null;
    }
}

/**
 * Sync staff from users table to staff table
 */
function syncStaffFromUsers() {
    global $database;

    try {
        // Get all staff users
        $staffUsers = $database->fetchAll(
            "SELECT id, name, email, phone FROM users WHERE role = 'STAFF'"
        );

        $synced = 0;
        foreach ($staffUsers as $user) {
            // Check if staff schedule record exists
            $existingStaff = $database->fetch(
                "SELECT id FROM staff_schedules WHERE user_id = ?",
                [$user['id']]
            );

            if (!$existingStaff) {
                $result = createStaffRecord($user['id']);
                if ($result['success']) {
                    $synced++;
                }
            }
        }

        return ['success' => true, 'synced' => $synced];

    } catch (Exception $e) {
        error_log("Error syncing staff: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to sync staff'];
    }
}
