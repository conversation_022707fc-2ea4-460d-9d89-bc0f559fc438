<?php
// Earnings functions - database connection handled by app.php

/**
 * Earnings and Revenue Management Functions
 * Handles all financial analytics, revenue tracking, and earnings calculations
 */

/**
 * Get comprehensive earnings data for dashboard
 */
function getEarningsData($range = 'month', $startDate = null, $endDate = null) {
    global $database;
    
    $now = new DateTime();
    
    // Set date range based on period
    if (!$startDate || !$endDate) {
        switch ($range) {
            case 'today':
                $startDate = $now->format('Y-m-d 00:00:00');
                $endDate = $now->format('Y-m-d 23:59:59');
                break;
            case 'week':
                $startDate = $now->modify('-7 days')->format('Y-m-d 00:00:00');
                $endDate = (new DateTime())->format('Y-m-d 23:59:59');
                break;
            case 'year':
                $startDate = $now->format('Y-01-01 00:00:00');
                $endDate = $now->format('Y-12-31 23:59:59');
                break;
            case 'month':
            default:
                $startDate = $now->format('Y-m-01 00:00:00');
                $endDate = $now->format('Y-m-t 23:59:59');
                break;
        }
    }
    
    return [
        'totalRevenue' => getTotalRevenue(),
        'periodRevenue' => getPeriodRevenue($startDate, $endDate),
        'dailyRevenue' => getDailyRevenue(),
        'revenueGrowth' => getRevenueGrowth($startDate, $endDate),
        'totalTransactions' => getTotalTransactions(),
        'averageOrderValue' => getAverageOrderValue(),
        'topServices' => getTopServicesByRevenue(5),
        'monthlyData' => getMonthlyRevenueData(12),
        'recentTransactions' => getRecentTransactions(10),
        'paymentMethods' => getPaymentMethodBreakdown(),
        'staffEarnings' => getAllStaffEarnings($startDate, $endDate)
    ];
}

/**
 * Get total revenue from all completed bookings
 */
function getTotalRevenue() {
    global $database;

    try {
        $result = $database->fetch("
            SELECT COALESCE(SUM(total_amount), 0) as total_revenue
            FROM bookings
            WHERE status = 'COMPLETED'
        ");

        return floatval($result['total_revenue'] ?? 0);
    } catch (Exception $e) {
        return 0;
    }
}

/**
 * Get revenue for specific period
 */
function getPeriodRevenue($startDate, $endDate) {
    global $database;

    try {
        $result = $database->fetch("
            SELECT COALESCE(SUM(total_amount), 0) as period_revenue
            FROM bookings
            WHERE status = 'COMPLETED'
            AND date BETWEEN ? AND ?
        ", [$startDate, $endDate]);

        return floatval($result['period_revenue'] ?? 0);
    } catch (Exception $e) {
        return 0;
    }
}

/**
 * Get today's revenue
 */
function getDailyRevenue() {
    global $database;
    
    $today = date('Y-m-d');
    
    $result = $database->fetch("
        SELECT COALESCE(SUM(total_amount), 0) as daily_revenue
        FROM bookings 
        WHERE status = 'COMPLETED' 
        AND DATE(date) = ?
    ", [$today]);
    
    return floatval($result['daily_revenue']);
}

/**
 * Calculate revenue growth percentage
 */
function getRevenueGrowth($startDate, $endDate) {
    global $database;
    
    $currentRevenue = getPeriodRevenue($startDate, $endDate);
    
    // Calculate previous period
    $start = new DateTime($startDate);
    $end = new DateTime($endDate);
    $diff = $start->diff($end);
    
    $prevStart = clone $start;
    $prevStart->sub($diff);
    $prevEnd = clone $end;
    $prevEnd->sub($diff);
    
    $previousRevenue = getPeriodRevenue(
        $prevStart->format('Y-m-d H:i:s'),
        $prevEnd->format('Y-m-d H:i:s')
    );
    
    if ($previousRevenue > 0) {
        return (($currentRevenue - $previousRevenue) / $previousRevenue) * 100;
    }
    
    return 0;
}

/**
 * Get total number of completed transactions
 */
function getTotalTransactions() {
    global $database;
    
    $result = $database->fetch("
        SELECT COUNT(*) as total_transactions
        FROM bookings 
        WHERE status = 'COMPLETED'
    ");
    
    return intval($result['total_transactions']);
}

/**
 * Calculate average order value
 */
function getAverageOrderValue() {
    global $database;
    
    $totalRevenue = getTotalRevenue();
    $totalTransactions = getTotalTransactions();
    
    return $totalTransactions > 0 ? $totalRevenue / $totalTransactions : 0;
}

/**
 * Get top services by revenue
 */
function getTopServicesByRevenue($limit = 5) {
    global $database;

    try {
        $services = $database->fetchAll("
            SELECT
                s.id, s.name, s.description, s.price, s.duration, s.category, s.image, s.is_active, s.created_at, s.updated_at,
                COUNT(b.id) as bookings,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as revenue
            FROM services s
            LEFT JOIN bookings b ON s.id = b.service_id
            GROUP BY s.id, s.name, s.description, s.price, s.duration, s.category, s.image, s.is_active, s.created_at, s.updated_at
            ORDER BY revenue DESC
            LIMIT ?
        ", [$limit]);
    } catch (Exception $e) {
        return [];
    }
    
    return array_map(function($service) {
        return [
            'name' => $service['name'],
            'revenue' => floatval($service['revenue']),
            'bookings' => intval($service['bookings'])
        ];
    }, $services);
}

/**
 * Get monthly revenue data for charts
 */
function getMonthlyRevenueData($months = 12) {
    global $database;
    
    $data = [];
    $now = new DateTime();
    
    for ($i = $months - 1; $i >= 0; $i--) {
        $monthStart = clone $now;
        $monthStart->modify("-$i months")->modify('first day of this month')->setTime(0, 0, 0);
        
        $monthEnd = clone $monthStart;
        $monthEnd->modify('last day of this month')->setTime(23, 59, 59);
        
        $revenue = $database->fetch("
            SELECT COALESCE(SUM(total_amount), 0) as revenue
            FROM bookings 
            WHERE status = 'COMPLETED' 
            AND date BETWEEN ? AND ?
        ", [$monthStart->format('Y-m-d H:i:s'), $monthEnd->format('Y-m-d H:i:s')]);
        
        $bookings = $database->fetch("
            SELECT COUNT(*) as bookings
            FROM bookings 
            WHERE date BETWEEN ? AND ?
        ", [$monthStart->format('Y-m-d H:i:s'), $monthEnd->format('Y-m-d H:i:s')]);
        
        $data[] = [
            'month' => $monthStart->format('M Y'),
            'revenue' => floatval($revenue['revenue']),
            'bookings' => intval($bookings['bookings'])
        ];
    }
    
    return $data;
}

/**
 * Get recent transactions
 */
function getRecentTransactions($limit = 10) {
    global $database;
    
    $transactions = $database->fetchAll("
        SELECT 
            b.id,
            b.date,
            b.total_amount as amount,
            b.status,
            u.name as customer_name,
            s.name as service_name,
            'Card' as payment_method
        FROM bookings b
        LEFT JOIN users u ON b.user_id = u.id
        LEFT JOIN services s ON b.service_id = s.id
        ORDER BY b.created_at DESC
        LIMIT ?
    ", [$limit]);
    
    return array_map(function($transaction) {
        return [
            'id' => $transaction['id'],
            'date' => $transaction['date'],
            'customerName' => $transaction['customer_name'] ?? 'Unknown',
            'serviceName' => $transaction['service_name'] ?? 'Unknown Service',
            'amount' => floatval($transaction['amount']),
            'paymentMethod' => $transaction['payment_method'],
            'status' => $transaction['status']
        ];
    }, $transactions);
}

/**
 * Get payment method breakdown
 */
function getPaymentMethodBreakdown() {
    global $database;
    
    // For now, we'll simulate payment methods since we don't have a payments table
    // In a real implementation, this would query the payments table
    $totalRevenue = getTotalRevenue();
    
    return [
        [
            'method' => 'Credit Card',
            'amount' => $totalRevenue * 0.7,
            'percentage' => 70,
            'transactions' => getTotalTransactions() * 0.7
        ],
        [
            'method' => 'Cash',
            'amount' => $totalRevenue * 0.2,
            'percentage' => 20,
            'transactions' => getTotalTransactions() * 0.2
        ],
        [
            'method' => 'Digital Wallet',
            'amount' => $totalRevenue * 0.1,
            'percentage' => 10,
            'transactions' => getTotalTransactions() * 0.1
        ]
    ];
}

/**
 * Get all staff earnings for period
 */
function getAllStaffEarnings($startDate, $endDate) {
    global $database;

    try {
        $earnings = $database->fetchAll("
            SELECT
                u.name as staff_name,
                COUNT(b.id) as total_bookings,
                COUNT(CASE WHEN b.status = 'COMPLETED' THEN 1 END) as completed_bookings,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_revenue,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount * 0.3 ELSE 0 END), 0) as staff_commission
            FROM users u
            LEFT JOIN bookings b ON u.id = b.staff_id AND b.date BETWEEN ? AND ?
            WHERE u.role = 'STAFF'
            GROUP BY u.id, u.name
            ORDER BY total_revenue DESC
        ", [$startDate, $endDate]);
    } catch (Exception $e) {
        return [];
    }
    
    return array_map(function($earning) {
        return [
            'staffName' => $earning['staff_name'],
            'totalBookings' => intval($earning['total_bookings']),
            'completedBookings' => intval($earning['completed_bookings']),
            'totalRevenue' => floatval($earning['total_revenue']),
            'staffCommission' => floatval($earning['staff_commission'])
        ];
    }, $earnings);
}

/**
 * Get earnings statistics summary
 */
function getEarningsStatistics() {
    global $database;
    
    $stats = [];
    
    // Revenue metrics
    $stats['total_revenue'] = getTotalRevenue();
    $stats['monthly_revenue'] = getPeriodRevenue(
        date('Y-m-01 00:00:00'),
        date('Y-m-t 23:59:59')
    );
    $stats['daily_revenue'] = getDailyRevenue();
    
    // Transaction metrics
    $stats['total_transactions'] = getTotalTransactions();
    $stats['average_order_value'] = getAverageOrderValue();
    
    // Growth metrics
    $stats['monthly_growth'] = getRevenueGrowth(
        date('Y-m-01 00:00:00'),
        date('Y-m-t 23:59:59')
    );
    
    return $stats;
}

/**
 * Get revenue by date range with custom filters
 */
function getCustomRevenueReport($startDate, $endDate, $filters = []) {
    global $database;
    
    $conditions = ["b.status = 'COMPLETED'"];
    $params = [];
    
    if ($startDate && $endDate) {
        $conditions[] = "b.date BETWEEN ? AND ?";
        $params[] = $startDate;
        $params[] = $endDate;
    }
    
    if (!empty($filters['service_id'])) {
        $conditions[] = "b.service_id = ?";
        $params[] = $filters['service_id'];
    }
    
    if (!empty($filters['staff_id'])) {
        $conditions[] = "b.staff_id = ?";
        $params[] = $filters['staff_id'];
    }
    
    $whereClause = implode(' AND ', $conditions);
    
    $result = $database->fetch("
        SELECT 
            COUNT(*) as total_bookings,
            COALESCE(SUM(total_amount), 0) as total_revenue,
            COALESCE(AVG(total_amount), 0) as average_order_value
        FROM bookings b
        WHERE $whereClause
    ", $params);
    
    return [
        'totalBookings' => intval($result['total_bookings']),
        'totalRevenue' => floatval($result['total_revenue']),
        'averageOrderValue' => floatval($result['average_order_value'])
    ];
}
