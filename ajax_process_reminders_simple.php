<?php
/**
 * Simple AJAX Reminder Processing Endpoint
 * Fallback version with basic functionality and comprehensive error handling
 */

// Set execution time limit
set_time_limit(30);

// Set JSON content type
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => [
        'processed' => 0,
        'sent' => 0,
        'failed' => 0,
        'skipped' => 0,
        'processing_time' => 0
    ],
    'timestamp' => date('Y-m-d H:i:s'),
    'debug' => []
];

try {
    $startTime = microtime(true);
    
    // Basic validation
    $response['debug'][] = 'Starting simple reminder processing...';
    
    // Try to include config
    if (file_exists('config/app.php')) {
        require_once 'config/app.php';
        $response['debug'][] = 'Config loaded successfully';
    } else {
        throw new Exception('Config file not found');
    }
    
    // Check database connection
    if (!isset($database)) {
        throw new Exception('Database connection not available');
    }
    
    // Test database connection
    $testQuery = $database->fetch("SELECT 1 as test");
    if (!$testQuery) {
        throw new Exception('Database connection test failed');
    }
    $response['debug'][] = 'Database connection verified';
    
    // Check if reminder tables exist
    $reminderTableExists = $database->fetch("SHOW TABLES LIKE 'booking_reminders'");
    if (!$reminderTableExists) {
        $response['debug'][] = 'Reminder tables do not exist, creating...';
        
        // Create basic reminder table
        $database->query("
            CREATE TABLE IF NOT EXISTS booking_reminders (
                id VARCHAR(36) PRIMARY KEY,
                booking_id VARCHAR(36) NOT NULL,
                reminder_type ENUM('24_HOURS', '30_MINUTES', 'AT_TIME') NOT NULL,
                priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
                status ENUM('PENDING', 'SENT', 'FAILED', 'SKIPPED') NOT NULL DEFAULT 'PENDING',
                scheduled_time DATETIME NOT NULL,
                sent_time DATETIME NULL,
                attempts INT DEFAULT 0,
                max_attempts INT DEFAULT 3,
                customer_email_sent BOOLEAN DEFAULT FALSE,
                staff_email_sent BOOLEAN DEFAULT FALSE,
                error_message TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        $response['debug'][] = 'Reminder table created';
    }
    
    // Simple reminder processing
    $processed = 0;
    $sent = 0;
    $failed = 0;
    
    // Get pending reminders
    $pendingReminders = $database->fetchAll(
        "SELECT br.*, b.date, b.start_time, b.status as booking_status,
                u.email as customer_email, u.name as customer_name,
                s.name as service_name
         FROM booking_reminders br
         JOIN bookings b ON br.booking_id = b.id
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         WHERE br.status = 'PENDING' 
         AND br.scheduled_time <= NOW()
         AND br.attempts < br.max_attempts
         AND b.status IN ('CONFIRMED', 'PENDING')
         ORDER BY br.priority DESC, br.scheduled_time ASC
         LIMIT 10"
    );
    
    $response['debug'][] = 'Found ' . count($pendingReminders) . ' pending reminders';
    
    foreach ($pendingReminders as $reminder) {
        $processed++;
        
        try {
            // Simple email sending
            $emailSent = false;
            
            if (function_exists('sendBookingReminderEmail')) {
                $emailSent = sendBookingReminderEmail($reminder['booking_id'], $reminder['reminder_type']);
            } elseif (function_exists('sendSMTPEmail') && $reminder['customer_email']) {
                // Fallback email sending
                $subject = 'Booking Reminder - ' . APP_NAME;
                $body = "Dear {$reminder['customer_name']},\n\nThis is a reminder for your upcoming appointment:\n\nService: {$reminder['service_name']}\nDate: {$reminder['date']}\nTime: {$reminder['start_time']}\n\nThank you!";
                $emailSent = sendSMTPEmail($reminder['customer_email'], $subject, $body);
            }
            
            if ($emailSent) {
                // Mark as sent
                $database->query(
                    "UPDATE booking_reminders 
                     SET status = 'SENT', sent_time = NOW(), customer_email_sent = 1, updated_at = NOW()
                     WHERE id = ?",
                    [$reminder['id']]
                );
                $sent++;
                $response['debug'][] = "Reminder sent for booking {$reminder['booking_id']}";
            } else {
                // Mark as failed
                $database->query(
                    "UPDATE booking_reminders 
                     SET status = 'FAILED', attempts = attempts + 1, error_message = 'Email sending failed', updated_at = NOW()
                     WHERE id = ?",
                    [$reminder['id']]
                );
                $failed++;
                $response['debug'][] = "Failed to send reminder for booking {$reminder['booking_id']}";
            }
            
        } catch (Exception $e) {
            $failed++;
            $response['debug'][] = "Error processing reminder {$reminder['id']}: " . $e->getMessage();
            
            // Update failure count
            $database->query(
                "UPDATE booking_reminders 
                 SET attempts = attempts + 1, error_message = ?, updated_at = NOW()
                 WHERE id = ?",
                [$e->getMessage(), $reminder['id']]
            );
        }
    }
    
    // Calculate processing time
    $endTime = microtime(true);
    $processingTime = round(($endTime - $startTime) * 1000, 2);
    
    // Update response
    $response['data']['processed'] = $processed;
    $response['data']['sent'] = $sent;
    $response['data']['failed'] = $failed;
    $response['data']['processing_time'] = $processingTime;
    
    $response['success'] = true;
    $response['message'] = "Processed $processed reminders. Sent: $sent, Failed: $failed";
    
    // Log success
    error_log("Simple AJAX reminder processing completed: Processed=$processed, Sent=$sent, Failed=$failed");
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    $response['debug'][] = 'Fatal error: ' . $e->getMessage();
    
    // Log error
    error_log("Simple AJAX reminder processing error: " . $e->getMessage());
}

// Output JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
