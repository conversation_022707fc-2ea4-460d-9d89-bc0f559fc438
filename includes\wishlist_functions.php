<?php
/**
 * Wishlist Functions
 * Handles wishlist operations for services and packages
 */

require_once __DIR__ . '/../config/database.php';

/**
 * Add item to wishlist
 */
function addToWishlist($userId, $itemType, $itemId) {
    global $database;
    
    try {
        // Check if item already exists in wishlist
        $existing = $database->fetch(
            "SELECT id FROM wishlists WHERE user_id = ? AND item_type = ? AND item_id = ?",
            [$userId, $itemType, $itemId]
        );
        
        if ($existing) {
            return ['success' => false, 'message' => 'Item already in wishlist'];
        }
        
        // Add to wishlist
        $wishlistId = generateUUID();
        $database->query(
            "INSERT INTO wishlists (id, user_id, item_type, item_id) VALUES (?, ?, ?, ?)",
            [$wishlistId, $userId, $itemType, $itemId]
        );
        
        return ['success' => true, 'message' => 'Added to wishlist'];
    } catch (Exception $e) {
        error_log("Error adding to wishlist: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to add to wishlist'];
    }
}

/**
 * Remove item from wishlist
 */
function removeFromWishlist($userId, $itemType, $itemId) {
    global $database;
    
    try {
        $database->query(
            "DELETE FROM wishlists WHERE user_id = ? AND item_type = ? AND item_id = ?",
            [$userId, $itemType, $itemId]
        );
        
        return ['success' => true, 'message' => 'Removed from wishlist'];
    } catch (Exception $e) {
        error_log("Error removing from wishlist: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to remove from wishlist'];
    }
}

/**
 * Check if item is in wishlist
 */
function isInWishlist($userId, $itemType, $itemId) {
    global $database;
    
    if (!$userId) return false;
    
    $result = $database->fetch(
        "SELECT id FROM wishlists WHERE user_id = ? AND item_type = ? AND item_id = ?",
        [$userId, $itemType, $itemId]
    );
    
    return $result !== false;
}

/**
 * Get user's wishlist items
 */
function getUserWishlist($userId, $limit = null, $offset = 0) {
    global $database;

    $limitClause = $limit ? "LIMIT $limit OFFSET $offset" : "";

    $query = "
        SELECT
            w.*,
            CASE
                WHEN w.item_type = 'service' THEN s.name
                WHEN w.item_type = 'package' THEN p.name
            END as item_name,
            CASE
                WHEN w.item_type = 'service' THEN s.description
                WHEN w.item_type = 'package' THEN p.description
            END as item_description,
            CASE
                WHEN w.item_type = 'service' THEN s.price
                WHEN w.item_type = 'package' THEN p.price
            END as item_price,
            CASE
                WHEN w.item_type = 'service' THEN s.duration
                WHEN w.item_type = 'package' THEN NULL
            END as item_duration,
            CASE
                WHEN w.item_type = 'service' THEN s.image
                WHEN w.item_type = 'package' THEN p.image
            END as item_image,
            CASE
                WHEN w.item_type = 'service' THEN s.category
                WHEN w.item_type = 'package' THEN 'Package'
            END as item_category
        FROM wishlists w
        LEFT JOIN services s ON w.item_type = 'service' AND w.item_id = s.id
        LEFT JOIN packages p ON w.item_type = 'package' AND w.item_id = p.id
        WHERE w.user_id = ?
        ORDER BY w.created_at DESC
        $limitClause
    ";

    $items = $database->fetchAll($query, [$userId]);

    // Calculate duration for packages by summing their services
    foreach ($items as $index => $item) {
        if ($item['item_type'] === 'package' && $item['item_duration'] === null) {
            $packageDuration = calculatePackageDuration($item['item_id']);
            $items[$index]['item_duration'] = $packageDuration;
        }
    }

    return $items;
}

/**
 * Calculate total duration for a package by summing its services
 */
function calculatePackageDuration($packageId) {
    global $database;

    $result = $database->fetch(
        "SELECT COALESCE(SUM(s.duration), 0) as total_duration
         FROM package_services ps
         INNER JOIN services s ON ps.service_id = s.id
         WHERE ps.package_id = ?",
        [$packageId]
    );

    return $result ? intval($result['total_duration']) : 0;
}

/**
 * Get wishlist count for user
 */
function getWishlistCount($userId) {
    global $database;
    
    if (!$userId) return 0;
    
    $result = $database->fetch(
        "SELECT COUNT(*) as count FROM wishlists WHERE user_id = ?",
        [$userId]
    );
    
    return $result ? intval($result['count']) : 0;
}

/**
 * Add item to session wishlist (for guests)
 */
function addToSessionWishlist($itemType, $itemId) {
    if (!isset($_SESSION['wishlist'])) {
        $_SESSION['wishlist'] = [];
    }
    
    $key = $itemType . '_' . $itemId;
    if (!in_array($key, $_SESSION['wishlist'])) {
        $_SESSION['wishlist'][] = $key;
        return ['success' => true, 'message' => 'Added to wishlist'];
    }
    
    return ['success' => false, 'message' => 'Item already in wishlist'];
}

/**
 * Remove item from session wishlist (for guests)
 */
function removeFromSessionWishlist($itemType, $itemId) {
    if (!isset($_SESSION['wishlist'])) {
        $_SESSION['wishlist'] = [];
    }
    
    $key = $itemType . '_' . $itemId;
    $index = array_search($key, $_SESSION['wishlist']);
    
    if ($index !== false) {
        unset($_SESSION['wishlist'][$index]);
        $_SESSION['wishlist'] = array_values($_SESSION['wishlist']); // Reindex array
        return ['success' => true, 'message' => 'Removed from wishlist'];
    }
    
    return ['success' => false, 'message' => 'Item not in wishlist'];
}

/**
 * Check if item is in session wishlist (for guests)
 */
function isInSessionWishlist($itemType, $itemId) {
    if (!isset($_SESSION['wishlist'])) {
        return false;
    }
    
    $key = $itemType . '_' . $itemId;
    return in_array($key, $_SESSION['wishlist']);
}

/**
 * Get session wishlist items (for guests)
 */
function getSessionWishlist() {
    global $database;

    if (!isset($_SESSION['wishlist']) || empty($_SESSION['wishlist'])) {
        return [];
    }

    $items = [];
    foreach ($_SESSION['wishlist'] as $key) {
        list($itemType, $itemId) = explode('_', $key, 2);

        if ($itemType === 'service') {
            $item = $database->fetch("SELECT *, 'service' as item_type FROM services WHERE id = ?", [$itemId]);
        } elseif ($itemType === 'package') {
            $item = $database->fetch("SELECT *, 'package' as item_type FROM packages WHERE id = ?", [$itemId]);
        }

        if ($item) {
            $item['item_name'] = $item['name'];
            $item['item_description'] = $item['description'];
            $item['item_price'] = $item['price'];

            // Handle duration - packages don't have duration column
            if ($itemType === 'service') {
                $item['item_duration'] = $item['duration'];
            } else {
                $item['item_duration'] = calculatePackageDuration($itemId);
            }

            $item['item_image'] = $item['image'];
            $item['item_category'] = $itemType === 'service' ? $item['category'] : 'Package';
            $item['created_at'] = date('Y-m-d H:i:s');
            $items[] = $item;
        }
    }

    return $items;
}

/**
 * Transfer session wishlist to database (when user logs in)
 */
function transferSessionWishlistToDatabase($userId) {
    global $database;
    
    if (!isset($_SESSION['wishlist']) || empty($_SESSION['wishlist'])) {
        return;
    }
    
    foreach ($_SESSION['wishlist'] as $key) {
        list($itemType, $itemId) = explode('_', $key, 2);
        
        // Check if item already exists in database wishlist
        $existing = $database->fetch(
            "SELECT id FROM wishlists WHERE user_id = ? AND item_type = ? AND item_id = ?",
            [$userId, $itemType, $itemId]
        );
        
        if (!$existing) {
            // Add to database wishlist
            $wishlistId = generateUUID();
            $database->query(
                "INSERT INTO wishlists (id, user_id, item_type, item_id) VALUES (?, ?, ?, ?)",
                [$wishlistId, $userId, $itemType, $itemId]
            );
        }
    }
    
    // Clear session wishlist
    unset($_SESSION['wishlist']);
}

/**
 * Get wishlist count for session (guests)
 */
function getSessionWishlistCount() {
    if (!isset($_SESSION['wishlist'])) {
        return 0;
    }
    
    return count($_SESSION['wishlist']);
}

/**
 * Clear all wishlist items for user
 */
function clearUserWishlist($userId) {
    global $database;
    
    try {
        $database->query("DELETE FROM wishlists WHERE user_id = ?", [$userId]);
        return ['success' => true, 'message' => 'Wishlist cleared'];
    } catch (Exception $e) {
        error_log("Error clearing wishlist: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to clear wishlist'];
    }
}
