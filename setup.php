<?php
/**
 * Database Setup Script
 * Flix Salonce - PHP Version
 */

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Flix Salonce - Database Setup</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <style>
        body { background: #0f172a; color: #ffffff; font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class='min-h-screen bg-gray-900 text-white'>
    <div class='max-w-4xl mx-auto py-12 px-6'>
        <div class='text-center mb-8'>
            <h1 class='text-4xl font-bold text-yellow-400 mb-2'>Flix Salonce</h1>
            <h2 class='text-2xl font-semibold mb-4'>Database Setup</h2>
        </div>
        
        <div class='bg-gray-800 rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-semibold mb-4'>Setup Progress</h3>
            <div class='space-y-2'>";

try {
    // Step 1: Test database connection
    echo "<div class='flex items-center space-x-2'>
            <div class='w-4 h-4 bg-blue-500 rounded-full animate-pulse'></div>
            <span>Testing database connection...</span>
          </div>";
    
    require_once __DIR__ . '/config/database.php';
    
    echo "<div class='flex items-center space-x-2'>
            <div class='w-4 h-4 bg-green-500 rounded-full'></div>
            <span>✓ Database connection successful</span>
          </div>";
    
    // Step 2: Run migrations
    echo "<div class='flex items-center space-x-2'>
            <div class='w-4 h-4 bg-blue-500 rounded-full animate-pulse'></div>
            <span>Running database migrations...</span>
          </div>";

    $migrationSql = file_get_contents(__DIR__ . '/database/migrations.sql');
    $statements = explode(';', $migrationSql);

    $createdTables = 0;
    $existingTables = 0;

    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && stripos($statement, 'CREATE TABLE') !== false) {
            try {
                $database->query($statement);
                $createdTables++;
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    $existingTables++;
                } else {
                    throw $e; // Re-throw if it's not a "table exists" error
                }
            }
        } elseif (!empty($statement)) {
            // Execute non-CREATE TABLE statements (like CREATE DATABASE, USE, CREATE INDEX)
            try {
                $database->query($statement);
            } catch (Exception $e) {
                // Silently ignore errors for indexes and other non-critical statements
                if (strpos($e->getMessage(), 'Duplicate key name') === false &&
                    strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
    }

    if ($createdTables > 0) {
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-green-500 rounded-full'></div>
                <span>✓ Created {$createdTables} new database tables</span>
              </div>";
    }

    if ($existingTables > 0) {
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-yellow-500 rounded-full'></div>
                <span>⚠ {$existingTables} tables already existed (skipped)</span>
              </div>";
    }

    echo "<div class='flex items-center space-x-2'>
            <div class='w-4 h-4 bg-green-500 rounded-full'></div>
            <span>✓ Database schema setup completed</span>
          </div>";
    
    // Step 3: Check if data already exists
    $userCount = $database->fetch("SELECT COUNT(*) as count FROM users")['count'];
    
    if ($userCount == 0) {
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-blue-500 rounded-full animate-pulse'></div>
                <span>Seeding database with sample data...</span>
              </div>";
        
        // Include and run seeder
        ob_start();
        include __DIR__ . '/database/seed.php';
        $seedOutput = ob_get_clean();
        
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-green-500 rounded-full'></div>
                <span>✓ Sample data created successfully</span>
              </div>";
    } else {
        echo "<div class='flex items-center space-x-2'>
                <div class='w-4 h-4 bg-yellow-500 rounded-full'></div>
                <span>⚠ Database already contains data - skipping seeding</span>
              </div>";
    }
    
    echo "<div class='flex items-center space-x-2'>
            <div class='w-4 h-4 bg-green-500 rounded-full'></div>
            <span>✓ Setup completed successfully!</span>
          </div>";
    
    echo "</div>
        </div>
        
        <div class='bg-green-900 border border-green-700 rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-semibold text-green-400 mb-4'>Setup Complete!</h3>
            <p class='mb-4'>Your Flix Salonce application has been set up successfully.</p>
            
            <div class='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>
                <div class='bg-gray-800 p-4 rounded-lg'>
                    <h4 class='font-semibold text-yellow-400 mb-2'>Demo Accounts</h4>
                    <div class='space-y-2 text-sm'>
                        <div><strong>Admin:</strong> <EMAIL> / admin123</div>
                        <div><strong>Staff:</strong> <EMAIL> / staff123</div>
                        <div><strong>Customer:</strong> <EMAIL> / customer123</div>
                    </div>
                </div>
                
                <div class='bg-gray-800 p-4 rounded-lg'>
                    <h4 class='font-semibold text-yellow-400 mb-2'>Quick Links</h4>
                    <div class='space-y-2 text-sm'>
                        <div><a href='/' class='text-blue-400 hover:text-blue-300'>🏠 Home Page</a></div>
                        <div><a href='/admin' class='text-blue-400 hover:text-blue-300'>⚙️ Admin Panel</a></div>
                        <div><a href='/auth/login.php' class='text-blue-400 hover:text-blue-300'>🔐 Login</a></div>
                    </div>
                </div>
            </div>
            
            <div class='bg-yellow-900 border border-yellow-700 rounded-lg p-4 mb-4'>
                <h4 class='font-semibold text-yellow-400 mb-2'>⚠️ Security Notice</h4>
                <p class='text-sm'>For security reasons, please delete or rename this setup.php file after setup is complete.</p>
            </div>
            
            <div class='flex space-x-4'>
                <a href='/' class='bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-2 rounded-lg font-semibold transition-colors'>
                    Visit Website
                </a>
                <a href='/admin' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                    Admin Panel
                </a>
            </div>
        </div>";
    
} catch (Exception $e) {
    echo "<div class='flex items-center space-x-2'>
            <div class='w-4 h-4 bg-red-500 rounded-full'></div>
            <span>✗ Error: " . htmlspecialchars($e->getMessage()) . "</span>
          </div>";
    
    echo "</div>
        </div>
        
        <div class='bg-red-900 border border-red-700 rounded-lg p-6'>
            <h3 class='text-xl font-semibold text-red-400 mb-4'>Setup Failed</h3>
            <p class='mb-4'>There was an error setting up the database:</p>
            <div class='bg-gray-800 p-4 rounded-lg'>
                <code class='text-red-400'>" . htmlspecialchars($e->getMessage()) . "</code>
            </div>
            
            <div class='mt-6'>
                <h4 class='font-semibold mb-2'>Troubleshooting:</h4>
                <ul class='list-disc list-inside space-y-1 text-sm'>
                    <li>Make sure MySQL is running</li>
                    <li>Check database credentials in config/database.php</li>
                    <li>Ensure the database 'flix_salonce' exists</li>
                    <li>Verify user has proper permissions</li>
                </ul>
            </div>
            
            <div class='mt-4'>
                <button onclick='window.location.reload()' class='bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-semibold transition-colors'>
                    Retry Setup
                </button>
            </div>
        </div>";
}

echo "
        <div class='mt-8 text-center text-gray-400 text-sm'>
            <p>Flix Salonce - PHP Version | Database Setup Script</p>
        </div>
    </div>
</body>
</html>";
?>
