<?php
/**
 * Add Sample Notifications Script
 * Run this script to populate the database with sample notifications
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/notification_triggers.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('Access denied. Admin authentication required.');
}

// Get admin user ID
$adminUserId = $_SESSION['user_id'];

echo "<h1>Adding Sample Notifications...</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }</style>";

// Sample notifications data
$sampleNotifications = [
    // URGENT Priority Notifications
    [
        'title' => 'URGENT: System Security Alert',
        'message' => 'Suspicious login attempts detected. Please review security logs immediately.',
        'type' => 'SYSTEM_MAINTENANCE',
        'priority' => 'URGENT',
        'action_url' => '/admin/settings'
    ],
    [
        'title' => 'Customer Complaint - Immediate Action Required',
        'message' => 'Customer Sarah <PERSON> reported serious service quality issues. Requires immediate management attention.',
        'type' => 'COMPLAINT_NEW',
        'priority' => 'URGENT',
        'action_url' => '/admin/customers'
    ],
    [
        'title' => 'Payment System Down',
        'message' => 'Payment processing system is currently unavailable. Multiple transactions failing.',
        'type' => 'PAYMENT_FAILED',
        'priority' => 'URGENT',
        'action_url' => '/admin/earnings'
    ],

    // HIGH Priority Notifications
    [
        'title' => 'New VIP Booking - Premium Package',
        'message' => 'Emma Davis booked the Luxury Spa Package (TSH 150,000) for tomorrow at 10:00 AM.',
        'type' => 'BOOKING_NEW',
        'priority' => 'HIGH',
        'action_url' => '/admin/bookings'
    ],
    [
        'title' => 'Staff Leave Request - Holiday Season',
        'message' => 'Jessica Martinez requested leave Dec 24-26. Need to arrange coverage for holiday bookings.',
        'type' => 'STAFF_LEAVE_REQUEST',
        'priority' => 'HIGH',
        'action_url' => '/admin/staff'
    ],
    [
        'title' => 'Booking Cancellation - Last Minute',
        'message' => 'Mike Wilson cancelled his 2:00 PM appointment (Hair Styling + Beard Trim) - 30 minutes notice.',
        'type' => 'BOOKING_CANCELLED',
        'priority' => 'HIGH',
        'action_url' => '/admin/bookings'
    ],
    [
        'title' => 'Appointment Reminder - 30 Minutes',
        'message' => 'Lisa Garcia has a Facial Treatment appointment in 30 minutes. Staff: Alex Thompson.',
        'type' => 'BOOKING_REMINDER',
        'priority' => 'HIGH',
        'action_url' => '/admin/bookings'
    ],

    // MEDIUM Priority Notifications
    [
        'title' => 'New Customer Registration',
        'message' => 'Maria Rodriguez (<EMAIL>) has registered. Welcome bonus points awarded.',
        'type' => 'CUSTOMER_NEW',
        'priority' => 'MEDIUM',
        'action_url' => '/admin/customers'
    ],
    [
        'title' => 'Booking Confirmed',
        'message' => 'David Brown confirmed his Hair Cut appointment for Dec 18, 2024 at 3:30 PM.',
        'type' => 'BOOKING_CONFIRMED',
        'priority' => 'MEDIUM',
        'action_url' => '/admin/bookings'
    ],
    [
        'title' => 'Staff Schedule Updated',
        'message' => 'Alex Thompson updated availability - added evening slots for Dec 20-22.',
        'type' => 'STAFF_SCHEDULE_CHANGE',
        'priority' => 'MEDIUM',
        'action_url' => '/admin/staff'
    ],
    [
        'title' => 'Holiday Promotion Launch',
        'message' => 'Christmas Special promotion is now live: 25% off all spa packages until Dec 31st.',
        'type' => 'PROMOTION_NEW',
        'priority' => 'MEDIUM',
        'action_url' => '/admin/offers'
    ],
    [
        'title' => 'Refund Processed',
        'message' => 'Refund of TSH 45,000 processed for Jennifer Smith (cancelled Bridal Package).',
        'type' => 'REFUND_PROCESSED',
        'priority' => 'MEDIUM',
        'action_url' => '/admin/earnings'
    ],

    // LOW Priority Notifications
    [
        'title' => 'Customer Birthday Today',
        'message' => 'Today is Amanda Wilson\'s birthday! Consider sending a special birthday offer.',
        'type' => 'CUSTOMER_BIRTHDAY',
        'priority' => 'LOW',
        'action_url' => '/admin/customers'
    ],
    [
        'title' => 'Payment Successful',
        'message' => 'Payment of TSH 85,000 processed successfully for booking #BK2024001.',
        'type' => 'PAYMENT_SUCCESS',
        'priority' => 'LOW',
        'action_url' => '/admin/earnings'
    ],
    [
        'title' => 'New 5-Star Review',
        'message' => 'Excellent service! The facial was amazing and the staff was very professional. Highly recommend!',
        'type' => 'REVIEW_NEW',
        'priority' => 'LOW',
        'action_url' => '/admin/customers'
    ],
    [
        'title' => 'Newsletter Subscription',
        'message' => 'New newsletter <NAME_EMAIL>.',
        'type' => 'NEWSLETTER_SUBSCRIBER',
        'priority' => 'LOW',
        'action_url' => '/admin/customers'
    ],
    [
        'title' => 'Daily Backup Completed',
        'message' => 'Automated daily backup completed successfully at 3:00 AM. All data secured.',
        'type' => 'SYSTEM_BACKUP',
        'priority' => 'LOW',
        'action_url' => '/admin/settings'
    ],
    [
        'title' => 'Booking Completed',
        'message' => 'Robert Johnson completed his Massage Therapy session. Points awarded automatically.',
        'type' => 'BOOKING_COMPLETED',
        'priority' => 'LOW',
        'action_url' => '/admin/bookings'
    ],
    [
        'title' => 'System Update Available',
        'message' => 'New system update (v2.1.5) available with bug fixes and performance improvements.',
        'type' => 'SYSTEM_UPDATE',
        'priority' => 'LOW',
        'action_url' => '/admin/settings'
    ]
];

$created = 0;
$errors = 0;

echo "<h2>Creating Sample Notifications...</h2>";
echo "<div style='background: #2a2a2a; padding: 15px; border-radius: 8px; margin: 10px 0;'>";

foreach ($sampleNotifications as $index => $notif) {
    try {
        $options = [
            'priority' => $notif['priority'],
            'action_url' => getBasePath() . $notif['action_url'],
            'metadata' => json_encode([
                'sample' => true,
                'created_by' => 'sample_script',
                'created_at' => date('Y-m-d H:i:s'),
                'index' => $index + 1
            ])
        ];
        
        $notificationId = createNotification($adminUserId, $notif['title'], $notif['message'], $notif['type'], $options);
        
        if ($notificationId) {
            $created++;
            $priorityColor = [
                'URGENT' => '#ef4444',
                'HIGH' => '#f97316', 
                'MEDIUM' => '#3b82f6',
                'LOW' => '#6b7280'
            ][$notif['priority']];
            
            echo "<div style='margin: 8px 0; padding: 8px; background: #3a3a3a; border-left: 4px solid {$priorityColor}; border-radius: 4px;'>";
            echo "<strong style='color: {$priorityColor};'>[{$notif['priority']}]</strong> ";
            echo "<span style='color: #10b981;'>✓</span> {$notif['title']}";
            echo "</div>";
        } else {
            $errors++;
            echo "<div style='margin: 8px 0; padding: 8px; background: #3a3a3a; border-left: 4px solid #ef4444; border-radius: 4px;'>";
            echo "<span style='color: #ef4444;'>✗</span> Failed to create: {$notif['title']}";
            echo "</div>";
        }
        
        // Small delay to make timestamps slightly different
        usleep(100000); // 0.1 second
        
    } catch (Exception $e) {
        $errors++;
        echo "<div style='margin: 8px 0; padding: 8px; background: #3a3a3a; border-left: 4px solid #ef4444; border-radius: 4px;'>";
        echo "<span style='color: #ef4444;'>✗</span> Error creating {$notif['title']}: " . $e->getMessage();
        echo "</div>";
    }
}

echo "</div>";

echo "<h2>Summary</h2>";
echo "<div style='background: #065f46; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<p><strong>✅ Successfully created:</strong> {$created} notifications</p>";
if ($errors > 0) {
    echo "<p><strong>❌ Errors:</strong> {$errors} notifications failed</p>";
}
echo "<p><strong>📊 Total attempted:</strong> " . count($sampleNotifications) . " notifications</p>";
echo "</div>";

echo "<h2>What's Next?</h2>";
echo "<div style='background: #1e40af; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<p>🎉 Sample notifications have been added! Now you can:</p>";
echo "<ul style='margin: 10px 0; padding-left: 20px;'>";
echo "<li>📱 Check the notification counter in the admin header</li>";
echo "<li>🔔 Click the notification bell to see the dropdown</li>";
echo "<li>📋 Visit <a href='" . getBasePath() . "/admin/notifications' style='color: #fbbf24;'>Notifications Management</a> to see all notifications</li>";
echo "<li>🎯 Test filtering by category, priority, and status</li>";
echo "<li>⚡ Try bulk actions like mark as read/unread</li>";
echo "<li>👁️ Click individual notifications to view details</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='" . getBasePath() . "/admin/notifications' style='background: #d97706; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold;'>View All Notifications →</a>";
echo "</div>";
?>
