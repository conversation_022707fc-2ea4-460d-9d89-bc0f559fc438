# GitHub Repository Setup Guide

This guide will help you create a GitHub repository for your Flix Salon & SPA PHP project.

## Prerequisites

1. **Git installed** on your computer
   - Download from: https://git-scm.com/downloads
   - Verify installation: `git --version`

2. **GitHub account**
   - Create at: https://github.com/signup

## Step 1: Prepare Your Local Repository

### 1.1 Initialize Git Repository
Open terminal/command prompt in your project directory (`c:\xampp\htdocs\flix-php`) and run:

```bash
git init
```

### 1.2 Configure Git (if not done before)
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### 1.3 Set Up Configuration Files
Before committing, you need to set up your configuration files:

1. **Copy configuration templates:**
   ```bash
   copy config\app.php.example config\app.php
   copy config\database.php.example config\database.php
   ```

2. **Edit the configuration files** with your actual settings:
   - Update database credentials in `config/database.php`
   - Update application settings in `config/app.php`
   - **Important:** These files are in `.gitignore` so your sensitive data won't be committed

## Step 2: Create GitHub Repository

### 2.1 Create Repository on GitHub
1. Go to https://github.com/new
2. Fill in repository details:
   - **Repository name:** `flix-salonce-php` (or your preferred name)
   - **Description:** `Complete PHP-based salon management system with luxury design and comprehensive features`
   - **Visibility:** Choose Public or Private
   - **DO NOT** initialize with README, .gitignore, or license (we already have these)

### 2.2 Note the Repository URL
After creation, you'll see a URL like:
- HTTPS: `https://github.com/yourusername/flix-salonce-php.git`
- SSH: `**************:yourusername/flix-salonce-php.git`

## Step 3: Connect Local Repository to GitHub

### 3.1 Add Files to Git
```bash
git add .
git commit -m "Initial commit: Complete Flix Salonce PHP salon management system"
```

### 3.2 Add Remote Repository
Replace `yourusername` and `repository-name` with your actual values:

```bash
git remote add origin https://github.com/yourusername/flix-salonce-php.git
```

### 3.3 Push to GitHub
```bash
git branch -M main
git push -u origin main
```

## Step 4: Verify Upload

1. Go to your GitHub repository URL
2. Verify all files are uploaded
3. Check that sensitive files (like `config/app.php`) are NOT visible
4. Confirm that template files (like `config/app.php.example`) ARE visible

## Step 5: Set Up Repository Settings (Optional)

### 5.1 Add Repository Description
1. Go to your repository on GitHub
2. Click the gear icon next to "About"
3. Add description and topics like: `php`, `salon-management`, `booking-system`, `mysql`

### 5.2 Create Releases
1. Go to "Releases" tab
2. Click "Create a new release"
3. Tag: `v1.0.0`
4. Title: `Flix Salonce PHP v1.0.0`
5. Description: Initial release with complete salon management features

## Step 6: Clone Instructions for Others

Add this to your README.md for others who want to use your project:

```markdown
## Installation from GitHub

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/flix-salonce-php.git
   cd flix-salonce-php
   ```

2. **Set up configuration:**
   ```bash
   cp config/app.php.example config/app.php
   cp config/database.php.example config/database.php
   ```

3. **Edit configuration files** with your database and application settings

4. **Run setup script:**
   Navigate to `http://localhost/flix-salonce-php/setup.php`
```

## Troubleshooting

### Common Issues:

1. **"Permission denied" error:**
   - Set up SSH keys: https://docs.github.com/en/authentication/connecting-to-github-with-ssh

2. **"Repository not found" error:**
   - Check repository URL is correct
   - Ensure you have access to the repository

3. **Large file warnings:**
   - Check if any files are over 100MB
   - Consider using Git LFS for large files

4. **Authentication issues:**
   - Use personal access token instead of password
   - Generate at: https://github.com/settings/tokens

## Security Notes

- ✅ Sensitive configuration files are excluded via `.gitignore`
- ✅ Database credentials are not committed
- ✅ Upload directories are preserved but content is excluded
- ✅ Template files are provided for easy setup

## Next Steps

After setting up the repository:
1. Add collaborators if needed
2. Set up GitHub Actions for CI/CD (optional)
3. Create issues for feature requests or bugs
4. Set up project boards for task management
5. Configure branch protection rules for main branch
