<?php
/**
 * Flutterwave Payment Return Page
 * Handles redirect after Flutterwave payment completion
 */

require_once __DIR__ . '/../../config/app.php';

// Check if user is logged in
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login');
}

$paymentId = $_GET['payment_id'] ?? '';
$status = $_GET['status'] ?? '';
$txRef = $_GET['tx_ref'] ?? '';
$transactionId = $_GET['transaction_id'] ?? '';

// Log the return parameters
error_log("Flutterwave return page accessed with: paymentId={$paymentId}, status={$status}, txRef={$txRef}, transactionId={$transactionId}");

if (empty($paymentId)) {
    redirect('/customer/payments?error=invalid_payment_id');
}

// Get payment details
global $database;
$payment = $database->fetch("
    SELECT p.*, b.user_id, b.id as booking_id,
           s.name as service_name, pkg.name as package_name,
           u.name as customer_name, u.email as customer_email
    FROM payments p
    INNER JOIN bookings b ON p.booking_id = b.id
    LEFT JOIN services s ON b.service_id = s.id
    LEFT JOIN packages pkg ON b.package_id = pkg.id
    LEFT JOIN users u ON b.user_id = u.id
    WHERE p.id = ? AND b.user_id = ? AND p.payment_gateway = 'FLUTTERWAVE'
", [$paymentId, $_SESSION['user_id']]);

if (!$payment) {
    redirect('/customer/payments?error=payment_not_found');
}

$serviceName = $payment['service_name'] ?: $payment['package_name'] ?: 'Unknown Service';
$pageTitle = "Payment Status - " . $serviceName;

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Payment Return Page -->
<div class="min-h-screen bg-secondary-900 py-8">
    <div class="max-w-md mx-auto px-4">
        <!-- Status Header -->
        <div class="bg-secondary-800 rounded-lg p-6 mb-6 text-center">
            <div id="status-icon" class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <!-- Will be updated by JavaScript -->
            </div>
            <h2 id="status-title" class="text-xl font-semibold text-white mb-2">Processing Payment...</h2>
            <p id="status-message" class="text-gray-400 text-sm">Please wait while we verify your payment.</p>
        </div>
        
        <!-- Payment Details -->
        <div class="bg-secondary-800 rounded-lg p-6 mb-6">
            <h3 class="text-white font-medium mb-4">Payment Details</h3>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-400">Service:</span>
                    <span class="text-white"><?= htmlspecialchars($serviceName) ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Amount:</span>
                    <span class="text-white"><?= CURRENCY_SYMBOL ?> <?= number_format($payment['amount']) ?></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">Reference:</span>
                    <span class="text-white"><?= htmlspecialchars($payment['payment_reference']) ?></span>
                </div>
                <?php if ($txRef): ?>
                <div class="flex justify-between">
                    <span class="text-gray-400">Transaction Ref:</span>
                    <span class="text-white"><?= htmlspecialchars($txRef) ?></span>
                </div>
                <?php endif; ?>
                <?php if ($transactionId): ?>
                <div class="flex justify-between">
                    <span class="text-gray-400">Transaction ID:</span>
                    <span class="text-white"><?= htmlspecialchars($transactionId) ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div id="action-buttons" class="text-center">
            <!-- Will be updated by JavaScript -->
        </div>
    </div>
</div>

<script>
// Payment verification status
let verificationComplete = false;

// Update UI based on status
function updateStatus(status, title, message, iconClass, iconColor) {
    const statusIcon = document.getElementById('status-icon');
    const statusTitle = document.getElementById('status-title');
    const statusMessage = document.getElementById('status-message');
    
    statusIcon.className = `w-16 h-16 ${iconColor} rounded-full flex items-center justify-center mx-auto mb-4`;
    statusIcon.innerHTML = `<i class="${iconClass} text-white text-2xl"></i>`;
    statusTitle.textContent = title;
    statusMessage.textContent = message;
}

// Show action buttons
function showActionButtons(buttons) {
    document.getElementById('action-buttons').innerHTML = buttons;
}

// Verify payment with server
async function verifyPayment() {
    try {
        console.log('Starting payment verification...');
        
        const verificationData = {
            payment_id: '<?= $paymentId ?>',
            transaction_id: '<?= $transactionId ?>',
            tx_ref: '<?= $txRef ?>'
        };
        
        console.log('Verification data:', verificationData);
        
        const response = await fetch('<?= getBasePath() ?>/api/payments/flutterwave/verify-simple.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(verificationData)
        });
        
        const result = await response.json();
        console.log('Verification result:', result);
        
        if (result.success) {
            updateStatus('success', 'Payment Successful!', 'Your payment has been verified and processed successfully.', 'fas fa-check', 'bg-green-600');
            showActionButtons(`
                <a href="<?= getBasePath() ?>/customer/payments?success=payment_completed" 
                   class="inline-block bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Payments
                </a>
            `);
        } else {
            updateStatus('error', 'Verification Failed', result.error || 'Payment verification failed. Please contact support.', 'fas fa-times', 'bg-red-600');
            showActionButtons(`
                <div class="space-y-3">
                    <button onclick="verifyPayment()" 
                            class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                        <i class="fas fa-sync mr-2"></i>Try Again
                    </button>
                    <a href="<?= getBasePath() ?>/customer/payments" 
                       class="block w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors text-center">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Payments
                    </a>
                </div>
            `);
        }
        
        verificationComplete = true;
        
    } catch (error) {
        console.error('Verification error:', error);
        updateStatus('error', 'Verification Error', 'An error occurred while verifying your payment. Please try again.', 'fas fa-exclamation-triangle', 'bg-red-600');
        showActionButtons(`
            <div class="space-y-3">
                <button onclick="verifyPayment()" 
                        class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    <i class="fas fa-sync mr-2"></i>Try Again
                </button>
                <a href="<?= getBasePath() ?>/customer/payments" 
                   class="block w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors text-center">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Payments
                </a>
            </div>
        `);
    }
}

// Initialize based on URL parameters
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');
    const txRef = urlParams.get('tx_ref');
    const transactionId = urlParams.get('transaction_id');
    
    console.log('Page loaded with parameters:', { status, txRef, transactionId });
    
    if (status === 'successful' || status === 'completed') {
        // Payment was successful, verify it
        setTimeout(verifyPayment, 1000);
    } else if (status === 'cancelled') {
        updateStatus('cancelled', 'Payment Cancelled', 'You cancelled the payment. You can try again when ready.', 'fas fa-times-circle', 'bg-yellow-600');
        showActionButtons(`
            <a href="<?= getBasePath() ?>/customer/payments" 
               class="inline-block bg-salon-gold hover:bg-yellow-500 text-black font-semibold py-3 px-6 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Payments
            </a>
        `);
    } else if (txRef || transactionId) {
        // We have transaction details, try to verify
        setTimeout(verifyPayment, 1000);
    } else {
        // No clear status, show manual verification option
        updateStatus('pending', 'Payment Status Unknown', 'We could not determine your payment status. Please verify manually.', 'fas fa-question-circle', 'bg-blue-600');
        showActionButtons(`
            <div class="space-y-3">
                <button onclick="verifyPayment()" 
                        class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>Check Payment Status
                </button>
                <a href="<?= getBasePath() ?>/customer/payments" 
                   class="block w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors text-center">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Payments
                </a>
            </div>
        `);
    }
});
</script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
