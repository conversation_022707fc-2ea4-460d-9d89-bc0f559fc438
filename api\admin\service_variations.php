<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';

// Check authentication and admin role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get specific variation
                $variation = getServiceVariationById($_GET['id']);
                if ($variation) {
                    echo json_encode(['success' => true, 'variation' => $variation]);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Service variation not found']);
                }
            } elseif (isset($_GET['service_id'])) {
                // Get variations for a specific service
                $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
                $variations = getServiceVariations($_GET['service_id'], $activeOnly);
                echo json_encode(['success' => true, 'variations' => $variations]);
            } elseif (isset($_GET['stats'])) {
                // Get variation statistics
                $serviceId = $_GET['service_id'] ?? null;
                $stats = getVariationStats($serviceId);
                echo json_encode(['success' => true, 'stats' => $stats]);
            } else {
                // Get all services with variations
                $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
                $services = getServicesWithVariations($activeOnly);
                echo json_encode(['success' => true, 'services' => $services]);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input']);
                exit;
            }
            
            if (empty($input['service_id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Service ID is required']);
                exit;
            }
            
            $result = createServiceVariation($input['service_id'], $input);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'PUT':
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Variation ID is required']);
                exit;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON input']);
                exit;
            }
            
            if (isset($input['sort_order'])) {
                // Update sort order
                $result = updateVariationSortOrder($_GET['id'], $input['sort_order']);
            } else {
                // Update variation data
                $result = updateServiceVariation($_GET['id'], $input);
            }
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'PATCH':
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Variation ID is required']);
                exit;
            }
            
            // Toggle active status
            $result = toggleServiceVariationStatus($_GET['id']);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        case 'DELETE':
            if (!isset($_GET['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Variation ID is required']);
                exit;
            }
            
            $result = deleteServiceVariation($_GET['id']);
            
            if ($result['success']) {
                echo json_encode($result);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    error_log("Service variations API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
