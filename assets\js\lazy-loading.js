/**
 * Universal Lazy Loading Utility
 * Flix Salon & SPA - PHP Version
 * 
 * This utility provides lazy loading functionality for images across all pages
 * with intersection observer support and graceful fallbacks.
 */

class UniversalLazyLoader {
    constructor(options = {}) {
        this.options = {
            rootMargin: '50px 0px',
            threshold: 0.01,
            placeholderClass: 'lazy-placeholder',
            imageClass: 'lazy-image',
            loadedClass: 'lazy-loaded',
            fadeTransition: true,
            ...options
        };
        
        this.imageObserver = null;
        this.init();
    }

    init() {
        // Add required styles
        this.addStyles();
        
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.setupIntersectionObserver();
            this.observeImages();
        } else {
            // Fallback for browsers without Intersection Observer
            this.loadAllImages();
        }
    }

    setupIntersectionObserver() {
        this.imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: this.options.rootMargin,
            threshold: this.options.threshold
        });
    }

    observeImages() {
        const lazyImages = document.querySelectorAll(`.${this.options.imageClass}`);
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        const placeholder = img.previousElementSibling;
        
        // Create a new image to preload
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // Image loaded successfully
            img.src = img.dataset.src;
            
            if (this.options.fadeTransition) {
                img.classList.remove('opacity-0');
                img.classList.add('opacity-100');
            }
            
            // Hide placeholder with fade effect
            if (placeholder && placeholder.classList.contains(this.options.placeholderClass)) {
                if (this.options.fadeTransition) {
                    placeholder.style.transition = 'opacity 0.3s ease-out';
                    placeholder.style.opacity = '0';
                    setTimeout(() => {
                        placeholder.style.display = 'none';
                    }, 300);
                } else {
                    placeholder.style.display = 'none';
                }
            }
            
            // Add loaded class for any additional styling
            img.classList.add(this.options.loadedClass);
            
            // Dispatch custom event
            img.dispatchEvent(new CustomEvent('lazyImageLoaded', {
                detail: { src: img.src, element: img }
            }));
        };
        
        imageLoader.onerror = () => {
            // Handle image load error with fallback
            this.handleImageError(img, placeholder);
        };
        
        // Start loading the image
        imageLoader.src = img.dataset.src;
    }

    handleImageError(img, placeholder) {
        // Create a fallback SVG placeholder
        const fallbackSvg = this.createFallbackSvg();
        img.src = fallbackSvg;
        
        if (this.options.fadeTransition) {
            img.classList.remove('opacity-0');
            img.classList.add('opacity-100');
        }
        
        if (placeholder && placeholder.classList.contains(this.options.placeholderClass)) {
            placeholder.style.display = 'none';
        }
        
        // Add error class for styling
        img.classList.add('lazy-error');
        
        // Dispatch custom event
        img.dispatchEvent(new CustomEvent('lazyImageError', {
            detail: { src: img.dataset.src, element: img }
        }));
    }

    createFallbackSvg() {
        // Create a base64 encoded SVG for fallback
        const svg = `
            <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#374151"/>
                <text x="50%" y="45%" font-family="Arial" font-size="14" fill="#9CA3AF" text-anchor="middle" dy=".3em">Image not found</text>
                <text x="50%" y="60%" font-family="Arial" font-size="12" fill="#6B7280" text-anchor="middle" dy=".3em">Click to retry</text>
            </svg>
        `;
        return 'data:image/svg+xml;base64,' + btoa(svg);
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const lazyImages = document.querySelectorAll(`.${this.options.imageClass}`);
        lazyImages.forEach(img => {
            this.loadImage(img);
        });
    }

    addStyles() {
        // Check if styles already added
        if (document.getElementById('lazy-loading-styles')) {
            return;
        }
        
        const style = document.createElement('style');
        style.id = 'lazy-loading-styles';
        style.textContent = `
            .${this.options.imageClass} {
                transition: opacity 0.3s ease-in-out;
            }
            
            .${this.options.placeholderClass} {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .${this.options.loadedClass} {
                z-index: 2;
            }
            
            .lazy-error {
                cursor: pointer;
                filter: grayscale(50%);
            }
            
            .lazy-error:hover {
                filter: grayscale(0%);
            }
            
            /* Pulse animation for loading placeholder */
            @keyframes lazy-pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            
            .animate-pulse {
                animation: lazy-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            
            /* Responsive placeholder sizes */
            .lazy-placeholder-sm .animate-pulse > div:first-child {
                width: 2rem;
                height: 2rem;
            }
            
            .lazy-placeholder-md .animate-pulse > div:first-child {
                width: 3rem;
                height: 3rem;
            }
            
            .lazy-placeholder-lg .animate-pulse > div:first-child {
                width: 4rem;
                height: 4rem;
            }
        `;
        document.head.appendChild(style);
    }

    // Method to reinitialize lazy loading after dynamic content changes
    reinitialize() {
        if (this.imageObserver) {
            this.observeImages();
        } else {
            this.loadAllImages();
        }
    }

    // Method to manually load a specific image
    loadSpecificImage(img) {
        if (img.classList.contains(this.options.imageClass) && !img.classList.contains(this.options.loadedClass)) {
            this.loadImage(img);
        }
    }

    // Method to destroy the lazy loader
    destroy() {
        if (this.imageObserver) {
            this.imageObserver.disconnect();
        }
        
        // Remove styles
        const styleElement = document.getElementById('lazy-loading-styles');
        if (styleElement) {
            styleElement.remove();
        }
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if lazy images are present
    if (document.querySelector('.lazy-image')) {
        window.lazyLoader = new UniversalLazyLoader();
        
        // Add retry functionality for failed images
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('lazy-error')) {
                e.target.classList.remove('lazy-error');
                e.target.classList.add('opacity-0');
                window.lazyLoader.loadSpecificImage(e.target);
            }
        });
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UniversalLazyLoader;
}
