<?php
/**
 * Production Test Suite
 * Comprehensive testing for all critical fixes
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>🧪 Production Test Suite</h1>";
echo "<p><strong>Environment:</strong> " . (defined('ENVIRONMENT') ? ENVIRONMENT : 'Unknown') . "</p>";
echo "<p><strong>Base URL:</strong> " . getBaseUrl() . "</p>";

try {
    echo "<h2>1. Email System Tests</h2>";
    
    if (isset($_POST['test_email_system'])) {
        $testEmail = sanitize($_POST['test_email']);
        
        if (filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            echo "<h3>Testing Email System with: $testEmail</h3>";
            
            // Test 1: Basic SMTP
            echo "<h4>Test 1: Basic SMTP Email</h4>";
            $start = microtime(true);
            $basicResult = sendSMTPEmail(
                $testEmail,
                'Production Test - Basic Email',
                '<h2>Basic Email Test</h2><p>This email confirms that basic SMTP sending is working.</p><p>Timestamp: ' . date('Y-m-d H:i:s') . '</p>'
            );
            $duration = round((microtime(true) - $start) * 1000, 2);
            
            if ($basicResult) {
                echo "<p style='color: green;'>✅ Basic email sent successfully ({$duration}ms)</p>";
            } else {
                echo "<p style='color: red;'>❌ Basic email failed ({$duration}ms)</p>";
            }
            
            // Test 2: OTP Email
            echo "<h4>Test 2: OTP Email System</h4>";
            $testOTP = sprintf('%06d', mt_rand(100000, 999999));
            
            // Create or find test user
            $testUser = $database->fetch("SELECT id FROM users WHERE email = ?", [$testEmail]);
            if (!$testUser) {
                $testUserId = generateUUID();
                $database->execute(
                    "INSERT INTO users (id, name, email, role, created_at) VALUES (?, ?, ?, 'CUSTOMER', NOW())",
                    [$testUserId, 'Test User', $testEmail]
                );
                $testUser = ['id' => $testUserId];
            }
            
            $start = microtime(true);
            $otpResult = sendOTPEmail($testUser['id'], $testOTP);
            $duration = round((microtime(true) - $start) * 1000, 2);
            
            if ($otpResult) {
                echo "<p style='color: green;'>✅ OTP email sent successfully ({$duration}ms)</p>";
                echo "<p><strong>Test OTP:</strong> $testOTP</p>";
            } else {
                echo "<p style='color: red;'>❌ OTP email failed ({$duration}ms)</p>";
            }
            
            // Test 3: Customer Message System
            echo "<h4>Test 3: Customer Message System</h4>";
            $messageData = [
                'message_type' => 'email',
                'subject' => 'Production Test - Customer Message',
                'message' => 'This is a test of the customer messaging system. If you receive this, the system is working correctly.'
            ];
            
            $start = microtime(true);
            $messageResult = sendCustomerMessage($testUser['id'], $messageData);
            $duration = round((microtime(true) - $start) * 1000, 2);
            
            if ($messageResult['success']) {
                echo "<p style='color: green;'>✅ Customer message sent successfully ({$duration}ms)</p>";
                echo "<p><strong>Message ID:</strong> {$messageResult['message_id']}</p>";
            } else {
                echo "<p style='color: red;'>❌ Customer message failed ({$duration}ms)</p>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($messageResult['error'] ?? 'Unknown error') . "</p>";
            }
            
            // Test 4: Password Reset Flow
            echo "<h4>Test 4: Password Reset Flow</h4>";
            $start = microtime(true);
            $resetResult = sendPasswordResetOTP($testEmail);
            $duration = round((microtime(true) - $start) * 1000, 2);
            
            if ($resetResult['success']) {
                echo "<p style='color: green;'>✅ Password reset OTP sent successfully ({$duration}ms)</p>";
            } else {
                echo "<p style='color: red;'>❌ Password reset failed ({$duration}ms)</p>";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($resetResult['error'] ?? 'Unknown error') . "</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Invalid email address</p>";
        }
    }
    
    echo "<h2>2. Database System Tests</h2>";
    
    if (isset($_POST['test_database'])) {
        echo "<h3>Testing Database Operations</h3>";
        
        try {
            $database->beginTransaction();
            
            // Test 1: UUID Generation and Insertion
            echo "<h4>Test 1: UUID Operations</h4>";
            $testTables = ['users', 'point_transactions', 'system_settings'];
            
            foreach ($testTables as $table) {
                try {
                    $testId = generateUUID();
                    
                    if ($table === 'users') {
                        $database->execute(
                            "INSERT INTO users (id, name, email, role, created_at) VALUES (?, ?, ?, 'CUSTOMER', NOW())",
                            [$testId, 'Test User ' . time(), 'test' . time() . '@example.com']
                        );
                    } elseif ($table === 'point_transactions') {
                        // Get a real user for testing
                        $realUser = $database->fetch("SELECT id FROM users WHERE role = 'CUSTOMER' LIMIT 1");
                        if ($realUser) {
                            $database->execute(
                                "INSERT INTO point_transactions (id, user_id, points, type, description, created_at) VALUES (?, ?, ?, ?, ?, NOW())",
                                [$testId, $realUser['id'], 10, 'BONUS', 'Test transaction']
                            );
                        } else {
                            throw new Exception("No customer found for point transaction test");
                        }
                    } elseif ($table === 'system_settings') {
                        $database->execute(
                            "INSERT INTO system_settings (id, setting_key, setting_value, updated_at) VALUES (?, ?, ?, NOW())",
                            [$testId, 'test_setting_' . time(), 'test_value']
                        );
                    }
                    
                    echo "<p style='color: green;'>✅ $table: UUID insertion successful</p>";
                    
                    // Clean up test record
                    $database->execute("DELETE FROM $table WHERE id = ?", [$testId]);
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ $table: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
            // Test 2: Transaction Handling
            echo "<h4>Test 2: Transaction Handling</h4>";
            try {
                $database->beginTransaction();
                
                $testUserId = generateUUID();
                $database->execute(
                    "INSERT INTO users (id, name, email, role, created_at) VALUES (?, ?, ?, 'CUSTOMER', NOW())",
                    [$testUserId, 'Transaction Test User', 'txtest' . time() . '@example.com']
                );
                
                $testTransactionId = generateUUID();
                $database->execute(
                    "INSERT INTO point_transactions (id, user_id, points, type, description, created_at) VALUES (?, ?, ?, ?, ?, NOW())",
                    [$testTransactionId, $testUserId, 50, 'EARNED', 'Transaction test']
                );
                
                $database->commit();
                echo "<p style='color: green;'>✅ Transaction commit successful</p>";
                
                // Clean up
                $database->execute("DELETE FROM point_transactions WHERE id = ?", [$testTransactionId]);
                $database->execute("DELETE FROM users WHERE id = ?", [$testUserId]);
                
            } catch (Exception $e) {
                $database->rollback();
                echo "<p style='color: red;'>❌ Transaction failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            
            $database->rollback(); // Rollback the main test transaction
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ Database test error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>3. System Integration Tests</h2>";
    
    if (isset($_POST['test_integration'])) {
        echo "<h3>Testing System Integration</h3>";
        
        // Test 1: Staff Suggestions API
        echo "<h4>Test 1: Staff Suggestions API</h4>";
        try {
            $staffSuggestions = getStaffSuggestions();
            if (!empty($staffSuggestions)) {
                echo "<p style='color: green;'>✅ Staff suggestions API working (" . count($staffSuggestions) . " staff found)</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Staff suggestions API returned empty results</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Staff suggestions API failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test 2: Admin Panel Functions
        echo "<h4>Test 2: Admin Panel Functions</h4>";
        try {
            $customers = getCustomers();
            if (is_array($customers)) {
                echo "<p style='color: green;'>✅ Customer listing working (" . count($customers) . " customers)</p>";
            } else {
                echo "<p style='color: red;'>❌ Customer listing failed</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Admin panel test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Test 3: Booking System
        echo "<h4>Test 3: Booking System</h4>";
        try {
            $services = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1 LIMIT 5");
            if (!empty($services)) {
                echo "<p style='color: green;'>✅ Services available (" . count($services) . " active services)</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ No active services found</p>";
            }
            
            $staff = $database->fetchAll("SELECT id, name FROM users WHERE role = 'STAFF' LIMIT 5");
            if (!empty($staff)) {
                echo "<p style='color: green;'>✅ Staff available (" . count($staff) . " staff members)</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ No staff members found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Booking system test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Critical error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🧪 Test Controls</h2>";
?>

<div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>📧 Email System Test</h3>
    <form method="post">
        <label>Test Email Address:</label><br>
        <input type="email" name="test_email" required style="width: 300px; padding: 8px; margin: 5px 0;" placeholder="<EMAIL>">
        <br><br>
        <button type="submit" name="test_email_system" style="background: #2196f3; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Test Email System</button>
    </form>
</div>

<div style="background: #f3e5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>🗄️ Database System Test</h3>
    <form method="post">
        <button type="submit" name="test_database" style="background: #9c27b0; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Test Database Operations</button>
    </form>
</div>

<div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>🔗 Integration Test</h3>
    <form method="post">
        <button type="submit" name="test_integration" style="background: #4caf50; color: white; padding: 10px 20px; border: none; border-radius: 5px;">Test System Integration</button>
    </form>
</div>

<div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h3>🔗 Quick Links</h3>
    <ul>
        <li><a href="<?= getBasePath() ?>/auth/forgot-password.php" target="_blank">Test Password Reset Flow</a></li>
        <li><a href="<?= getBasePath() ?>/admin/customers/" target="_blank">Test Customer Messaging</a></li>
        <li><a href="<?= getBasePath() ?>/customer/book.php" target="_blank">Test Booking System</a></li>
        <li><a href="<?= getBasePath() ?>/api/staff-suggestions.php" target="_blank">Test Staff API</a></li>
    </ul>
</div>
