<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);
$pointsData = getCustomerPointsData($customerId);
$loyaltyTier = getCustomerLoyaltyTierEnhanced($customerId);
$stats = getIndividualCustomerStats($customerId);

// Get available rewards
global $database;
$rewards = $database->fetchAll("
    SELECT * FROM rewards 
    WHERE is_active = 1 
    ORDER BY points_required ASC
");

$pageTitle = "Rewards & Points";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Page Header -->
<div class="bg-secondary-800 shadow mb-8">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="py-6 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <div>
                        <div class="flex items-center">
                            <h1 class="text-2xl font-bold leading-7 text-white sm:leading-9 sm:truncate">
                                Rewards & Points
                            </h1>
                        </div>
                        <dl class="mt-2 flex flex-col sm:mt-1 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">Page description</dt>
                            <dd class="flex items-center text-sm text-gray-300 font-medium sm:mr-6">
                                Track your loyalty points and redeem exclusive rewards
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

            <!-- Points Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Current Points -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-coins text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Current Points</p>
                            <p class="text-3xl font-bold text-white"><?= number_format($pointsData['currentPoints']) ?></p>
                            <p class="text-xs text-gray-400">= TSH <?= number_format($pointsData['currentPoints'] * 10) ?> value</p>
                        </div>
                    </div>
                </div>

                <!-- Monthly Earned -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-plus text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Earned This Month</p>
                            <p class="text-3xl font-bold text-green-400">+<?= number_format($pointsData['monthlyEarned']) ?></p>
                            <p class="text-xs text-gray-400">Keep it up!</p>
                        </div>
                    </div>
                </div>

                <!-- Monthly Redeemed -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-minus text-2xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-400">Redeemed This Month</p>
                            <p class="text-3xl font-bold text-red-400">-<?= number_format($pointsData['monthlyRedeemed']) ?></p>
                            <p class="text-xs text-gray-400">Great savings!</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loyalty Tier Progress -->
            <div class="bg-secondary-800 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-white mb-4">Loyalty Tier Progress</h3>
                
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 rounded-full bg-<?= $loyaltyTier['color'] ?>-100 flex items-center justify-center">
                            <i class="fas fa-crown text-<?= $loyaltyTier['color'] ?>-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold text-white"><?= $loyaltyTier['name'] ?> Member</h4>
                            <p class="text-sm text-gray-400">Total spent: TSH <?= number_format($stats['totalSpent']) ?></p>
                            <p class="text-xs text-salon-gold"><?= $loyaltyTier['pointsMultiplier'] ?>x points multiplier</p>
                        </div>
                    </div>
                    
                    <?php if ($loyaltyTier['nextTier']): ?>
                        <div class="text-right">
                            <p class="text-sm text-gray-400">Next tier: <?= $loyaltyTier['nextTier'] ?></p>
                            <p class="text-sm font-semibold text-salon-gold">TSH <?= number_format($loyaltyTier['nextTierAmount']) ?> to go</p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($loyaltyTier['nextTier']): ?>
                    <?php 
                    $progress = (($stats['totalSpent'] - $loyaltyTier['minSpent']) / 
                                ($loyaltyTier['nextTierAmount'] + $stats['totalSpent'] - $loyaltyTier['minSpent'])) * 100;
                    ?>
                    <div class="w-full bg-secondary-700 rounded-full h-3 mb-4">
                        <div class="bg-salon-gold h-3 rounded-full transition-all duration-500" style="width: <?= min(100, max(0, $progress)) ?>%"></div>
                    </div>
                <?php else: ?>
                    <div class="w-full bg-salon-gold rounded-full h-3 mb-4"></div>
                    <p class="text-center text-salon-gold font-semibold">🎉 You've reached the highest tier!</p>
                <?php endif; ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <h5 class="font-medium text-white col-span-full">Your Benefits:</h5>
                    <?php foreach ($loyaltyTier['benefits'] as $benefit): ?>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-check text-salon-gold text-sm"></i>
                            <span class="text-sm text-gray-300"><?= htmlspecialchars($benefit) ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Available Rewards -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Available Rewards</h3>
                    
                    <?php if (empty($rewards)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-gift text-4xl text-gray-600 mb-4"></i>
                            <p class="text-gray-400">No rewards available at the moment</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($rewards as $reward): ?>
                                <div class="border border-secondary-600 rounded-lg p-4 <?= $pointsData['currentPoints'] >= $reward['points_required'] ? 'border-salon-gold' : '' ?>">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-white"><?= htmlspecialchars($reward['name']) ?></h4>
                                        <div class="text-right">
                                            <p class="text-sm font-semibold text-salon-gold"><?= number_format($reward['points_required']) ?> pts</p>
                                            <p class="text-xs text-gray-400">TSH <?= number_format($reward['value']) ?> value</p>
                                        </div>
                                    </div>
                                    
                                    <p class="text-sm text-gray-400 mb-3"><?= htmlspecialchars($reward['description']) ?></p>
                                    
                                    <?php if ($pointsData['currentPoints'] >= $reward['points_required']): ?>
                                        <button onclick="redeemReward('<?= $reward['id'] ?>', '<?= htmlspecialchars($reward['name']) ?>', <?= $reward['points_required'] ?>)" 
                                                class="w-full bg-salon-gold hover:bg-yellow-500 text-black px-4 py-2 rounded-lg font-semibold transition-colors">
                                            <i class="fas fa-gift mr-2"></i>Redeem Now
                                        </button>
                                    <?php else: ?>
                                        <div class="w-full bg-secondary-700 text-gray-400 px-4 py-2 rounded-lg text-center">
                                            <i class="fas fa-lock mr-2"></i>Need <?= number_format($reward['points_required'] - $pointsData['currentPoints']) ?> more points
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Points History -->
                <div class="bg-secondary-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Points History</h3>
                    
                    <?php if (empty($pointsData['transactions'])): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-history text-4xl text-gray-600 mb-4"></i>
                            <p class="text-gray-400">No point transactions yet</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3 max-h-96 overflow-y-auto">
                            <?php foreach ($pointsData['transactions'] as $transaction): ?>
                                <div class="flex items-center justify-between border-b border-secondary-700 pb-3">
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 rounded-full <?= $transaction['type'] === 'EARNED' ? 'bg-green-100' : 'bg-red-100' ?> flex items-center justify-center">
                                            <i class="fas fa-<?= $transaction['type'] === 'EARNED' ? 'plus' : 'minus' ?> text-<?= $transaction['type'] === 'EARNED' ? 'green' : 'red' ?>-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-white"><?= htmlspecialchars($transaction['description']) ?></p>
                                            <p class="text-xs text-gray-400"><?= date('M j, Y g:i A', strtotime($transaction['created_at'])) ?></p>
                                        </div>
                                    </div>
                                    <span class="font-semibold <?= $transaction['type'] === 'EARNED' ? 'text-green-400' : 'text-red-400' ?>">
                                        <?= $transaction['type'] === 'EARNED' ? '+' : '' ?><?= number_format($transaction['points']) ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- How to Earn Points -->
            <div class="bg-secondary-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">How to Earn Points</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-calendar-check text-blue-600 text-2xl"></i>
                        </div>
                        <h4 class="font-medium text-white mb-2">Book Services</h4>
                        <p class="text-sm text-gray-400">Earn 1 point for every TSH 1,000 spent</p>
                        <p class="text-xs text-salon-gold">Your tier: <?= $loyaltyTier['pointsMultiplier'] ?>x multiplier</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-user-friends text-green-600 text-2xl"></i>
                        </div>
                        <h4 class="font-medium text-white mb-2">Refer Friends</h4>
                        <p class="text-sm text-gray-400">Get 100 points when a friend books their first service</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-birthday-cake text-purple-600 text-2xl"></i>
                        </div>
                        <h4 class="font-medium text-white mb-2">Birthday Bonus</h4>
                        <p class="text-sm text-gray-400">Receive 50 bonus points on your birthday month</p>
                    </div>

                    <div class="text-center">
                        <div class="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-star text-yellow-600 text-2xl"></i>
                        </div>
                        <h4 class="font-medium text-white mb-2">Leave Reviews</h4>
                        <p class="text-sm text-gray-400">Earn 25 points for each service review</p>
                    </div>
                </div>
            </div>

            <!-- Customer Tiers Overview -->
            <div class="bg-secondary-800 rounded-lg p-6 mt-6">
                <h3 class="text-lg font-semibold text-white mb-4">Customer Tiers</h3>
                <p class="text-sm text-gray-400 mb-6">Spend more to unlock higher tiers and better rewards!</p>

                <?php
                // Get all tiers for display
                require_once __DIR__ . '/../../includes/rewards_functions.php';
                $allTiers = getCustomerTiers();
                ?>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <?php foreach ($allTiers as $tier): ?>
                        <?php
                        $isCurrentTier = $tier['name'] === $loyaltyTier['name'];
                        $isUnlocked = $stats['totalSpent'] >= $tier['minSpent'];
                        ?>
                        <div class="border <?= $isCurrentTier ? 'border-salon-gold bg-salon-gold/10' : ($isUnlocked ? 'border-green-500/30 bg-green-500/5' : 'border-secondary-600') ?> rounded-lg p-4">
                            <div class="text-center">
                                <div class="w-12 h-12 rounded-full <?= str_replace('text-', 'bg-', $tier['color']) ?>/20 flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-crown <?= $tier['color'] ?> text-xl"></i>
                                </div>
                                <h4 class="font-semibold text-white mb-1"><?= $tier['name'] ?></h4>
                                <p class="text-xs text-gray-400 mb-2">TSH <?= number_format($tier['minSpent']) ?>+</p>
                                <p class="text-xs text-salon-gold mb-3"><?= $tier['pointsMultiplier'] ?>x points</p>

                                <?php if ($isCurrentTier): ?>
                                    <span class="inline-block px-2 py-1 bg-salon-gold text-black text-xs rounded font-medium">Current Tier</span>
                                <?php elseif ($isUnlocked): ?>
                                    <span class="inline-block px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">Unlocked</span>
                                <?php else: ?>
                                    <span class="inline-block px-2 py-1 bg-secondary-700 text-gray-400 text-xs rounded">Locked</span>
                                <?php endif; ?>

                                <div class="mt-3 space-y-1">
                                    <?php foreach (array_slice($tier['benefits'], 0, 2) as $benefit): ?>
                                        <p class="text-xs text-gray-300"><?= htmlspecialchars($benefit) ?></p>
                                    <?php endforeach; ?>
                                    <?php if (count($tier['benefits']) > 2): ?>
                                        <p class="text-xs text-gray-400">+<?= count($tier['benefits']) - 2 ?> more</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Redeem Confirmation Modal -->
    <div id="redeemModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-secondary-800 rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">Confirm Redemption</h3>
                        <button onclick="closeRedeemModal()" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="text-center mb-6">
                        <div class="w-16 h-16 rounded-full bg-salon-gold flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-gift text-black text-2xl"></i>
                        </div>
                        <h4 class="text-xl font-semibold text-white mb-2" id="rewardName">Reward Name</h4>
                        <p class="text-gray-400">Are you sure you want to redeem this reward?</p>
                        <p class="text-salon-gold font-semibold mt-2"><span id="rewardPoints">0</span> points will be deducted</p>
                    </div>

                    <div class="flex items-center justify-end space-x-3">
                        <button type="button" onclick="closeRedeemModal()" 
                                class="px-4 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="button" onclick="confirmRedemption()" 
                                class="px-4 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            Confirm Redemption
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedReward = null;

        function redeemReward(rewardId, rewardName, pointsRequired) {
            selectedReward = {
                id: rewardId,
                name: rewardName,
                points: pointsRequired
            };
            
            document.getElementById('rewardName').textContent = rewardName;
            document.getElementById('rewardPoints').textContent = pointsRequired.toLocaleString();
            document.getElementById('redeemModal').classList.remove('hidden');
        }

        function closeRedeemModal() {
            document.getElementById('redeemModal').classList.add('hidden');
            selectedReward = null;
        }

        async function confirmRedemption() {
            if (!selectedReward) return;
            
            try {
                const response = await fetch('<?= getBasePath() ?>/api/customer/redeem-reward.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        reward_id: selectedReward.id
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Reward redeemed successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            } catch (error) {
                console.error('Error redeeming reward:', error);
                alert('An error occurred while redeeming the reward');
            }
            
            closeRedeemModal();
        }

        // Close modal when clicking outside
        document.getElementById('redeemModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRedeemModal();
            }
        });
    </script>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
