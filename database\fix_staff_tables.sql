-- Fix Staff Tables and Data
-- This script creates missing tables and migrates existing staff data

USE flix_salonce;

-- Drop and recreate staff_schedules table with correct structure
DROP TABLE IF EXISTS staff_schedules;

CREATE TABLE staff_schedules (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    staff_id VARCHAR(36) NULL COMMENT 'For backward compatibility',
    day_of_week VARCHAR(20) NULL COMMENT 'For individual day schedules',
    start_time TIME NULL,
    end_time TIME NULL,
    is_working BOOLEAN DEFAULT TRUE,
    role VARCHAR(100) DEFAULT 'Staff Member',
    hourly_rate DECIMAL(8,2) DEFAULT 50.00,
    bio TEXT,
    experience INT DEFAULT 0,
    schedule JSON COMMENT 'Weekly schedule as JSON for full schedule storage',
    specialties JSON COMMENT 'For backward compatibility',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_staff_schedules_user (user_id),
    INDEX idx_staff_schedules_day (day_of_week),
    INDEX idx_staff_schedules_working (is_working)
);

-- Drop and recreate staff_specialties table with correct structure
DROP TABLE IF EXISTS staff_specialties;

CREATE TABLE staff_specialties (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    proficiency_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT') DEFAULT 'INTERMEDIATE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_service (user_id, service_id),
    INDEX idx_staff_specialties_user (user_id),
    INDEX idx_staff_specialties_service (service_id)
);

-- Update bookings table to include missing status values
ALTER TABLE bookings MODIFY COLUMN status 
ENUM('PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'EXPIRED') 
DEFAULT 'PENDING';

-- Migrate existing staff data from staff table to staff_schedules (if staff table exists and has data)
INSERT IGNORE INTO staff_schedules (id, user_id, role, hourly_rate, bio, experience, schedule, created_at, updated_at)
SELECT 
    CONCAT('schedule-', s.id) as id,
    u.id as user_id,
    s.role,
    s.hourly_rate,
    s.bio,
    s.experience,
    s.schedule,
    s.created_at,
    s.updated_at
FROM staff s
JOIN users u ON s.email = u.email
WHERE u.role = 'STAFF'
AND NOT EXISTS (
    SELECT 1 FROM staff_schedules ss WHERE ss.user_id = u.id
);

-- Create default staff schedule records for staff users who don't have them
INSERT IGNORE INTO staff_schedules (id, user_id, role, hourly_rate, schedule, created_at, updated_at)
SELECT 
    CONCAT('schedule-', u.id) as id,
    u.id as user_id,
    'Staff Member' as role,
    50.00 as hourly_rate,
    JSON_OBJECT(
        'monday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'tuesday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'wednesday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'thursday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'friday', JSON_OBJECT('start_time', '09:00', 'end_time', '17:00', 'is_working', true),
        'saturday', JSON_OBJECT('start_time', '10:00', 'end_time', '16:00', 'is_working', true),
        'sunday', JSON_OBJECT('start_time', '10:00', 'end_time', '16:00', 'is_working', false)
    ) as schedule,
    NOW() as created_at,
    NOW() as updated_at
FROM users u
WHERE u.role = 'STAFF'
AND NOT EXISTS (
    SELECT 1 FROM staff_schedules ss WHERE ss.user_id = u.id
);

-- Add some default specialties for staff members (link them to existing services)
INSERT IGNORE INTO staff_specialties (id, user_id, service_id, proficiency_level, created_at, updated_at)
SELECT 
    CONCAT('specialty-', u.id, '-', s.id) as id,
    u.id as user_id,
    s.id as service_id,
    'INTERMEDIATE' as proficiency_level,
    NOW() as created_at,
    NOW() as updated_at
FROM users u
CROSS JOIN services s
WHERE u.role = 'STAFF'
AND s.is_active = 1
AND NOT EXISTS (
    SELECT 1 FROM staff_specialties sp WHERE sp.user_id = u.id AND sp.service_id = s.id
)
LIMIT 20; -- Limit to prevent too many specialties per staff

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_staff_date ON bookings(staff_id, date);
CREATE INDEX IF NOT EXISTS idx_bookings_status_date ON bookings(status, date);

-- Verify the migration
SELECT 'Staff Users' as table_name, COUNT(*) as count FROM users WHERE role = 'STAFF'
UNION ALL
SELECT 'Staff Schedules' as table_name, COUNT(*) as count FROM staff_schedules
UNION ALL
SELECT 'Staff Specialties' as table_name, COUNT(*) as count FROM staff_specialties;
