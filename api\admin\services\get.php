<?php
/**
 * Get Single Service API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../../config/app.php';

// Set JSON header
header('Content-Type: application/json');

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get service ID from query parameter
$serviceId = $_GET['id'] ?? '';

if (empty($serviceId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Service ID is required']);
    exit;
}

try {
    // Get single service
    $service = getServiceById($serviceId);

    if ($service) {
        echo json_encode($service);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'Service not found']);
    }

} catch (Exception $e) {
    error_log("Get Service API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
