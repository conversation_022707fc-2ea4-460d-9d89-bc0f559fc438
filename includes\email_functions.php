<?php
/**
 * Email Functions
 * Comprehensive email system for Flix Salon & SPA
 * Uses PHPMailer for SMTP with SSL/TLS authentication
 */

// Include PHPMailer
require_once __DIR__ . '/../admin/vendor/PHPMailer/src/Exception.php';
require_once __DIR__ . '/../admin/vendor/PHPMailer/src/PHPMailer.php';
require_once __DIR__ . '/../admin/vendor/PHPMailer/src/SMTP.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

/**
 * Enhanced SMTP Email Sender using <PERSON><PERSON>Mailer
 * Sends emails using SMTP with proper authentication
 */
function sendSMTPEmail($to, $subject, $body, $options = []) {
    // Default options
    $options = array_merge([
        'from_name' => SMTP_FROM_NAME,
        'from_email' => SMTP_FROM_EMAIL,
        'reply_to' => SMTP_FROM_EMAIL,
        'is_html' => true,
        'attachments' => [],
        'cc' => [],
        'bcc' => []
    ], $options);

    // Validate email addresses
    if (!isValidEmail($to)) {
        error_log("Invalid recipient email: $to");
        return false;
    }

    if (!isValidEmail($options['from_email'])) {
        error_log("Invalid sender email: {$options['from_email']}");
        return false;
    }

    // Log email attempt
    error_log("Attempting to send email to: $to, Subject: $subject");

    try {
        // Create a new PHPMailer instance
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST;
        $mail->SMTPAuth   = true;
        $mail->Username   = SMTP_USERNAME;
        $mail->Password   = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_SECURE; // 'ssl' for port 465
        $mail->Port       = SMTP_PORT;

        // Additional settings for better compatibility
        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );

        // Set timeout
        $mail->Timeout = 60;

        // Enable verbose debug output (disable in production)
        $mail->SMTPDebug = 0; // Set to 2 for debugging
        $mail->Debugoutput = 'error_log';

        // Recipients
        $mail->setFrom($options['from_email'], $options['from_name']);
        $mail->addAddress($to);
        $mail->addReplyTo($options['reply_to'], $options['from_name']);

        // Add CC recipients
        if (!empty($options['cc'])) {
            foreach ($options['cc'] as $cc_email) {
                if (isValidEmail($cc_email)) {
                    $mail->addCC($cc_email);
                }
            }
        }

        // Add BCC recipients
        if (!empty($options['bcc'])) {
            foreach ($options['bcc'] as $bcc_email) {
                if (isValidEmail($bcc_email)) {
                    $mail->addBCC($bcc_email);
                }
            }
        }

        // Add attachments
        if (!empty($options['attachments'])) {
            foreach ($options['attachments'] as $attachment) {
                if (is_array($attachment)) {
                    $mail->addAttachment($attachment['path'], $attachment['name'] ?? '');
                } else {
                    $mail->addAttachment($attachment);
                }
            }
        }

        // Content
        $mail->isHTML($options['is_html']);
        $mail->Subject = $subject;
        $mail->Body    = $body;

        // Set plain text version for HTML emails
        if ($options['is_html']) {
            $mail->AltBody = strip_tags($body);
        }

        // Send the email
        $result = $mail->send();

        if ($result) {
            error_log("Email sent successfully to: $to");
            logEmailActivity($to, $subject, 'SENT');
        }

        return $result;

    } catch (Exception $e) {
        error_log("PHPMailer Error: {$mail->ErrorInfo}");
        error_log("Failed to send email to: $to - " . $e->getMessage());
        logEmailActivity($to, $subject, 'FAILED', $e->getMessage());
        return false;
    }
}



/**
 * Log email activity for tracking
 */
function logEmailActivity($recipient, $subject, $status, $details = '') {
    global $database;
    
    try {
        $database->query(
            "INSERT INTO email_logs (recipient, subject, status, details, created_at) VALUES (?, ?, ?, ?, NOW())",
            [$recipient, $subject, $status, $details]
        );
    } catch (Exception $e) {
        error_log("Failed to log email activity: " . $e->getMessage());
    }
}

/**
 * Get email template with variable substitution
 */
function getEmailTemplate($templateName, $variables = []) {
    $templates = [
        'booking_confirmation' => [
            'subject' => 'Booking Confirmation - {{service_name}}',
            'body' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                        <h1 style="margin: 0; font-size: 28px;">{{app_name}}</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">Your booking has been confirmed!</p>
                    </div>
                    
                    <div style="padding: 30px; background: #f8f9fa;">
                        <h2 style="color: #333; margin-top: 0;">Hello {{customer_name}},</h2>
                        <p style="color: #666; line-height: 1.6;">Thank you for choosing {{app_name}}! Your booking has been confirmed with the following details:</p>
                        
                        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                            <h3 style="margin-top: 0; color: #333;">Booking Details</h3>
                            <p><strong>Service:</strong> {{service_name}}</p>
                            <p><strong>Date:</strong> {{booking_date}}</p>
                            <p><strong>Time:</strong> {{booking_time}}</p>
                            <p><strong>Duration:</strong> {{duration}} minutes</p>
                            <p><strong>Staff:</strong> {{staff_name}}</p>
                            <p><strong>Total Amount:</strong> {{total_amount}}</p>
                        </div>
                        
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                            <p style="margin: 0; color: #1565c0;"><strong>Important:</strong> Please arrive 10 minutes before your appointment time.</p>
                        </div>
                        
                        <p style="color: #666; line-height: 1.6;">If you need to reschedule or cancel your appointment, please contact us at least 24 hours in advance.</p>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{booking_url}}" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Booking</a>
                        </div>
                    </div>
                    
                    <div style="background: #333; color: white; padding: 20px; text-align: center;">
                        <p style="margin: 0;">{{app_name}} | {{contact_phone}} | {{contact_email}}</p>
                    </div>
                </div>
            '
        ],
        
        'booking_reminder' => [
            'subject' => 'Appointment Reminder - {{service_name}}',
            'body' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%); color: white; padding: 30px; text-align: center;">
                        <h1 style="margin: 0; font-size: 28px;">{{app_name}}</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">Appointment Reminder</p>
                    </div>
                    
                    <div style="padding: 30px; background: #f8f9fa;">
                        <h2 style="color: #333; margin-top: 0;">Hello {{customer_name}},</h2>
                        <p style="color: #666; line-height: 1.6;">This is a friendly reminder about your upcoming appointment:</p>
                        
                        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff6b6b;">
                            <h3 style="margin-top: 0; color: #333;">Appointment Details</h3>
                            <p><strong>Service:</strong> {{service_name}}</p>
                            <p><strong>Date:</strong> {{booking_date}}</p>
                            <p><strong>Time:</strong> {{booking_time}}</p>
                            <p><strong>Staff:</strong> {{staff_name}}</p>
                            <p><strong>Duration:</strong> {{duration}} minutes</p>
                        </div>
                        
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ffeaa7;">
                            <p style="margin: 0; color: #856404;"><strong>Reminder:</strong> Please arrive 10 minutes before your appointment time.</p>
                        </div>
                        
                        <p style="color: #666; line-height: 1.6;">We look forward to seeing you soon!</p>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{booking_url}}" style="background: #ff6b6b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Booking</a>
                        </div>
                    </div>
                    
                    <div style="background: #333; color: white; padding: 20px; text-align: center;">
                        <p style="margin: 0;">{{app_name}} | {{contact_phone}} | {{contact_email}}</p>
                    </div>
                </div>
            '
        ],
        
        'password_reset' => [
            'subject' => 'Password Reset Request - {{app_name}}',
            'body' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                        <h1 style="margin: 0; font-size: 28px;">{{app_name}}</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">Password Reset Request</p>
                    </div>

                    <div style="padding: 30px; background: #f8f9fa;">
                        <h2 style="color: #333; margin-top: 0;">Hello {{user_name}},</h2>
                        <p style="color: #666; line-height: 1.6;">We received a request to reset your password. Click the button below to create a new password:</p>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{{reset_url}}" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Reset Password</a>
                        </div>

                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;">
                            <p style="margin: 0; color: #721c24;"><strong>Security Notice:</strong> This link will expire in 1 hour. If you didn\'t request this reset, please ignore this email.</p>
                        </div>

                        <p style="color: #666; line-height: 1.6; font-size: 14px;">If the button doesn\'t work, copy and paste this link into your browser:<br>
                        <span style="word-break: break-all;">{{reset_url}}</span></p>
                    </div>

                    <div style="background: #333; color: white; padding: 20px; text-align: center;">
                        <p style="margin: 0;">{{app_name}} | {{contact_phone}} | {{contact_email}}</p>
                    </div>
                </div>
            '
        ],

        'password_reset_otp' => [
            'subject' => 'Password Reset Code - {{app_name}}',
            'body' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                        <h1 style="margin: 0; font-size: 28px;">{{app_name}}</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">Password Reset Verification</p>
                    </div>

                    <div style="padding: 30px; background: #f8f9fa;">
                        <h2 style="color: #333; margin-top: 0;">Hello {{user_name}},</h2>
                        <p style="color: #666; line-height: 1.6;">We received a request to reset your password. Use the verification code below to proceed:</p>

                        <div style="background: white; padding: 30px; border-radius: 12px; margin: 30px 0; text-align: center; border: 2px solid #667eea;">
                            <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Your Verification Code</h3>
                            <div style="font-size: 36px; font-weight: bold; color: #667eea; letter-spacing: 8px; font-family: monospace;">{{otp_code}}</div>
                            <p style="color: #666; margin: 15px 0 0 0; font-size: 14px;">This code expires in 15 minutes</p>
                        </div>

                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ffeaa7;">
                            <p style="margin: 0; color: #856404;"><strong>Security Tips:</strong></p>
                            <ul style="margin: 10px 0 0 0; color: #856404; padding-left: 20px;">
                                <li>Never share this code with anyone</li>
                                <li>We will never ask for this code over phone or email</li>
                                <li>If you didn\'t request this, please ignore this email</li>
                            </ul>
                        </div>

                        <p style="color: #666; line-height: 1.6; font-size: 14px;">Enter this code on the password reset page to continue. The code will expire in 15 minutes for your security.</p>
                    </div>

                    <div style="background: #333; color: white; padding: 20px; text-align: center;">
                        <p style="margin: 0;">{{app_name}} | {{contact_phone}} | {{contact_email}}</p>
                    </div>
                </div>
            '
        ],

        'contact_reply' => [
            'subject' => 'Re: {{original_subject}} - {{app_name}}',
            'body' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff;">
                    <!-- Header -->
                    <div style="background: #ffffff; padding: 30px; text-align: center; border-bottom: 3px solid #d4af37;">
                        <h1 style="margin: 0; font-size: 28px; color: #333333; font-weight: bold;">{{app_name}}</h1>
                        <p style="margin: 10px 0 0 0; color: #666666; font-size: 16px;">Thank you for contacting us!</p>
                    </div>

                    <!-- Main Content -->
                    <div style="padding: 30px; background: #ffffff;">
                        <h2 style="color: #333333; margin-top: 0; font-size: 20px;">Dear {{customer_name}},</h2>
                        <p style="color: #666666; line-height: 1.6; margin-bottom: 25px;">Thank you for reaching out to {{app_name}}. We have received your message and here is our response:</p>

                        <!-- Original Message Section -->
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d4af37;">
                            <h3 style="margin-top: 0; color: #333333; font-size: 16px; font-weight: bold;">Your Original Message:</h3>
                            <div style="background: #ffffff; padding: 15px; border-radius: 6px; margin: 10px 0; border: 1px solid #e9ecef;">
                                <p style="margin: 0; color: #666666; font-style: italic; line-height: 1.5;">"{{original_message}}"</p>
                            </div>
                        </div>

                        <!-- Response Section -->
                        <div style="background: #f0f8f0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
                            <h3 style="margin-top: 0; color: #333333; font-size: 16px; font-weight: bold;">Our Response:</h3>
                            <div style="color: #333333; line-height: 1.6;">{{reply_message}}</div>
                        </div>

                        <!-- Call to Action -->
                        <div style="background: #f0f7ff; padding: 20px; border-radius: 8px; margin: 25px 0; text-align: center; border: 1px solid #d4edda;">
                            <p style="margin: 0 0 15px 0; color: #0066cc; font-weight: bold;">Need Further Assistance?</p>
                            <p style="margin: 0 0 20px 0; color: #666666; line-height: 1.5;">Feel free to reply to this email or contact us directly.</p>
                            <a href="{{contact_url}}" style="background: #d4af37; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Contact Us Again</a>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div style="background: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e9ecef;">
                        <h3 style="margin: 0 0 15px 0; color: #d4af37; font-size: 20px;">{{app_name}}</h3>
                        
                        <div style="margin: 20px 0;">
                            <p style="margin: 5px 0; color: #666666; font-size: 14px;">📞 {{contact_phone}}</p>
                            <p style="margin: 5px 0; color: #666666; font-size: 14px;">✉️ {{contact_email}}</p>
                            <p style="margin: 5px 0; color: #666666; font-size: 14px;">🌐 {{website_url}}</p>
                        </div>

                        <div style="border-top: 1px solid #e9ecef; padding-top: 15px; margin-top: 20px;">
                            <p style="margin: 0; font-size: 12px; color: #999999;">
                                Thank you for choosing {{app_name}} - Your beauty, our passion!
                            </p>
                        </div>
                    </div>
                </div>
            '
        ]
    ];
    
    if (!isset($templates[$templateName])) {
        return false;
    }
    
    $template = $templates[$templateName];
    
    // Replace variables in subject and body
    foreach ($variables as $key => $value) {
        $template['subject'] = str_replace('{{' . $key . '}}', $value, $template['subject']);
        $template['body'] = str_replace('{{' . $key . '}}', $value, $template['body']);
    }
    
    return $template;
}

/**
 * Send booking confirmation email
 */
function sendBookingConfirmationEmail($bookingId) {
    global $database;
    
    // Get booking details
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, s.duration, s.price as service_price,
                sv.name as variation_name, sv.price as variation_price, sv.duration as variation_duration,
                p.name as package_name, p.price as package_price,
                st.name as staff_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
         LEFT JOIN packages p ON b.package_id = p.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         WHERE b.id = ?",
        [$bookingId]
    );
    
    if (!$booking || !$booking['customer_email']) {
        return false;
    }
    
    // Build service name with variation if applicable
    $serviceName = $booking['service_name'] ?: $booking['package_name'] ?: 'Service';
    if (!empty($booking['variation_name'])) {
        $serviceName .= ' (' . $booking['variation_name'] . ')';
    }

    // Use variation price and duration if available
    $servicePrice = $booking['variation_price'] ?: $booking['service_price'] ?: $booking['package_price'];
    $totalAmount = formatCurrency($servicePrice ?: $booking['total_amount']);
    $duration = $booking['variation_duration'] ?: $booking['duration'] ?: 60;
    
    $variables = [
        'app_name' => APP_NAME,
        'customer_name' => $booking['customer_name'],
        'service_name' => $serviceName,
        'booking_date' => formatDate($booking['date']),
        'booking_time' => formatTime($booking['start_time']),
        'duration' => $duration,
        'staff_name' => $booking['staff_name'] ?: 'To be assigned',
        'total_amount' => $totalAmount,
        'booking_url' => getBaseUrl() . '/customer/bookings/view.php?id=' . $bookingId,
        'contact_phone' => '+255 781 985 757', // You can make this configurable
        'contact_email' => SMTP_FROM_EMAIL
    ];
    
    $template = getEmailTemplate('booking_confirmation', $variables);
    if (!$template) {
        return false;
    }
    
    return sendSMTPEmail(
        $booking['customer_email'],
        $template['subject'],
        $template['body']
    );
}

/**
 * Send booking reminder email
 */
function sendBookingReminderEmail($bookingId, $reminderType = '30_minutes') {
    global $database;

    // Get booking details
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, s.duration,
                sv.name as variation_name, sv.duration as variation_duration,
                p.name as package_name,
                st.name as staff_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
         LEFT JOIN packages p ON b.package_id = p.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         WHERE b.id = ? AND b.status IN ('CONFIRMED', 'PENDING')",
        [$bookingId]
    );

    if (!$booking || !$booking['customer_email']) {
        return false;
    }

    // Build service name with variation if applicable
    $serviceName = $booking['service_name'] ?: $booking['package_name'] ?: 'Service';
    if (!empty($booking['variation_name'])) {
        $serviceName .= ' (' . $booking['variation_name'] . ')';
    }

    $duration = $booking['variation_duration'] ?: $booking['duration'] ?: 60;

    $variables = [
        'app_name' => APP_NAME,
        'customer_name' => $booking['customer_name'],
        'service_name' => $serviceName,
        'booking_date' => formatDate($booking['date']),
        'booking_time' => formatTime($booking['start_time']),
        'duration' => $duration,
        'staff_name' => $booking['staff_name'] ?: 'To be assigned',
        'booking_url' => getBaseUrl() . '/customer/bookings/view.php?id=' . $bookingId,
        'contact_phone' => '+255 781 985 757',
        'contact_email' => SMTP_FROM_EMAIL
    ];

    $template = getEmailTemplate('booking_reminder', $variables);
    if (!$template) {
        return false;
    }

    return sendSMTPEmail(
        $booking['customer_email'],
        $template['subject'],
        $template['body']
    );
}

/**
 * Send password reset email
 */
function sendPasswordResetEmail($userId, $resetToken) {
    global $database;

    // Get user details
    $user = $database->fetch(
        "SELECT * FROM users WHERE id = ?",
        [$userId]
    );

    if (!$user || !$user['email']) {
        return false;
    }

    $resetUrl = getBaseUrl() . '/auth/reset-password.php?token=' . $resetToken;

    $variables = [
        'app_name' => APP_NAME,
        'user_name' => $user['name'],
        'reset_url' => $resetUrl,
        'contact_phone' => '+255 781 985 757',
        'contact_email' => SMTP_FROM_EMAIL
    ];

    $template = getEmailTemplate('password_reset', $variables);
    if (!$template) {
        return false;
    }

    return sendSMTPEmail(
        $user['email'],
        $template['subject'],
        $template['body']
    );
}

/**
 * Send OTP email for password reset
 */
function sendOTPEmail($userId, $otp) {
    global $database;

    // Get user details
    $user = $database->fetch(
        "SELECT * FROM users WHERE id = ?",
        [$userId]
    );

    if (!$user || !$user['email']) {
        return false;
    }

    $variables = [
        'app_name' => APP_NAME,
        'user_name' => $user['name'],
        'otp_code' => $otp,
        'contact_phone' => '+255 781 985 757',
        'contact_email' => SMTP_FROM_EMAIL
    ];

    $template = getEmailTemplate('password_reset_otp', $variables);
    if (!$template) {
        return false;
    }

    return sendSMTPEmail(
        $user['email'],
        $template['subject'],
        $template['body']
    );
}

/**
 * Send booking cancellation email
 */
function sendBookingCancellationEmail($bookingId, $reason = '') {
    global $database;

    // Get booking details
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, sv.name as variation_name,
                p.name as package_name,
                st.name as staff_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
         LEFT JOIN packages p ON b.package_id = p.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         WHERE b.id = ?",
        [$bookingId]
    );

    if (!$booking || !$booking['customer_email']) {
        return false;
    }

    // Build service name with variation if applicable
    $serviceName = $booking['service_name'] ?: $booking['package_name'] ?: 'Service';
    if (!empty($booking['variation_name'])) {
        $serviceName .= ' (' . $booking['variation_name'] . ')';
    }

    $subject = 'Booking Cancellation - ' . $serviceName;
    $reasonText = $reason ? "<p><strong>Reason:</strong> $reason</p>" : '';

    $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">' . APP_NAME . '</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Booking Cancellation</p>
            </div>

            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-top: 0;">Hello ' . $booking['customer_name'] . ',</h2>
                <p style="color: #666; line-height: 1.6;">We regret to inform you that your booking has been cancelled:</p>

                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff6b6b;">
                    <h3 style="margin-top: 0; color: #333;">Cancelled Booking Details</h3>
                    <p><strong>Service:</strong> ' . $serviceName . '</p>
                    <p><strong>Date:</strong> ' . formatDate($booking['date']) . '</p>
                    <p><strong>Time:</strong> ' . formatTime($booking['start_time']) . '</p>
                    <p><strong>Staff:</strong> ' . ($booking['staff_name'] ?: 'To be assigned') . '</p>
                    ' . $reasonText . '
                </div>

                <p style="color: #666; line-height: 1.6;">If you would like to reschedule, please contact us or book a new appointment through our website.</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . getBaseUrl() . '/services" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Book New Appointment</a>
                </div>
            </div>

            <div style="background: #333; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0;">' . APP_NAME . ' | +255 781 985 757 | ' . SMTP_FROM_EMAIL . '</p>
            </div>
        </div>
    ';

    return sendSMTPEmail(
        $booking['customer_email'],
        $subject,
        $body
    );
}

/**
 * Send staff notification email
 */
function sendStaffNotificationEmail($staffId, $subject, $message, $bookingId = null) {
    global $database;

    // Get staff details
    $staff = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
        [$staffId]
    );

    if (!$staff || !$staff['email']) {
        return false;
    }

    $bookingLink = $bookingId ?
        '<div style="text-align: center; margin: 30px 0;">
            <a href="' . getBaseUrl() . '/staff/appointments?date=' . date('Y-m-d') . '" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View Appointments</a>
        </div>' : '';

    $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">' . APP_NAME . '</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Staff Notification</p>
            </div>

            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-top: 0;">Hello ' . $staff['name'] . ',</h2>
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                    <p style="color: #666; line-height: 1.6; margin: 0;">' . $message . '</p>
                </div>
                ' . $bookingLink . '
            </div>

            <div style="background: #333; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0;">' . APP_NAME . ' | +255 781 985 757 | ' . SMTP_FROM_EMAIL . '</p>
            </div>
        </div>
    ';

    return sendSMTPEmail(
        $staff['email'],
        $subject,
        $body
    );
}

/**
 * Send staff booking notification email
 */
function sendStaffBookingNotificationEmail($bookingId, $type, $additionalData = []) {
    global $database;

    // Get booking details with staff information
    $booking = $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email,
                s.name as service_name, sv.name as variation_name,
                st.name as staff_name, st.email as staff_email,
                p.name as package_name
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN service_variations sv ON b.service_variation_id = sv.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN packages p ON b.package_id = p.id
         WHERE b.id = ?",
        [$bookingId]
    );

    if (!$booking || !$booking['staff_email']) {
        return false;
    }

    $customerName = $booking['customer_name'];

    // Build service name with variation if applicable
    $serviceName = $booking['service_name'] ?: $booking['package_name'];
    if (!empty($booking['variation_name'])) {
        $serviceName .= ' (' . $booking['variation_name'] . ')';
    }

    // Define notification messages based on type
    $notifications = [
        'BOOKING_NEW' => [
            'subject' => 'New Booking Assignment - ' . APP_NAME,
            'message' => "You have been assigned a new booking with {$customerName} for {$serviceName} on " . formatDate($booking['date']) . " at " . formatTime($booking['start_time']) . "."
        ],
        'BOOKING_CONFIRMED' => [
            'subject' => 'Booking Confirmed - ' . APP_NAME,
            'message' => "Your booking with {$customerName} for {$serviceName} has been confirmed for " . formatDate($booking['date']) . " at " . formatTime($booking['start_time']) . "."
        ],
        'BOOKING_CANCELLED' => [
            'subject' => 'Booking Cancelled - ' . APP_NAME,
            'message' => "The booking with {$customerName} for {$serviceName} on " . formatDate($booking['date']) . " has been cancelled."
        ],
        'BOOKING_COMPLETED' => [
            'subject' => 'Booking Completed - ' . APP_NAME,
            'message' => "The booking with {$customerName} for {$serviceName} has been marked as completed."
        ],
        'BOOKING_REMINDER' => [
            'subject' => 'Upcoming Appointment - ' . APP_NAME,
            'message' => "Reminder: You have an appointment with {$customerName} for {$serviceName} in 30 minutes."
        ],
        'BOOKING_EXPIRED' => [
            'subject' => 'Booking Expired - ' . APP_NAME,
            'message' => "The booking with {$customerName} for {$serviceName} on " . formatDate($booking['date']) . " has expired."
        ],
        'BOOKING_NO_SHOW' => [
            'subject' => 'Customer No-Show - ' . APP_NAME,
            'message' => "Customer {$customerName} did not show up for the {$serviceName} appointment on " . formatDate($booking['date']) . "."
        ]
    ];

    if (!isset($notifications[$type])) {
        return false;
    }

    $notificationData = $notifications[$type];

    return sendStaffNotificationEmail(
        $booking['staff_id'],
        $notificationData['subject'],
        $notificationData['message'],
        $bookingId
    );
}

/**
 * Send welcome email to new customers
 */
function sendWelcomeEmail($userId) {
    global $database;

    // Get user details
    $user = $database->fetch(
        "SELECT * FROM users WHERE id = ? AND role = 'CUSTOMER'",
        [$userId]
    );

    if (!$user || !$user['email']) {
        return false;
    }

    $subject = 'Welcome to ' . APP_NAME . '!';

    $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">' . APP_NAME . '</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Welcome to our family!</p>
            </div>

            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-top: 0;">Hello ' . $user['name'] . ',</h2>
                <p style="color: #666; line-height: 1.6;">Welcome to ' . APP_NAME . '! We\'re excited to have you as part of our community.</p>

                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                    <h3 style="margin-top: 0; color: #333;">What\'s Next?</h3>
                    <ul style="color: #666; line-height: 1.6;">
                        <li>Browse our services and packages</li>
                        <li>Book your first appointment</li>
                        <li>Earn points with every booking</li>
                        <li>Enjoy exclusive member benefits</li>
                    </ul>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . getBaseUrl() . '/services" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">Browse Services</a>
                    <a href="' . getBaseUrl() . '/customer/dashboard" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">My Dashboard</a>
                </div>

                <p style="color: #666; line-height: 1.6;">If you have any questions, feel free to contact us. We\'re here to help!</p>
            </div>

            <div style="background: #333; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0;">' . APP_NAME . ' | +255 781 985 757 | ' . SMTP_FROM_EMAIL . '</p>
            </div>
        </div>
    ';

    return sendSMTPEmail(
        $user['email'],
        $subject,
        $body
    );
}

/**
 * Send contact message reply email
 */
function sendContactReplyEmail($contactMessageId, $replyMessage) {
    global $database;

    // Get contact message details
    $contact = $database->fetch(
        "SELECT * FROM contact_messages WHERE id = ?",
        [$contactMessageId]
    );

    if (!$contact || !$contact['email']) {
        return false;
    }

    $variables = [
        'app_name' => APP_NAME,
        'customer_name' => $contact['name'],
        'original_subject' => $contact['subject'] ?: 'Your inquiry',
        'original_message' => substr($contact['message'], 0, 200) . (strlen($contact['message']) > 200 ? '...' : ''),
        'reply_message' => nl2br(htmlspecialchars($replyMessage)),
        'contact_phone' => '+255 781 985 757', // Make this configurable
        'contact_email' => SMTP_FROM_EMAIL,
        'website_url' => getBaseUrl(),
        'contact_url' => getBaseUrl() . '/contact'
    ];

    $template = getEmailTemplate('contact_reply', $variables);
    if (!$template) {
        return false;
    }

    return sendSMTPEmail(
        $contact['email'],
        $template['subject'],
        $template['body']
    );
}

/**
 * Send general notification email
 */
function sendNotificationEmail($userId, $subject, $message, $actionUrl = null, $actionText = 'View Details') {
    global $database;

    // Get user details
    $user = $database->fetch(
        "SELECT * FROM users WHERE id = ?",
        [$userId]
    );

    if (!$user || !$user['email']) {
        return false;
    }

    $actionButton = $actionUrl ?
        '<div style="text-align: center; margin: 30px 0;">
            <a href="' . $actionUrl . '" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">' . $actionText . '</a>
        </div>' : '';

    $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">' . APP_NAME . '</h1>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">Notification</p>
            </div>

            <div style="padding: 30px; background: #f8f9fa;">
                <h2 style="color: #333; margin-top: 0;">Hello ' . $user['name'] . ',</h2>
                <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                    <p style="color: #666; line-height: 1.6; margin: 0;">' . $message . '</p>
                </div>
                ' . $actionButton . '
            </div>

            <div style="background: #333; color: white; padding: 20px; text-align: center;">
                <p style="margin: 0;">' . APP_NAME . ' | +255 781 985 757 | ' . SMTP_FROM_EMAIL . '</p>
            </div>
        </div>
    ';

    return sendSMTPEmail(
        $user['email'],
        $subject,
        $body
    );
}

/**
 * Send bulk email to multiple recipients
 */
function sendBulkEmail($recipients, $subject, $body, $options = []) {
    $results = [];
    $successCount = 0;
    $failureCount = 0;

    foreach ($recipients as $recipient) {
        $email = is_array($recipient) ? $recipient['email'] : $recipient;
        $name = is_array($recipient) ? $recipient['name'] : '';

        // Personalize the email if name is provided
        $personalizedBody = $name ? str_replace('{{name}}', $name, $body) : $body;

        $result = sendSMTPEmail($email, $subject, $personalizedBody, $options);
        $results[$email] = $result;

        if ($result) {
            $successCount++;
        } else {
            $failureCount++;
        }

        // Add small delay to prevent overwhelming the server
        usleep(100000); // 0.1 second delay
    }

    // Log bulk email activity
    error_log("Bulk email sent: {$successCount} successful, {$failureCount} failed");

    return [
        'success_count' => $successCount,
        'failure_count' => $failureCount,
        'results' => $results
    ];
}

/**
 * Create email log table if it doesn't exist
 */
function createEmailLogTable() {
    global $database;

    $sql = "CREATE TABLE IF NOT EXISTS email_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        recipient VARCHAR(255) NOT NULL,
        subject VARCHAR(500) NOT NULL,
        status ENUM('SENT', 'FAILED', 'PENDING') DEFAULT 'PENDING',
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_recipient (recipient),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    )";

    try {
        $database->query($sql);
        return true;
    } catch (Exception $e) {
        error_log("Failed to create email_logs table: " . $e->getMessage());
        return false;
    }
}

/**
 * Get email statistics
 */
function getEmailStats($days = 30) {
    global $database;

    try {
        $stats = $database->fetch(
            "SELECT
                COUNT(*) as total_emails,
                SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) as sent_emails,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_emails,
                SUM(CASE WHEN status = 'PENDING' THEN 1 ELSE 0 END) as pending_emails
             FROM email_logs
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
            [$days]
        );

        return $stats ?: [
            'total_emails' => 0,
            'sent_emails' => 0,
            'failed_emails' => 0,
            'pending_emails' => 0
        ];
    } catch (Exception $e) {
        error_log("Failed to get email stats: " . $e->getMessage());
        return [
            'total_emails' => 0,
            'sent_emails' => 0,
            'failed_emails' => 0,
            'pending_emails' => 0
        ];
    }
}

/**
 * Test email configuration
 */
function testEmailConfiguration() {
    $testEmail = SMTP_FROM_EMAIL;
    $subject = 'Email Configuration Test - ' . APP_NAME;
    $body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: #28a745; color: white; padding: 20px; text-align: center;">
                <h1 style="margin: 0;">Email Test Successful!</h1>
            </div>
            <div style="padding: 20px;">
                <p>This is a test email to verify that your email configuration is working correctly.</p>
                <p><strong>Timestamp:</strong> ' . date('Y-m-d H:i:s') . '</p>
                <p><strong>Server:</strong> ' . SMTP_HOST . ':' . SMTP_PORT . '</p>
                <p><strong>Using:</strong> PHPMailer with SMTP Authentication</p>
                <p>If you receive this email, your email system is configured correctly!</p>
            </div>
        </div>
    ';

    return sendSMTPEmail($testEmail, $subject, $body);
}

/**
 * Enhanced email function wrapper for backward compatibility
 * This will be used to replace the basic sendEmail in functions.php
 */
function sendEnhancedEmail($to, $subject, $body, $isHtml = true) {
    return sendSMTPEmail($to, $subject, $body, ['is_html' => $isHtml]);
}

// Initialize email log table when this file is included
createEmailLogTable();
