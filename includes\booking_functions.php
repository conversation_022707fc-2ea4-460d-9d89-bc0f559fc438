<?php
/**
 * Booking Management Functions
 * Flix Salonce - PHP Version
 */

/**
 * Create a new booking
 */
function createBooking($data) {
    global $database;

    try {
        // Validate required fields
        $required = ['user_id', 'service_id', 'staff_id', 'date', 'start_time'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                return ['success' => false, 'error' => "Field $field is required"];
            }
        }

        // Calculate end_time if not provided
        if (empty($data['end_time'])) {
            $service = $database->fetch("SELECT duration FROM services WHERE id = ?", [$data['service_id']]);
            if ($service) {
                $startTime = new DateTime($data['start_time']);
                $startTime->add(new DateInterval('PT' . $service['duration'] . 'M'));
                $data['end_time'] = $startTime->format('H:i:s');
            } else {
                return ['success' => false, 'error' => 'Service not found'];
            }
        }

        // Calculate total_amount if not provided
        if (empty($data['total_amount'])) {
            $service = $database->fetch("SELECT price FROM services WHERE id = ?", [$data['service_id']]);
            if ($service) {
                $pointsUsed = intval($data['points_used'] ?? 0);
                $discount = $pointsUsed * 100; // 10 points = TSH 1,000
                $data['total_amount'] = max(0, $service['price'] - $discount);
            } else {
                return ['success' => false, 'error' => 'Service not found'];
            }
        }
        
        // Check if staff is available
        try {
            $isAvailable = checkStaffAvailability($data['staff_id'], $data['date'], $data['start_time'], $data['end_time']);
            if (!$isAvailable) {
                return ['success' => false, 'error' => 'Staff member is not available at the selected time'];
            }
        } catch (Exception $e) {
            // If staff availability check fails, log it but continue (for now)
            error_log("Staff availability check failed: " . $e->getMessage());
        }
        
        // Calculate points earned (1 point per TSH 1,000 spent)
        $pointsUsed = intval($data['points_used'] ?? 0);
        $pointsEarned = max(0, floor(($data['total_amount'] - ($pointsUsed * 10)) / 1000));
        
        $bookingId = generateUUID();

        // Prepare booking data
        $bookingData = [
            $bookingId,
            $data['user_id'],
            $data['service_id'],
            $data['package_id'] ?? null,
            $data['staff_id'],
            $data['date'],
            $data['start_time'],
            $data['end_time'],
            floatval($data['total_amount']),
            $pointsUsed,
            $pointsEarned,
            sanitize($data['notes'] ?? '')
        ];

        // Insert booking
        $result = $database->query(
            "INSERT INTO bookings (id, user_id, service_id, package_id, staff_id, date, start_time, end_time,
                                 total_amount, points_used, points_earned, notes, status, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'PENDING', NOW(), NOW())",
            $bookingData
        );

        if (!$result) {
            return ['success' => false, 'error' => 'Failed to insert booking into database'];
        }
        
        // Update user points
        if ($pointsEarned > 0 || $pointsUsed > 0) {
            $pointsChange = $pointsEarned - $pointsUsed;
            try {
                $database->query(
                    "UPDATE users SET points = points + ? WHERE id = ?",
                    [$pointsChange, $data['user_id']]
                );
            } catch (Exception $e) {
                error_log("Failed to update user points: " . $e->getMessage());
                // Don't fail the booking creation for points update failure
            }
        }

        // Create notification for new booking with email integration
        require_once __DIR__ . '/notification_triggers.php';
        createBookingNotificationWithEmail($bookingId, 'BOOKING_NEW');

        // Schedule reminders for the new booking
        scheduleBookingReminders($bookingId);

        return ['success' => true, 'id' => $bookingId];
        
    } catch (Exception $e) {
        error_log("Booking creation error: " . $e->getMessage());
        error_log("Booking data: " . print_r($data, true));
        return ['success' => false, 'error' => 'Failed to create booking: ' . $e->getMessage()];
    }
}

/**
 * Update booking status
 */
function updateBookingStatus($bookingId, $status, $adminId = null) {
    global $database;

    try {
        $validStatuses = ['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'EXPIRED'];
        if (!in_array($status, $validStatuses)) {
            return ['success' => false, 'error' => 'Invalid status'];
        }

        // Get current booking
        $booking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$booking) {
            return ['success' => false, 'error' => 'Booking not found'];
        }

        $oldStatus = $booking['status'];

        // Validate status transition
        if (!isValidStatusTransition($oldStatus, $status)) {
            return ['success' => false, 'error' => "Invalid status transition from {$oldStatus} to {$status}"];
        }

        $database->beginTransaction();

        // Update booking status
        $database->query(
            "UPDATE bookings SET status = ?, updated_at = NOW() WHERE id = ?",
            [$status, $bookingId]
        );

        // Handle points based on status change
        handlePointsForStatusChange($booking, $oldStatus, $status);

        // Log the status change if booking_status_log table exists
        try {
            $database->query(
                "INSERT INTO booking_status_log (id, booking_id, old_status, new_status, changed_by, created_at)
                 VALUES (?, ?, ?, ?, ?, NOW())",
                [generateUUID(), $bookingId, $oldStatus, $status, $adminId]
            );
        } catch (Exception $e) {
            // Ignore if table doesn't exist
        }

        $database->commit();

        // Create notification for status change (non-blocking)
        try {
            require_once __DIR__ . '/notification_triggers.php';

            switch ($status) {
                case 'CONFIRMED':
                    createBookingNotificationWithEmail($bookingId, 'BOOKING_CONFIRMED');
                    sendStaffBookingNotificationEmail($bookingId, 'BOOKING_CONFIRMED');
                    // Schedule reminders when booking is confirmed
                    scheduleBookingReminders($bookingId);
                    break;
                case 'COMPLETED':
                    createBookingNotificationWithEmail($bookingId, 'BOOKING_COMPLETED');
                    sendStaffBookingNotificationEmail($bookingId, 'BOOKING_COMPLETED');
                    // Cancel any pending reminders
                    cancelBookingReminders($bookingId, 'Booking completed');
                    break;
                case 'CANCELLED':
                    createBookingNotificationWithEmail($bookingId, 'BOOKING_CANCELLED');
                    sendStaffBookingNotificationEmail($bookingId, 'BOOKING_CANCELLED');
                    // Cancel any pending reminders
                    cancelBookingReminders($bookingId, 'Booking cancelled');
                    break;
                case 'NO_SHOW':
                    createBookingNotificationWithEmail($bookingId, 'BOOKING_NO_SHOW');
                    sendStaffBookingNotificationEmail($bookingId, 'BOOKING_NO_SHOW');
                    // Cancel any pending reminders
                    cancelBookingReminders($bookingId, 'Customer no-show');
                    break;
                case 'EXPIRED':
                    createBookingNotificationWithEmail($bookingId, 'BOOKING_EXPIRED');
                    sendStaffBookingNotificationEmail($bookingId, 'BOOKING_EXPIRED');
                    // Cancel any pending reminders
                    cancelBookingReminders($bookingId, 'Booking expired');
                    break;
            }
        } catch (Exception $notificationError) {
            // Log notification errors but don't fail the status update
            error_log("Notification error for booking $bookingId: " . $notificationError->getMessage());
        }

        return ['success' => true];

    } catch (Exception $e) {
        // Only rollback if transaction is still active
        try {
            $database->rollback();
        } catch (Exception $rollbackError) {
            error_log("Rollback error: " . $rollbackError->getMessage());
        }
        error_log("Booking status update error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update booking status'];
    }
}

/**
 * Check staff availability
 */
function checkStaffAvailability($staffId, $date, $startTime, $endTime, $excludeBookingId = null) {
    global $database;
    
    try {
        $whereClause = "WHERE staff_id = ? AND date = ? AND status NOT IN ('CANCELLED', 'NO_SHOW') 
                       AND ((start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?) 
                       OR (start_time >= ? AND end_time <= ?))";
        $params = [$staffId, $date, $endTime, $startTime, $startTime, $endTime, $startTime, $endTime];
        
        if ($excludeBookingId) {
            $whereClause .= " AND id != ?";
            $params[] = $excludeBookingId;
        }
        
        $conflictingBookings = $database->fetch(
            "SELECT COUNT(*) as count FROM bookings $whereClause",
            $params
        )['count'];
        
        return $conflictingBookings === 0;
        
    } catch (Exception $e) {
        error_log("Staff availability check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get booking statistics
 */
function getBookingStats() {
    global $database;
    
    $stats = [];
    
    // Total bookings
    $stats['total'] = $database->fetch("SELECT COUNT(*) as count FROM bookings")['count'];
    
    // Bookings by status
    $statusCounts = $database->fetchAll(
        "SELECT status, COUNT(*) as count FROM bookings GROUP BY status"
    );
    
    $stats['pending'] = 0;
    $stats['confirmed'] = 0;
    $stats['completed'] = 0;
    $stats['cancelled'] = 0;
    $stats['no_show'] = 0;
    
    foreach ($statusCounts as $statusCount) {
        $stats[strtolower($statusCount['status'])] = $statusCount['count'];
    }
    
    // Today's revenue
    $stats['today_revenue'] = $database->fetch(
        "SELECT COALESCE(SUM(total_amount), 0) as revenue 
         FROM bookings 
         WHERE DATE(date) = CURDATE() AND status = 'COMPLETED'"
    )['revenue'];
    
    // This month's revenue
    $stats['month_revenue'] = $database->fetch(
        "SELECT COALESCE(SUM(total_amount), 0) as revenue 
         FROM bookings 
         WHERE YEAR(date) = YEAR(CURDATE()) AND MONTH(date) = MONTH(CURDATE()) AND status = 'COMPLETED'"
    )['revenue'];
    
    // Upcoming bookings (next 7 days)
    $stats['upcoming'] = $database->fetch(
        "SELECT COUNT(*) as count 
         FROM bookings 
         WHERE date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) 
         AND status IN ('PENDING', 'CONFIRMED')"
    )['count'];
    
    return $stats;
}

/**
 * Get detailed booking analytics for a date range
 */
function getBookingAnalytics($startDate, $endDate) {
    global $database;

    $analytics = [];

    // Total revenue and bookings
    $totals = $database->fetch(
        "SELECT
            COUNT(*) as total_bookings,
            COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_revenue,
            COALESCE(AVG(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE NULL END), 0) as avg_booking_value
         FROM bookings
         WHERE date BETWEEN ? AND ?",
        [$startDate, $endDate]
    );

    $analytics['total_bookings'] = $totals['total_bookings'];
    $analytics['total_revenue'] = $totals['total_revenue'];
    $analytics['avg_booking_value'] = $totals['avg_booking_value'];

    // Completion rate
    $completedBookings = $database->fetch(
        "SELECT COUNT(*) as completed FROM bookings WHERE date BETWEEN ? AND ? AND status = 'COMPLETED'",
        [$startDate, $endDate]
    )['completed'];

    $analytics['completion_rate'] = $totals['total_bookings'] > 0 ?
        ($completedBookings / $totals['total_bookings']) * 100 : 0;

    // Top services
    $analytics['top_services'] = $database->fetchAll(
        "SELECT s.name, COUNT(b.id) as booking_count,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as revenue
         FROM services s
         LEFT JOIN bookings b ON s.id = b.service_id AND b.date BETWEEN ? AND ?
         GROUP BY s.id, s.name
         ORDER BY booking_count DESC, revenue DESC
         LIMIT 5",
        [$startDate, $endDate]
    );

    // Top staff
    $analytics['top_staff'] = $database->fetchAll(
        "SELECT u.name, COUNT(b.id) as booking_count,
                COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as revenue
         FROM users u
         LEFT JOIN bookings b ON u.id = b.staff_id AND b.date BETWEEN ? AND ?
         WHERE u.role = 'STAFF'
         GROUP BY u.id, u.name
         ORDER BY booking_count DESC, revenue DESC
         LIMIT 5",
        [$startDate, $endDate]
    );

    // Status distribution
    $statusData = $database->fetchAll(
        "SELECT status, COUNT(*) as count
         FROM bookings
         WHERE date BETWEEN ? AND ?
         GROUP BY status",
        [$startDate, $endDate]
    );

    $analytics['status_distribution'] = [];
    foreach ($statusData as $status) {
        $analytics['status_distribution'][] = [
            'status' => $status['status'],
            'count' => $status['count'],
            'percentage' => $totals['total_bookings'] > 0 ?
                ($status['count'] / $totals['total_bookings']) * 100 : 0
        ];
    }

    // Daily trend
    $analytics['daily_trend'] = $database->fetchAll(
        "SELECT date, COUNT(*) as booking_count,
                COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as revenue
         FROM bookings
         WHERE date BETWEEN ? AND ?
         GROUP BY date
         ORDER BY date DESC
         LIMIT 14",
        [$startDate, $endDate]
    );

    return $analytics;
}

/**
 * Perform bulk operations on bookings
 */
function performBulkOperation($action, $bookingIds) {
    global $database;

    try {
        $successCount = 0;
        $errorCount = 0;

        foreach ($bookingIds as $bookingId) {
            switch ($action) {
                case 'confirm':
                    $result = updateBookingStatus($bookingId, 'CONFIRMED');
                    break;
                case 'complete':
                    $result = updateBookingStatus($bookingId, 'COMPLETED');
                    break;
                case 'cancel':
                    $result = updateBookingStatus($bookingId, 'CANCELLED');
                    break;
                case 'send_reminder':
                    $result = sendBookingReminder($bookingId);
                    break;
                case 'export':
                    // Export functionality would be implemented here
                    $result = ['success' => true];
                    break;
                default:
                    $result = ['success' => false, 'error' => 'Invalid action'];
            }

            if ($result['success']) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        if ($successCount > 0 && $errorCount === 0) {
            return [
                'success' => true,
                'message' => "Successfully processed {$successCount} booking(s)."
            ];
        } elseif ($successCount > 0 && $errorCount > 0) {
            return [
                'success' => true,
                'message' => "Processed {$successCount} booking(s) successfully, {$errorCount} failed."
            ];
        } else {
            return [
                'success' => false,
                'error' => "Failed to process {$errorCount} booking(s)."
            ];
        }

    } catch (Exception $e) {
        error_log("Bulk operation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to perform bulk operation'];
    }
}

/**
 * Send booking reminder
 */
function sendBookingReminder($bookingId) {
    global $database;

    try {
        $booking = getBookingById($bookingId);
        if (!$booking) {
            return ['success' => false, 'error' => 'Booking not found'];
        }

        // Send email reminder using the new email system
        $emailResult = sendBookingReminderEmail($bookingId);

        if ($emailResult) {
            // Create notification for reminder sent
            createBookingNotificationWithEmail($bookingId, 'BOOKING_REMINDER');
            return ['success' => true, 'message' => 'Reminder sent successfully'];
        } else {
            return ['success' => false, 'error' => 'Failed to send email reminder'];
        }

    } catch (Exception $e) {
        error_log("Reminder sending error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to send reminder'];
    }
}

/**
 * Get available time slots for a staff member on a specific date
 */
function getAvailableTimeSlots($staffId, $date, $serviceDuration = 60) {
    global $database;
    
    try {
        // Get staff working hours (assuming 9 AM to 6 PM for now)
        $workStart = '09:00:00';
        $workEnd = '18:00:00';
        
        // Get existing bookings for the staff on this date
        $existingBookings = $database->fetchAll(
            "SELECT start_time, end_time FROM bookings 
             WHERE staff_id = ? AND date = ? AND status NOT IN ('CANCELLED', 'NO_SHOW')
             ORDER BY start_time",
            [$staffId, $date]
        );
        
        // Generate time slots
        $slots = [];
        $currentTime = strtotime($workStart);
        $endTime = strtotime($workEnd);
        $slotDuration = $serviceDuration * 60; // Convert to seconds
        
        while ($currentTime + $slotDuration <= $endTime) {
            $slotStart = date('H:i:s', $currentTime);
            $slotEnd = date('H:i:s', $currentTime + $slotDuration);
            
            // Check if this slot conflicts with existing bookings
            $isAvailable = true;
            foreach ($existingBookings as $booking) {
                if (($slotStart < $booking['end_time'] && $slotEnd > $booking['start_time'])) {
                    $isAvailable = false;
                    break;
                }
            }
            
            if ($isAvailable) {
                $slots[] = [
                    'start' => $slotStart,
                    'end' => $slotEnd,
                    'display' => date('g:i A', $currentTime) . ' - ' . date('g:i A', $currentTime + $slotDuration)
                ];
            }
            
            $currentTime += 30 * 60; // 30-minute intervals
        }
        
        return $slots;
        
    } catch (Exception $e) {
        error_log("Time slots error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get booking by ID with related data
 */
function getBookingById($bookingId) {
    global $database;

    return $database->fetch(
        "SELECT b.*, u.name as customer_name, u.email as customer_email, u.phone as customer_phone,
                s.name as service_name, s.price as service_price, s.duration as service_duration,
                st.name as staff_name, st.email as staff_email,
                p.name as package_name, p.price as package_price
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN packages p ON b.package_id = p.id
         WHERE b.id = ?",
        [$bookingId]
    );
}

/**
 * Get user's booking history
 */
function getUserBookings($userId, $limit = 10, $offset = 0) {
    global $database;

    return $database->fetchAll(
        "SELECT b.*, s.name as service_name, s.duration as service_duration,
                st.name as staff_name, p.name as package_name
         FROM bookings b
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN packages p ON b.package_id = p.id
         WHERE b.user_id = ?
         ORDER BY b.date DESC, b.start_time DESC
         LIMIT ? OFFSET ?",
        [$userId, $limit, $offset]
    );
}

/**
 * Delete booking
 */
function deleteBooking($bookingId) {
    global $database;

    try {
        // Get booking details first
        $booking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$booking) {
            return ['success' => false, 'error' => 'Booking not found'];
        }

        // Check if booking can be deleted (only allow deletion of cancelled or no-show bookings)
        if (!in_array($booking['status'], ['CANCELLED', 'NO_SHOW'])) {
            return ['success' => false, 'error' => 'Only cancelled or no-show bookings can be deleted'];
        }

        // Delete the booking
        $database->query("DELETE FROM bookings WHERE id = ?", [$bookingId]);

        return ['success' => true];

    } catch (Exception $e) {
        error_log("Booking deletion error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to delete booking'];
    }
}

/**
 * Cancel booking
 */
function cancelBooking($bookingId, $reason = '') {
    global $database;
    
    try {
        // Get booking details
        $booking = $database->fetch("SELECT * FROM bookings WHERE id = ?", [$bookingId]);
        if (!$booking) {
            return ['success' => false, 'error' => 'Booking not found'];
        }
        
        // Check if booking can be cancelled (not already completed)
        if ($booking['status'] === 'COMPLETED') {
            return ['success' => false, 'error' => 'Cannot cancel completed booking'];
        }
        
        // Update booking status
        $database->query(
            "UPDATE bookings SET status = 'CANCELLED', notes = CONCAT(COALESCE(notes, ''), '\nCancellation reason: ', ?), updated_at = NOW() WHERE id = ?",
            [$reason, $bookingId]
        );
        
        // Refund points if they were used
        if ($booking['points_used'] > 0) {
            $database->query(
                "UPDATE users SET points = points + ? WHERE id = ?",
                [$booking['points_used'], $booking['user_id']]
            );
        }
        
        return ['success' => true];
        
    } catch (Exception $e) {
        error_log("Booking cancellation error: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to cancel booking'];
    }
}

/**
 * Handle points when booking status changes
 */
function handlePointsForStatusChange($booking, $oldStatus, $newStatus) {
    global $database;

    // Award points when booking is completed
    if ($newStatus === 'COMPLETED' && $oldStatus !== 'COMPLETED') {
        awardPointsForCompletedBooking($booking['id']);
    }

    // Handle cancellation - refund used points, remove earned points
    if ($newStatus === 'CANCELLED' && $oldStatus !== 'CANCELLED') {
        handleBookingCancellation($booking);
    }

    // Handle no-show - similar to cancellation but different transaction type
    if ($newStatus === 'NO_SHOW' && $oldStatus !== 'NO_SHOW') {
        handleBookingNoShow($booking);
    }

    // Handle expiration - remove earned points, refund used points
    if ($newStatus === 'EXPIRED' && $oldStatus !== 'EXPIRED') {
        handleBookingExpiration($booking);
    }
}

/**
 * Award points when booking is completed
 */
function awardPointsForCompletedBooking($bookingId) {
    global $database;

    try {
        // Get booking details
        $booking = $database->fetch("
            SELECT user_id, total_amount, points_earned, status, service_id, package_id
            FROM bookings
            WHERE id = ? AND status = 'COMPLETED'
        ", [$bookingId]);

        if (!$booking) {
            throw new Exception("Booking not found or not completed");
        }

        // Calculate points to earn (1 point per TSH 10 spent)
        $pointsToEarn = floor($booking['total_amount'] / 10);

        if ($pointsToEarn <= 0) {
            return 0;
        }

        // Update booking with earned points
        $database->query("
            UPDATE bookings
            SET points_earned = ?, updated_at = NOW()
            WHERE id = ?
        ", [$pointsToEarn, $bookingId]);

        // Add points to user account
        $database->query("
            UPDATE users
            SET points = points + ?, updated_at = NOW()
            WHERE id = ?
        ", [$pointsToEarn, $booking['user_id']]);

        // Record point transaction
        try {
            $database->query("
                INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
                VALUES (?, ?, ?, ?, 'EARNED', 'Points earned from completed booking', NOW())
            ", [
                generateUUID(),
                $booking['user_id'],
                $bookingId,
                $pointsToEarn
            ]);
        } catch (Exception $e) {
            // Fallback for tables without booking_id column
            if (strpos($e->getMessage(), "Unknown column 'booking_id'") !== false) {
                error_log("Point transactions table missing booking_id column, using fallback for points award");
                $database->query("
                    INSERT INTO point_transactions (id, user_id, points, type, description, created_at)
                    VALUES (?, ?, ?, 'EARNED', 'Points earned from completed booking', NOW())
                ", [
                    generateUUID(),
                    $booking['user_id'],
                    $pointsToEarn
                ]);
            } else {
                throw $e; // Re-throw if it's a different error
            }
        }

        return $pointsToEarn;

    } catch (Exception $e) {
        error_log("Points award error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Handle booking cancellation
 */
function handleBookingCancellation($booking) {
    global $database;

    // Refund used points
    if ($booking['points_used'] > 0) {
        $database->query("
            UPDATE users
            SET points = points + ?, updated_at = NOW()
            WHERE id = ?
        ", [$booking['points_used'], $booking['user_id']]);

        $database->query("
            INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
            VALUES (?, ?, ?, ?, 'REFUND', 'Points refunded due to booking cancellation', NOW())
        ", [
            generateUUID(),
            $booking['user_id'],
            $booking['id'],
            $booking['points_used']
        ]);
    }

    // Remove earned points (if any were incorrectly awarded)
    if ($booking['points_earned'] > 0) {
        $database->query("
            UPDATE users
            SET points = GREATEST(0, points - ?), updated_at = NOW()
            WHERE id = ?
        ", [$booking['points_earned'], $booking['user_id']]);

        $database->query("
            INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
            VALUES (?, ?, ?, ?, 'DEDUCTION', 'Points removed due to booking cancellation', NOW())
        ", [
            generateUUID(),
            $booking['user_id'],
            $booking['id'],
            -$booking['points_earned']
        ]);

        // Update booking to remove earned points
        $database->query("
            UPDATE bookings
            SET points_earned = 0, updated_at = NOW()
            WHERE id = ?
        ", [$booking['id']]);
    }
}

/**
 * Handle booking no-show
 */
function handleBookingNoShow($booking) {
    global $database;

    // For no-show, we don't refund used points (penalty)
    // But we remove any earned points
    if ($booking['points_earned'] > 0) {
        $database->query("
            UPDATE users
            SET points = GREATEST(0, points - ?), updated_at = NOW()
            WHERE id = ?
        ", [$booking['points_earned'], $booking['user_id']]);

        $database->query("
            INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
            VALUES (?, ?, ?, ?, 'DEDUCTION', 'Points removed due to no-show', NOW())
        ", [
            generateUUID(),
            $booking['user_id'],
            $booking['id'],
            -$booking['points_earned']
        ]);

        // Update booking to remove earned points
        $database->query("
            UPDATE bookings
            SET points_earned = 0, updated_at = NOW()
            WHERE id = ?
        ", [$booking['id']]);
    }
}

/**
 * Handle booking expiration
 */
function handleBookingExpiration($booking) {
    global $database;

    // Refund used points
    if ($booking['points_used'] > 0) {
        $database->query("
            UPDATE users
            SET points = points + ?, updated_at = NOW()
            WHERE id = ?
        ", [$booking['points_used'], $booking['user_id']]);

        $database->query("
            INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
            VALUES (?, ?, ?, ?, 'REFUND', 'Points refunded due to booking expiration', NOW())
        ", [
            generateUUID(),
            $booking['user_id'],
            $booking['id'],
            $booking['points_used']
        ]);
    }

    // Remove earned points (if any were incorrectly awarded)
    if ($booking['points_earned'] > 0) {
        $database->query("
            UPDATE users
            SET points = GREATEST(0, points - ?), updated_at = NOW()
            WHERE id = ?
        ", [$booking['points_earned'], $booking['user_id']]);

        $database->query("
            INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
            VALUES (?, ?, ?, ?, 'DEDUCTION', 'Points removed due to booking expiration', NOW())
        ", [
            generateUUID(),
            $booking['user_id'],
            $booking['id'],
            -$booking['points_earned']
        ]);

        // Update booking to remove earned points
        $database->query("
            UPDATE bookings
            SET points_earned = 0, updated_at = NOW()
            WHERE id = ?
        ", [$booking['id']]);
    }
}

/**
 * Validate status transitions
 */
function isValidStatusTransition($oldStatus, $newStatus) {
    $validTransitions = [
        'PENDING' => ['CONFIRMED', 'CANCELLED', 'EXPIRED'],
        'CONFIRMED' => ['IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'EXPIRED'],
        'IN_PROGRESS' => ['COMPLETED', 'CANCELLED'],
        'COMPLETED' => [], // Completed bookings cannot be changed
        'CANCELLED' => [], // Cancelled bookings cannot be changed
        'NO_SHOW' => [], // No-show bookings cannot be changed
        'EXPIRED' => [] // Expired bookings cannot be changed
    ];

    return in_array($newStatus, $validTransitions[$oldStatus] ?? []);
}
?>
