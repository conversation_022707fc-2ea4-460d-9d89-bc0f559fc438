<?php
/**
 * Get Package API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../../config/app.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get package ID from query parameter
$packageId = $_GET['id'] ?? '';

if (empty($packageId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Package ID is required']);
    exit;
}

try {
    $package = getPackageById($packageId);
    
    if (!$package) {
        http_response_code(404);
        echo json_encode(['error' => 'Package not found']);
        exit;
    }
    
    // Get package services (both catalog and manual)
    $services = getPackageServices($packageId);
    
    // Format the response to include services with their types
    $package['services'] = $services;
    
    // Return package data
    echo json_encode($package);
    
} catch (Exception $e) {
    error_log("Get package API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>