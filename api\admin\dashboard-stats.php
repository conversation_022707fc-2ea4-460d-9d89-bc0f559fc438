<?php
/**
 * Admin Dashboard Stats API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication and admin role
if (!$auth->isAuthenticated() || !$auth->hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

try {
    $stats = [];

    // Total customers
    $stats['customers'] = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'CUSTOMER'")['count'];

    // Total bookings
    $stats['bookings'] = $database->fetch("SELECT COUNT(*) as count FROM bookings")['count'];

    // Total revenue
    $stats['revenue'] = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE status = 'COMPLETED'")['total'] ?? 0;

    // Pending bookings
    $stats['pending_bookings'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE status = 'PENDING'")['count'];

    // Today's bookings
    $stats['today_bookings'] = $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE DATE(date) = CURDATE()")['count'];

    // Today's revenue
    $stats['today_revenue'] = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE DATE(date) = CURDATE() AND status = 'COMPLETED'")['total'] ?? 0;

    // This month's revenue
    $stats['month_revenue'] = $database->fetch("SELECT SUM(total_amount) as total FROM bookings WHERE MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE()) AND status = 'COMPLETED'")['total'] ?? 0;

    // Active services
    $stats['active_services'] = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];

    // Active staff
    $stats['active_staff'] = $database->fetch("SELECT COUNT(*) as count FROM staff WHERE is_active = 1")['count'];

    // Recent activity
    $stats['recent_bookings'] = $database->fetchAll("
        SELECT b.*, u.name as customer_name, s.name as service_name
        FROM bookings b
        LEFT JOIN users u ON b.user_id = u.id
        LEFT JOIN services s ON b.service_id = s.id
        ORDER BY b.created_at DESC
        LIMIT 5
    ");

    // Monthly revenue trend (last 6 months)
    $stats['monthly_revenue'] = $database->fetchAll("
        SELECT 
            DATE_FORMAT(date, '%Y-%m') as month,
            SUM(total_amount) as revenue,
            COUNT(*) as bookings
        FROM bookings 
        WHERE status = 'COMPLETED' 
        AND date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(date, '%Y-%m')
        ORDER BY month
    ");

    // Booking status distribution
    $stats['booking_status'] = $database->fetchAll("
        SELECT status, COUNT(*) as count
        FROM bookings
        GROUP BY status
    ");

    // Top services
    $stats['top_services'] = $database->fetchAll("
        SELECT s.name, COUNT(b.id) as booking_count, SUM(b.total_amount) as revenue
        FROM services s
        LEFT JOIN bookings b ON s.id = b.service_id AND b.status = 'COMPLETED'
        WHERE s.is_active = 1
        GROUP BY s.id, s.name
        ORDER BY booking_count DESC
        LIMIT 5
    ");

    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch dashboard stats'
    ]);
}
?>
