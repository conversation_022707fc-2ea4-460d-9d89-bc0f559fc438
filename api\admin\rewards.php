<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/rewards_functions.php';

// Check authentication and admin role
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

try {
    switch ($method) {
        case 'GET':
            handleGetRewards();
            break;
            
        case 'POST':
            handleRewardsAction();
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetRewards() {
    $action = $_GET['action'] ?? 'dashboard';
    
    switch ($action) {
        case 'dashboard':
            $data = getRewardsData();
            break;
            
        case 'settings':
            $data = getRewardSettings();
            break;
            
        case 'tiers':
            $data = getCustomerTiers();
            break;
            
        case 'statistics':
            $data = getRewardsStatistics();
            break;
            
        case 'transactions':
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
            $data = getRecentPointTransactions($limit);
            break;
            
        case 'tier-distribution':
            $data = getTierDistribution();
            break;

        case 'reward':
            $id = $_GET['id'] ?? '';
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['error' => 'Reward ID is required']);
                return;
            }
            $data = getReward($id);
            if (!$data) {
                http_response_code(404);
                echo json_encode(['error' => 'Reward not found']);
                return;
            }
            echo json_encode(['success' => true, 'reward' => $data]);
            return;

        case 'tier':
            $id = $_GET['id'] ?? '';
            if (empty($id)) {
                http_response_code(400);
                echo json_encode(['error' => 'Tier ID is required']);
                return;
            }

            // Debug logging
            error_log("API: Fetching tier with ID: $id");

            try {
                $data = getCustomerTierById($id);
                error_log("API: Tier data result: " . json_encode($data));

                if (!$data) {
                    http_response_code(404);
                    echo json_encode(['error' => 'Customer tier not found', 'tier_id' => $id]);
                    return;
                }
                echo json_encode(['success' => true, 'tier' => $data]);
                return;
            } catch (Exception $e) {
                error_log("API: Error fetching tier: " . $e->getMessage());
                http_response_code(500);
                echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
                return;
            }

        case 'rewards':
            $data = getRewardRedemptions();
            echo json_encode(['success' => true, 'rewards' => $data]);
            return;

        default:
            $data = getRewardsData();
            break;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
}

function handleRewardsAction() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'update_settings':
            handleUpdateSettings($input);
            break;
            
        case 'award_points':
            handleAwardPoints($input);
            break;
            
        case 'redeem_points':
            handleRedeemPoints($input);
            break;
            
        case 'calculate_points':
            handleCalculatePoints($input);
            break;

        case 'create_reward':
            handleCreateReward($input);
            break;

        case 'update_reward':
            handleUpdateReward($input);
            break;

        case 'delete_reward':
            handleDeleteReward($input);
            break;

        case 'create_tier':
            handleCreateTier($input);
            break;

        case 'update_tier':
            handleUpdateTier($input);
            break;

        case 'delete_tier':
            handleDeleteTier($input);
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handleUpdateSettings($input) {
    $requiredFields = ['pointsPerDollar', 'welcomeBonus', 'referralBonus', 'birthdayBonus', 'reviewBonus', 'minimumRedemption'];
    
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $settings = [
        'pointsPerDollar' => floatval($input['pointsPerDollar']),
        'welcomeBonus' => intval($input['welcomeBonus']),
        'referralBonus' => intval($input['referralBonus']),
        'birthdayBonus' => intval($input['birthdayBonus']),
        'reviewBonus' => intval($input['reviewBonus']),
        'minimumRedemption' => intval($input['minimumRedemption'])
    ];
    
    // Validate values
    if ($settings['pointsPerDollar'] < 0 || $settings['minimumRedemption'] < 1) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid settings values']);
        return;
    }
    
    updateRewardSettings($settings);
    
    echo json_encode([
        'success' => true,
        'message' => 'Reward settings updated successfully',
        'data' => $settings
    ]);
}

function handleAwardPoints($input) {
    $requiredFields = ['customerId', 'points', 'description'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $customerId = $input['customerId'];
    $points = intval($input['points']);
    $description = trim($input['description']);
    
    if ($points <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Points must be greater than 0']);
        return;
    }
    
    awardPoints($customerId, $points, $description);
    
    echo json_encode([
        'success' => true,
        'message' => 'Points awarded successfully',
        'data' => [
            'customerId' => $customerId,
            'points' => $points,
            'description' => $description
        ]
    ]);
}

function handleRedeemPoints($input) {
    $requiredFields = ['customerId', 'points', 'description'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $customerId = $input['customerId'];
    $points = intval($input['points']);
    $description = trim($input['description']);
    
    if ($points <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Points must be greater than 0']);
        return;
    }
    
    redeemPoints($customerId, $points, $description);
    
    echo json_encode([
        'success' => true,
        'message' => 'Points redeemed successfully',
        'data' => [
            'customerId' => $customerId,
            'points' => $points,
            'description' => $description
        ]
    ]);
}

function handleCalculatePoints($input) {
    $amount = floatval($input['amount'] ?? 0);
    $customerId = $input['customerId'] ?? null;
    
    if ($amount <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Amount must be greater than 0']);
        return;
    }
    
    $points = calculatePointsForBooking($amount, $customerId);
    
    $response = [
        'success' => true,
        'data' => [
            'amount' => $amount,
            'points' => $points,
            'customerId' => $customerId
        ]
    ];
    
    if ($customerId) {
        $totalSpent = getTotalSpentByCustomer($customerId);
        $tier = getCustomerTier($totalSpent);
        $response['data']['tier'] = $tier;
        $response['data']['totalSpent'] = $totalSpent;
    }
    
    echo json_encode($response);
}

// Additional endpoints for specific data
if (isset($_GET['endpoint'])) {
    handleSpecificEndpoint($_GET['endpoint']);
}

function handleSpecificEndpoint($endpoint) {
    switch ($endpoint) {
        case 'customer-tier':
            $customerId = $_GET['customer_id'] ?? '';
            if (empty($customerId)) {
                http_response_code(400);
                echo json_encode(['error' => 'Customer ID is required']);
                return;
            }
            
            $totalSpent = getTotalSpentByCustomer($customerId);
            $tier = getCustomerTier($totalSpent);
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'customerId' => $customerId,
                    'totalSpent' => $totalSpent,
                    'tier' => $tier
                ]
            ]);
            break;
            
        case 'points-value':
            $points = intval($_GET['points'] ?? 0);
            $settings = getRewardSettings();
            
            // Assuming 1 point = TSH 10 value (can be configured)
            $value = $points * 10; // 1 point = TSH 10
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'points' => $points,
                    'value' => $value,
                    'minimumRedemption' => $settings['minimumRedemption']
                ]
            ]);
            break;
            
        case 'loyalty-summary':
            $stats = getRewardsStatistics();
            $settings = getRewardSettings();
            $tiers = getCustomerTiers();
            
            echo json_encode([
                'success' => true,
                'data' => [
                    'statistics' => $stats,
                    'settings' => $settings,
                    'tiers' => $tiers,
                    'summary' => [
                        'totalValue' => $stats['totalPointsIssued'] * 10,
                        'redeemedValue' => $stats['totalPointsRedeemed'] * 10,
                        'outstandingValue' => ($stats['totalPointsIssued'] - $stats['totalPointsRedeemed']) * 10
                    ]
                ]
            ]);
            break;
            
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

function handleCreateReward($input) {
    $requiredFields = ['name', 'description', 'points_cost', 'value', 'type'];

    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }

    $rewardData = [
        'name' => trim($input['name']),
        'description' => trim($input['description']),
        'points_required' => intval($input['points_cost']),
        'value' => floatval($input['value']),
        'type' => $input['type'],
        'is_active' => $input['is_active'] ?? 1
    ];

    // Validate values
    if ($rewardData['points_required'] <= 0 || $rewardData['value'] <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Points cost and value must be greater than 0']);
        return;
    }

    $rewardId = createReward($rewardData);

    if ($rewardId) {
        echo json_encode([
            'success' => true,
            'message' => 'Reward created successfully',
            'reward_id' => $rewardId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create reward']);
    }
}

function handleUpdateReward($input) {
    $id = $input['id'] ?? '';
    if (empty($id)) {
        http_response_code(400);
        echo json_encode(['error' => 'Reward ID is required']);
        return;
    }

    $requiredFields = ['name', 'description', 'points_cost', 'value', 'type'];

    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }

    $rewardData = [
        'name' => trim($input['name']),
        'description' => trim($input['description']),
        'points_required' => intval($input['points_cost']),
        'value' => floatval($input['value']),
        'type' => $input['type'],
        'is_active' => $input['is_active'] ?? 1
    ];

    // Validate values
    if ($rewardData['points_required'] <= 0 || $rewardData['value'] <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Points cost and value must be greater than 0']);
        return;
    }

    $result = updateReward($id, $rewardData);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Reward updated successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update reward']);
    }
}

function handleDeleteReward($input) {
    $id = $input['id'] ?? '';
    if (empty($id)) {
        http_response_code(400);
        echo json_encode(['error' => 'Reward ID is required']);
        return;
    }

    $result = deleteReward($id);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Reward deleted successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete reward']);
    }
}

function handleCreateTier($input) {
    $requiredFields = ['name', 'min_spent', 'points_multiplier', 'benefits', 'color', 'bg_color', 'sort_order'];

    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || (is_string($input[$field]) && trim($input[$field]) === '')) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }

    $tierData = [
        'name' => trim($input['name']),
        'min_spent' => intval($input['min_spent']),
        'points_multiplier' => floatval($input['points_multiplier']),
        'benefits' => trim($input['benefits']),
        'color' => $input['color'],
        'bg_color' => $input['bg_color'],
        'is_active' => $input['is_active'] ?? 1,
        'sort_order' => intval($input['sort_order'])
    ];

    // Validate values
    if ($tierData['min_spent'] < 0 || $tierData['points_multiplier'] <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid tier values']);
        return;
    }

    $tierId = createCustomerTier($tierData);

    if ($tierId) {
        echo json_encode([
            'success' => true,
            'message' => 'Customer tier created successfully',
            'tier_id' => $tierId
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create customer tier']);
    }
}

function handleUpdateTier($input) {
    $id = $input['id'] ?? '';
    if (empty($id)) {
        http_response_code(400);
        echo json_encode(['error' => 'Tier ID is required']);
        return;
    }

    $requiredFields = ['name', 'min_spent', 'points_multiplier', 'benefits', 'color', 'bg_color', 'sort_order'];

    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || (is_string($input[$field]) && trim($input[$field]) === '')) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }

    $tierData = [
        'name' => trim($input['name']),
        'min_spent' => intval($input['min_spent']),
        'points_multiplier' => floatval($input['points_multiplier']),
        'benefits' => trim($input['benefits']),
        'color' => $input['color'],
        'bg_color' => $input['bg_color'],
        'is_active' => $input['is_active'] ?? 1,
        'sort_order' => intval($input['sort_order'])
    ];

    // Validate values
    if ($tierData['min_spent'] < 0 || $tierData['points_multiplier'] <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid tier values']);
        return;
    }

    $result = updateCustomerTier($id, $tierData);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Customer tier updated successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update customer tier']);
    }
}

function handleDeleteTier($input) {
    $id = $input['id'] ?? '';
    if (empty($id)) {
        http_response_code(400);
        echo json_encode(['error' => 'Tier ID is required']);
        return;
    }

    $result = deleteCustomerTier($id);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Customer tier deactivated successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to deactivate customer tier']);
    }
}
