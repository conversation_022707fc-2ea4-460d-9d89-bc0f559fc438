<?php
/**
 * Blog Page - Professional Beauty Blog
 * Displays all blog posts or a single blog post with modern design
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/config/app.php';
require_once __DIR__ . '/includes/blog_functions.php';

// Check if we're viewing a single blog post
$slug = $_GET['slug'] ?? null;
$search = $_GET['search'] ?? '';
$singlePost = null;

if ($slug) {
    // Get the single blog post by slug
    $singlePost = $database->fetch(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.slug = ? AND bp.status = 'published'",
        [$slug]
    );

    // If post not found, redirect to blog index
    if (!$singlePost) {
        redirect('/blog');
    }

    $pageTitle = $singlePost['title'] . ' - Flix Salonce Blog';
} else {
    // Get search and pagination parameters
    $page = (int)($_GET['page'] ?? 1);
    $limit = 9; // Increased for better grid layout
    $offset = ($page - 1) * $limit;

    // Build search query
    $searchCondition = '';
    $searchConditionCount = '';
    $searchParams = [];
    if (!empty($search)) {
        $searchCondition = " AND (bp.title LIKE ? OR bp.summary LIKE ? OR bp.full_content LIKE ?)";
        $searchConditionCount = " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Get total posts count
    $totalPosts = $database->fetch(
        "SELECT COUNT(*) as count FROM blog_posts WHERE status = 'published'" . $searchConditionCount,
        $searchParams
    )['count'];

    // Get featured posts (latest 3 posts)
    $featuredPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT 3"
    );

    // Get regular blog posts (excluding featured ones if on first page)
    $excludeFeatured = ($page === 1 && empty($search)) ? " AND bp.id NOT IN ('" . implode("','", array_column($featuredPosts, 'id')) . "')" : '';

    $blogPosts = $database->fetchAll(
        "SELECT bp.*, u.name as author_name
         FROM blog_posts bp
         LEFT JOIN users u ON bp.author_id = u.id
         WHERE bp.status = 'published'" . $searchCondition . $excludeFeatured . "
         ORDER BY bp.publish_date DESC, bp.created_at DESC
         LIMIT $limit OFFSET $offset",
        $searchParams
    );

    $totalPages = ceil($totalPosts / $limit);
    $pageTitle = !empty($search) ? "Search: $search - Blog" : 'Beauty Blog - Flix Salonce';
}

// Include header
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Blog Styling */
.blog-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(245, 158, 11, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.blog-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(245, 158, 11, 0.1);
}

.featured-card {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(30, 41, 59, 0.9) 100%);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.featured-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 35px 60px -12px rgba(245, 158, 11, 0.2);
}

.search-glow {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.text-gradient {
    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>

<!-- Hero Section -->
<section class="relative min-h-[70vh] bg-gradient-to-br from-salon-black via-secondary-900 to-salon-black overflow-hidden">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-72 h-72 bg-salon-gold/5 rounded-full blur-3xl animate-float"></div>
        <div class="absolute bottom-20 right-10 w-96 h-96 bg-salon-gold/3 rounded-full blur-3xl animate-float" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-salon-gold/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(245,158,11,0.3) 1px, transparent 0); background-size: 50px 50px;"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-6 py-24 flex items-center min-h-[70vh]">
        <div class="w-full text-center">
            <?php if ($singlePost): ?>
                <!-- Single Post Hero -->
                <div class="max-w-4xl mx-auto">
                    <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a2 2 0 012-2z"></path>
                        </svg>
                        Blog Article
                    </div>
                    <h1 class="text-4xl md:text-6xl font-bold font-serif text-white mb-6 leading-tight">
                        <?= htmlspecialchars($singlePost['title']) ?>
                    </h1>
                    <div class="flex items-center justify-center space-x-6 text-gray-300 mb-8">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <?= htmlspecialchars($singlePost['author_name'] ?? 'Flix Salonce') ?>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <?= $singlePost['publish_date'] ? date('F j, Y', strtotime($singlePost['publish_date'])) : date('F j, Y', strtotime($singlePost['created_at'])) ?>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <?php
                            $wordCount = str_word_count(strip_tags($singlePost['full_content']));
                            $readTime = max(1, ceil($wordCount / 200));
                            echo "$readTime min read";
                            ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Blog Index Hero -->
                <div class="max-w-5xl mx-auto">
                    <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-4 py-2 rounded-full text-sm font-semibold mb-6">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        Beauty & Wellness Blog
                    </div>
                    <h1 class="text-5xl md:text-7xl font-bold font-serif text-white mb-6">
                        Beauty <span class="text-gradient">Insights</span>
                    </h1>
                    <p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
                        Discover the latest trends, tips, and secrets from the world of beauty and wellness. Your journey to radiance starts here.
                    </p>

                    <!-- Search Bar -->
                    <div class="max-w-2xl mx-auto">
                        <form method="GET" action="<?= getBasePath() ?>/blog" class="relative">
                            <div class="relative">
                                <input
                                    type="text"
                                    name="search"
                                    value="<?= htmlspecialchars($search) ?>"
                                    placeholder="Search beauty tips, tutorials, trends..."
                                    class="w-full px-6 py-4 pl-14 bg-secondary-800/50 backdrop-blur-sm border border-secondary-600 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold focus:ring-2 focus:ring-salon-gold/20 transition-all duration-300"
                                >
                                <svg class="absolute left-5 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-salon-gold hover:bg-gold-light text-black px-6 py-2 rounded-xl font-semibold transition-colors">
                                    Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Breadcrumb -->
            <div class="flex items-center justify-center space-x-2 text-gray-400 mt-8">
                <a href="<?= getBasePath() ?>/" class="hover:text-salon-gold transition-colors">Home</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <?php if ($singlePost): ?>
                    <a href="<?= getBasePath() ?>/blog" class="hover:text-salon-gold transition-colors">Blog</a>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-salon-gold"><?= htmlspecialchars($singlePost['title']) ?></span>
                <?php else: ?>
                    <span class="text-salon-gold">Blog</span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<div class="bg-salon-black py-20">
    <div class="max-w-7xl mx-auto px-6">
        <?php if ($singlePost): ?>
            <!-- Single Blog Post View -->
            <article class="max-w-4xl mx-auto">
                <!-- Featured Image -->
                <?php if ($singlePost['image_url']): ?>
                <div class="relative mb-12 rounded-2xl overflow-hidden shadow-2xl">
                    <div class="aspect-w-16 aspect-h-9">
                        <img
                            src="<?= htmlspecialchars(getBlogImageUrl($singlePost['image_url'])) ?>"
                            alt="<?= htmlspecialchars($singlePost['title']) ?>"
                            class="w-full h-[400px] object-cover"
                        >
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-salon-black/50 to-transparent"></div>
                </div>
                <?php endif; ?>

                <!-- Article Content -->
                <div class="bg-secondary-800/30 backdrop-blur-sm rounded-2xl border border-secondary-700/50 overflow-hidden">
                    <div class="p-8 md:p-12">
                        <!-- Article Summary -->
                        <?php if (!empty($singlePost['summary'])): ?>
                        <div class="bg-salon-gold/10 border-l-4 border-salon-gold p-6 rounded-r-lg mb-8">
                            <p class="text-lg text-gray-200 italic leading-relaxed">
                                <?= htmlspecialchars($singlePost['summary']) ?>
                            </p>
                        </div>
                        <?php endif; ?>

                        <!-- Article Body -->
                        <div class="prose prose-lg prose-invert max-w-none">
                            <style>
                                .prose {
                                    color: #e5e7eb;
                                    line-height: 1.8;
                                }
                                .prose h1, .prose h2, .prose h3, .prose h4 {
                                    color: #f9fafb;
                                    font-weight: 700;
                                    margin-top: 2em;
                                    margin-bottom: 1em;
                                }
                                .prose h1 { font-size: 2.25em; }
                                .prose h2 {
                                    font-size: 1.875em;
                                    border-bottom: 2px solid rgba(245, 158, 11, 0.2);
                                    padding-bottom: 0.5em;
                                }
                                .prose h3 { font-size: 1.5em; }
                                .prose h4 { font-size: 1.25em; }
                                .prose p {
                                    margin-bottom: 1.5em;
                                    font-size: 1.125em;
                                }
                                .prose a {
                                    color: #f59e0b;
                                    text-decoration: none;
                                    border-bottom: 1px solid transparent;
                                    transition: all 0.3s ease;
                                }
                                .prose a:hover {
                                    border-bottom-color: #f59e0b;
                                }
                                .prose ul, .prose ol {
                                    margin: 1.5em 0;
                                    padding-left: 2em;
                                }
                                .prose li {
                                    margin-bottom: 0.5em;
                                }
                                .prose img {
                                    border-radius: 1rem;
                                    margin: 2.5em 0;
                                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
                                }
                                .prose blockquote {
                                    border-left: 4px solid #f59e0b;
                                    padding-left: 1.5em;
                                    margin: 2em 0;
                                    font-style: italic;
                                    color: #d1d5db;
                                    background: rgba(245, 158, 11, 0.05);
                                    padding: 1.5em;
                                    border-radius: 0.5rem;
                                }
                                .prose code {
                                    background: rgba(245, 158, 11, 0.1);
                                    color: #f59e0b;
                                    padding: 0.25em 0.5em;
                                    border-radius: 0.25rem;
                                    font-size: 0.9em;
                                }
                                .prose pre {
                                    background: rgba(15, 23, 42, 0.8);
                                    border: 1px solid rgba(245, 158, 11, 0.2);
                                    border-radius: 0.75rem;
                                    padding: 1.5em;
                                    overflow-x: auto;
                                }
                            </style>
                            <?= $singlePost['full_content'] ?>
                        </div>

                        <!-- Article Footer -->
                        <div class="mt-12 pt-8 border-t border-secondary-700/50">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                                <!-- Back Button -->
                                <a href="<?= getBasePath() ?>/blog" class="inline-flex items-center text-salon-gold hover:text-gold-light transition-all duration-300 font-medium group">
                                    <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    Back to all posts
                                </a>

                                <!-- Share Buttons -->
                                <div class="flex items-center space-x-4">
                                    <span class="text-gray-400 text-sm">Share:</span>
                                    <div class="flex space-x-3">
                                        <a href="https://twitter.com/intent/tweet?text=<?= urlencode($singlePost['title']) ?>&url=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                           target="_blank"
                                           class="w-10 h-10 bg-secondary-700 hover:bg-blue-500 rounded-full flex items-center justify-center transition-colors">
                                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                            </svg>
                                        </a>
                                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                           target="_blank"
                                           class="w-10 h-10 bg-secondary-700 hover:bg-blue-600 rounded-full flex items-center justify-center transition-colors">
                                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                            </svg>
                                        </a>
                                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?= urlencode(getBasePath() . '/blog?slug=' . $singlePost['slug']) ?>"
                                           target="_blank"
                                           class="w-10 h-10 bg-secondary-700 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors">
                                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        <?php else: ?>
            <!-- Search Results Header -->
            <?php if (!empty($search)): ?>
            <div class="mb-12 text-center">
                <h2 class="text-3xl font-bold text-white mb-4">
                    Search Results for "<span class="text-salon-gold"><?= htmlspecialchars($search) ?></span>"
                </h2>
                <p class="text-gray-300">
                    <?= $totalPosts ?> <?= $totalPosts === 1 ? 'post' : 'posts' ?> found
                </p>
            </div>
            <?php endif; ?>

            <!-- Featured Posts Section (only on first page without search) -->
            <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
            <section class="mb-20">
                <div class="flex items-center justify-between mb-12">
                    <div>
                        <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-2">
                            Featured <span class="text-salon-gold">Stories</span>
                        </h2>
                        <p class="text-gray-300">Discover our latest beauty insights and trends</p>
                    </div>
                    <div class="hidden md:block w-32 h-px bg-gradient-to-r from-salon-gold to-transparent"></div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Main Featured Post -->
                    <?php $mainPost = $featuredPosts[0]; ?>
                    <div class="lg:col-span-2">
                        <article class="featured-card rounded-2xl overflow-hidden h-full group">
                            <div class="relative h-80 lg:h-96 overflow-hidden">
                                <?php if ($mainPost['image_url']): ?>
                                    <img
                                        src="<?= htmlspecialchars(getBlogImageUrl($mainPost['image_url'])) ?>"
                                        alt="<?= htmlspecialchars($mainPost['title']) ?>"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                                    >
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-secondary-800 flex items-center justify-center">
                                        <svg class="w-20 h-20 text-salon-gold/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                <div class="absolute inset-0 bg-gradient-to-t from-salon-black via-transparent to-transparent"></div>
                                <div class="absolute top-4 left-4">
                                    <span class="bg-salon-gold text-black px-3 py-1 rounded-full text-sm font-semibold">
                                        Featured
                                    </span>
                                </div>
                            </div>
                            <div class="p-8">
                                <div class="flex items-center text-gray-400 text-sm mb-4 space-x-4">
                                    <span><?= $mainPost['publish_date'] ? date('F j, Y', strtotime($mainPost['publish_date'])) : date('F j, Y', strtotime($mainPost['created_at'])) ?></span>
                                    <span>•</span>
                                    <span><?= max(1, ceil(str_word_count(strip_tags($mainPost['full_content'])) / 200)) ?> min read</span>
                                </div>
                                <h3 class="text-2xl lg:text-3xl font-bold text-white mb-4 group-hover:text-salon-gold transition-colors">
                                    <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>">
                                        <?= htmlspecialchars($mainPost['title']) ?>
                                    </a>
                                </h3>
                                <p class="text-gray-300 mb-6 line-clamp-3">
                                    <?= !empty($mainPost['summary']) ? htmlspecialchars($mainPost['summary']) : htmlspecialchars(substr(strip_tags($mainPost['full_content']), 0, 200)) . '...' ?>
                                </p>
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($mainPost['slug']) ?>"
                                   class="inline-flex items-center text-salon-gold hover:text-gold-light font-semibold transition-all duration-300 group">
                                    Read Full Story
                                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    </div>

                    <!-- Side Featured Posts -->
                    <div class="space-y-6">
                        <?php for ($i = 1; $i < min(3, count($featuredPosts)); $i++): ?>
                            <?php $sidePost = $featuredPosts[$i]; ?>
                            <article class="blog-card rounded-xl overflow-hidden group">
                                <div class="flex">
                                    <div class="w-24 h-24 flex-shrink-0 overflow-hidden">
                                        <?php if ($sidePost['image_url']): ?>
                                            <img
                                                src="<?= htmlspecialchars(getBlogImageUrl($sidePost['image_url'])) ?>"
                                                alt="<?= htmlspecialchars($sidePost['title']) ?>"
                                                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                            >
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                                                <svg class="w-8 h-8 text-salon-gold/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 p-4">
                                        <div class="text-xs text-gray-400 mb-2">
                                            <?= $sidePost['publish_date'] ? date('M j, Y', strtotime($sidePost['publish_date'])) : date('M j, Y', strtotime($sidePost['created_at'])) ?>
                                        </div>
                                        <h4 class="text-sm font-semibold text-white line-clamp-2 group-hover:text-salon-gold transition-colors">
                                            <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($sidePost['slug']) ?>">
                                                <?= htmlspecialchars($sidePost['title']) ?>
                                            </a>
                                        </h4>
                                    </div>
                                </div>
                            </article>
                        <?php endfor; ?>
                    </div>
                </div>
            </section>
            <?php endif; ?>

            <!-- Regular Blog Posts Grid -->
            <?php if (!empty($blogPosts)): ?>
            <section>
                <?php if ($page === 1 && empty($search) && !empty($featuredPosts)): ?>
                    <div class="flex items-center justify-between mb-12">
                        <div>
                            <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-2">
                                Latest <span class="text-salon-gold">Articles</span>
                            </h2>
                            <p class="text-gray-300">Stay updated with our beauty and wellness insights</p>
                        </div>
                        <div class="hidden md:block w-32 h-px bg-gradient-to-r from-salon-gold to-transparent"></div>
                    </div>
                <?php endif; ?>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($blogPosts as $post): ?>
                        <article class="blog-card rounded-xl overflow-hidden group h-full flex flex-col">
                            <!-- Post Image -->
                            <div class="relative h-48 overflow-hidden">
                                <?php if ($post['image_url']): ?>
                                    <img
                                        src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>"
                                        alt="<?= htmlspecialchars($post['title']) ?>"
                                        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                                    >
                                <?php else: ?>
                                    <div class="w-full h-full bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                                        <svg class="w-16 h-16 text-salon-gold/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>
                                <div class="absolute inset-0 bg-gradient-to-t from-salon-black/20 to-transparent"></div>
                            </div>

                            <!-- Post Content -->
                            <div class="p-6 flex flex-col flex-grow">
                                <!-- Meta Info -->
                                <div class="flex items-center text-gray-400 text-sm mb-3 space-x-3">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <?= $post['publish_date'] ? date('M j, Y', strtotime($post['publish_date'])) : date('M j, Y', strtotime($post['created_at'])) ?>
                                    </span>
                                    <span>•</span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <?= max(1, ceil(str_word_count(strip_tags($post['full_content'])) / 200)) ?> min
                                    </span>
                                </div>

                                <!-- Title -->
                                <h3 class="text-xl font-bold text-white mb-3 line-clamp-2 group-hover:text-salon-gold transition-colors">
                                    <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>">
                                        <?= htmlspecialchars($post['title']) ?>
                                    </a>
                                </h3>

                                <!-- Excerpt -->
                                <p class="text-gray-300 mb-6 line-clamp-3 flex-grow">
                                    <?= !empty($post['summary']) ? htmlspecialchars($post['summary']) : htmlspecialchars(substr(strip_tags($post['full_content']), 0, 150)) . '...' ?>
                                </p>

                                <!-- Read More Link -->
                                <a href="<?= getBasePath() ?>/blog?slug=<?= urlencode($post['slug']) ?>"
                                   class="inline-flex items-center text-salon-gold hover:text-gold-light font-semibold transition-all duration-300 group mt-auto">
                                    Read Article
                                    <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php else: ?>
                <!-- No Posts Found -->
                <div class="text-center py-20">
                    <div class="max-w-md mx-auto">
                        <svg class="w-24 h-24 text-gray-600 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                        </svg>
                        <h3 class="text-2xl font-bold text-white mb-4">
                            <?= !empty($search) ? 'No posts found' : 'No blog posts available' ?>
                        </h3>
                        <p class="text-gray-400 mb-8">
                            <?= !empty($search) ? "Try adjusting your search terms or browse all posts." : "We're working on creating amazing content for you. Check back soon!" ?>
                        </p>
                        <?php if (!empty($search)): ?>
                            <a href="<?= getBasePath() ?>/blog" class="inline-flex items-center bg-salon-gold hover:bg-gold-light text-black px-6 py-3 rounded-lg font-semibold transition-colors">
                                View All Posts
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Enhanced Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="mt-20 flex flex-col items-center space-y-6">
                <!-- Pagination Info -->
                <div class="text-center">
                    <p class="text-gray-400">
                        Showing page <span class="text-salon-gold font-semibold"><?= $page ?></span>
                        of <span class="text-salon-gold font-semibold"><?= $totalPages ?></span>
                        (<?= $totalPosts ?> total <?= $totalPosts === 1 ? 'post' : 'posts' ?>)
                    </p>
                </div>

                <!-- Pagination Controls -->
                <nav class="flex items-center space-x-2" aria-label="Pagination">
                    <?php
                    // Build pagination URL with search parameter
                    $baseUrl = getBasePath() . '/blog';
                    $searchParam = !empty($search) ? '&search=' . urlencode($search) : '';

                    // Calculate pagination range
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);

                    // Adjust range if we're near the beginning or end
                    if ($endPage - $startPage < 4) {
                        if ($startPage === 1) {
                            $endPage = min($totalPages, $startPage + 4);
                        } else {
                            $startPage = max(1, $endPage - 4);
                        }
                    }
                    ?>

                    <!-- Previous Button -->
                    <?php if ($page > 1): ?>
                        <a href="<?= $baseUrl ?>?page=<?= $page - 1 ?><?= $searchParam ?>"
                           class="flex items-center px-4 py-2 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg hover:bg-secondary-600 hover:border-salon-gold/30 transition-all duration-300 group">
                            <svg class="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </a>
                    <?php else: ?>
                        <span class="flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-secondary-800/30 border border-secondary-700 rounded-lg cursor-not-allowed">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </span>
                    <?php endif; ?>

                    <!-- First Page -->
                    <?php if ($startPage > 1): ?>
                        <a href="<?= $baseUrl ?>?page=1<?= $searchParam ?>"
                           class="px-4 py-2 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg hover:bg-secondary-600 hover:border-salon-gold/30 transition-all duration-300">
                            1
                        </a>
                        <?php if ($startPage > 2): ?>
                            <span class="px-2 py-2 text-gray-500">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <?php if ($i === $page): ?>
                            <span class="px-4 py-2 text-sm font-bold text-black bg-salon-gold rounded-lg shadow-lg">
                                <?= $i ?>
                            </span>
                        <?php else: ?>
                            <a href="<?= $baseUrl ?>?page=<?= $i ?><?= $searchParam ?>"
                               class="px-4 py-2 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg hover:bg-secondary-600 hover:border-salon-gold/30 transition-all duration-300">
                                <?= $i ?>
                            </a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <!-- Last Page -->
                    <?php if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                            <span class="px-2 py-2 text-gray-500">...</span>
                        <?php endif; ?>
                        <a href="<?= $baseUrl ?>?page=<?= $totalPages ?><?= $searchParam ?>"
                           class="px-4 py-2 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg hover:bg-secondary-600 hover:border-salon-gold/30 transition-all duration-300">
                            <?= $totalPages ?>
                        </a>
                    <?php endif; ?>

                    <!-- Next Button -->
                    <?php if ($page < $totalPages): ?>
                        <a href="<?= $baseUrl ?>?page=<?= $page + 1 ?><?= $searchParam ?>"
                           class="flex items-center px-4 py-2 text-sm font-medium text-white bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg hover:bg-secondary-600 hover:border-salon-gold/30 transition-all duration-300 group">
                            Next
                            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    <?php else: ?>
                        <span class="flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-secondary-800/30 border border-secondary-700 rounded-lg cursor-not-allowed">
                            Next
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </span>
                    <?php endif; ?>
                </nav>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Newsletter Subscription Section -->
<section class="py-20 bg-gradient-to-r from-salon-gold/10 via-salon-gold/5 to-transparent">
    <div class="max-w-4xl mx-auto px-6 text-center">
        <div class="bg-secondary-800/50 backdrop-blur-sm rounded-2xl border border-secondary-700/50 p-12">
            <div class="mb-8">
                <svg class="w-16 h-16 text-salon-gold mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <h2 class="text-3xl md:text-4xl font-bold font-serif text-white mb-4">
                    Stay <span class="text-salon-gold">Beautiful</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Subscribe to our newsletter and never miss the latest beauty tips, trends, and exclusive offers.
                </p>
            </div>

            <form class="max-w-md mx-auto" action="<?= getBasePath() ?>/api/newsletter.php" method="POST">
                <div class="flex flex-col sm:flex-row gap-4">
                    <input
                        type="email"
                        name="email"
                        placeholder="Enter your email address"
                        class="flex-1 px-6 py-4 bg-secondary-700/50 backdrop-blur-sm border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-salon-gold focus:ring-2 focus:ring-salon-gold/20 transition-all duration-300"
                        required
                    >
                    <button
                        type="submit"
                        class="px-8 py-4 bg-salon-gold hover:bg-gold-light text-black font-semibold rounded-lg transition-all duration-300 hover:scale-105 whitespace-nowrap"
                    >
                        Subscribe
                    </button>
                </div>
                <p class="text-sm text-gray-400 mt-4">
                    We respect your privacy. Unsubscribe at any time.
                </p>
            </form>
        </div>
    </div>
</section>

<?php include __DIR__ . '/includes/footer.php'; ?>