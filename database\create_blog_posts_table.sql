CREATE TABLE IF NOT EXISTS `blog_posts` (
  `id` VARCHAR(36) <PERSON><PERSON>AR<PERSON> KEY,
  `title` VA<PERSON>HA<PERSON>(255) NOT NULL,
  `slug` VARCHAR(255) NOT NULL UNIQUE,
  `summary` TEXT,
  `full_content` LONGTEXT NOT NULL,
  `image_url` VARCHA<PERSON>(2048),
  `publish_date` DATETIM<PERSON>,
  `author_id` VARCHAR(36) NULL,
  `status` ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOR<PERSON><PERSON><PERSON> KEY (`author_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
);

-- Add an index for faster searching by slug and status
CREATE INDEX idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_blog_posts_publish_date ON blog_posts(publish_date);