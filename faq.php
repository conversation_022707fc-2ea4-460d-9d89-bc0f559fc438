<?php
/**
 * FAQ Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get FAQs from database
$faqs = $database->fetchAll("SELECT * FROM faqs WHERE is_active = 1 ORDER BY category, display_order, id");

// Group FAQs by category
$faqsByCategory = [];
foreach ($faqs as $faq) {
    $faqsByCategory[$faq['category']][] = $faq;
}

$pageTitle = "Frequently Asked Questions";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced FAQ Styles */
.faq-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.faq-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.faq-card:hover::before {
    left: 100%;
}

.faq-card:hover {
    transform: translateY(-2px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.faq-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), padding 0.4s ease;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(212, 175, 55, 0.1);
}

.faq-content.show {
    max-height: 200px;
    padding: 1.5rem;
}

.faq-icon {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 0 4px rgba(212, 175, 55, 0.3));
}

.faq-icon.rotate {
    transform: rotate(180deg);
}

.category-header {
    position: relative;
    text-align: center;
    margin-bottom: 3rem;
}

.category-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #D4AF37, #F59E0B);
    border-radius: 2px;
}

.search-box {
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: #D4AF37;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.2);
}
</style>

<style>
/* Enhanced FAQ Styles */
.faq-card {
    background: linear-gradient(135deg, rgba(10, 10, 10, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.faq-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.faq-card:hover::before {
    left: 100%;
}

.faq-card:hover {
    transform: translateY(-2px);
    border-color: rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(212, 175, 55, 0.1);
}

.faq-toggle {
    position: relative;
    z-index: 2;
}

.faq-toggle:hover .faq-question {
    color: #D4AF37;
}

.faq-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), padding 0.4s ease;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(212, 175, 55, 0.1);
}

.faq-content.show {
    max-height: 200px;
    padding: 1.5rem;
}

.faq-icon {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 0 4px rgba(212, 175, 55, 0.3));
}

.faq-icon.rotate {
    transform: rotate(180deg);
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    margin: 3rem 0;
}

.category-header {
    position: relative;
    text-align: center;
    margin-bottom: 3rem;
}

.category-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #D4AF37, #F59E0B);
    border-radius: 2px;
}

.luxury-gradient {
    background: linear-gradient(135deg,
        rgba(212, 175, 55, 0.1) 0%,
        rgba(245, 158, 11, 0.05) 25%,
        rgba(31, 41, 55, 0.1) 50%,
        rgba(212, 175, 55, 0.05) 75%,
        rgba(17, 24, 39, 0.1) 100%);
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-elements::before,
.floating-elements::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.floating-elements::before {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-elements::after {
    bottom: 10%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.search-box {
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: #D4AF37;
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.2);
}

.search-input {
    background: transparent;
    border: none;
    outline: none;
    color: white;
    width: 100%;
    font-size: 1rem;
}

.search-input::placeholder {
    color: rgba(156, 163, 175, 0.7);
}

.quick-links {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.quick-link {
    background: rgba(212, 175, 55, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    color: #D4AF37;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-link:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-2px);
}
</style>

<!-- Enhanced Hero Section -->
<section class="relative bg-salon-black py-32 overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0 bg-gradient-to-br from-salon-black via-salon-black to-salon-black"></div>
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
         style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>

    <!-- Floating Decorative Elements -->
    <div class="absolute top-20 left-10 w-32 h-32 bg-salon-gold/10 rounded-full blur-xl animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-48 h-48 bg-salon-gold/5 rounded-full blur-2xl animate-pulse" style="animation-delay: 2s;"></div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <!-- Luxury Badge -->
            <div class="inline-flex items-center px-4 py-2 bg-salon-gold/10 border border-salon-gold/30 rounded-full mb-8">
                <svg class="w-4 h-4 text-salon-gold mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                <span class="text-salon-gold text-sm font-medium">Premium Support Center</span>
            </div>

            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
                Frequently Asked
                <span class="block text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                    Questions
                </span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                Find answers to common questions about our luxury services, booking process, and exclusive policies.
                <span class="text-salon-gold">Your satisfaction is our priority.</span>
            </p>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl font-bold text-salon-gold mb-2">50+</div>
                    <div class="text-gray-400 text-sm">Common Questions</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-salon-gold mb-2">24/7</div>
                    <div class="text-gray-400 text-sm">Online Support</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-salon-gold mb-2">100%</div>
                    <div class="text-gray-400 text-sm">Satisfaction Rate</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-salon-black relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">

        <!-- Search and Quick Navigation -->
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-white mb-4">How Can We Help You?</h2>
            <p class="text-gray-300 mb-8 max-w-2xl mx-auto">Search through our comprehensive FAQ or browse by category</p>

            <!-- Search Box -->
            <div class="search-box max-w-2xl mx-auto">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-salon-gold mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" id="faqSearch" class="search-input" placeholder="Search for answers..." onkeyup="searchFAQs()">
                </div>
            </div>

            <!-- Quick Category Links -->
            <div class="flex flex-wrap justify-center gap-3 mt-8">
                <?php foreach (array_keys($faqsByCategory) as $category) : ?>
                    <button onclick="scrollToCategory('<?= htmlspecialchars($category) ?>')" class="quick-link">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?= htmlspecialchars(ucfirst($category)) ?>
                    </button>
                <?php endforeach; ?>
            </div>
        </div>
        
        <?php if (empty($faqsByCategory)) : ?>
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">No FAQs Available</h3>
                <p class="text-gray-300 mb-6">We are currently updating our frequently asked questions. Please check back later.</p>
            </div>
        <?php else : ?>
            <?php foreach ($faqsByCategory as $category => $faqs) : ?>
                <div id="<?= htmlspecialchars($category) ?>" class="mb-20 faq-category">
                    <div class="category-header">
                        <h2 class="text-4xl font-bold text-white"><?= htmlspecialchars(ucfirst($category)) ?></h2>
                    </div>

                    <div class="grid gap-6">
                        <?php foreach ($faqs as $faq) : ?>
                            <div class="faq-card rounded-xl overflow-hidden">
                                <button class="faq-toggle w-full text-left p-8 focus:outline-none" onclick="toggleFAQ(this)">
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center">
                                            <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mr-4">
                                                <!-- Generic Icon -->
                                                <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                            <h3 class="text-xl font-semibold text-white faq-question"><?= htmlspecialchars($faq['question']) ?></h3>
                                        </div>
                                        <svg class="faq-icon w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div>
                                </button>
                                <div class="faq-content">
                                    <p class="text-gray-300 leading-relaxed"><?= nl2br(htmlspecialchars($faq['answer'])) ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Enhanced Contact Section -->
        <div class="relative bg-gradient-to-br from-salon-gold/10 via-secondary-800 to-salon-gold/5 rounded-3xl p-12 overflow-hidden">
            <!-- Background Elements -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-salon-gold/10 rounded-full blur-2xl"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-salon-gold/5 rounded-full blur-xl"></div>

            <div class="relative text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-salon-gold/20 rounded-full mb-6">
                    <svg class="w-8 h-8 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-bold text-white mb-4">Still Have Questions?</h2>
                <p class="text-gray-300 mb-8 max-w-2xl mx-auto text-lg">Can't find what you're looking for? Our luxury concierge team is here to provide personalized assistance.</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-white font-semibold mb-1">Call Us</h3>
                        <p class="text-gray-400 text-sm">Immediate assistance</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-white font-semibold mb-1">Email Us</h3>
                        <p class="text-gray-400 text-sm">Detailed inquiries</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-white font-semibold mb-1">Visit Us</h3>
                        <p class="text-gray-400 text-sm">In-person consultation</p>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?= getBasePath() ?>/contact.php" class="inline-flex items-center justify-center bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black px-8 py-4 rounded-xl font-semibold transition-all hover:scale-105 shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Contact Us
                    </a>
                    <a href="tel:+15551234567" class="inline-flex items-center justify-center bg-secondary-800/80 backdrop-blur-sm hover:bg-secondary-700 text-white px-8 py-4 rounded-xl font-semibold transition-all border border-salon-gold/30 hover:border-salon-gold/50">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        Call (*************
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced FAQ functionality with smooth animations and search
function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.faq-icon');
    const isOpen = content.classList.contains('show');

    // Close all other FAQs with smooth animation
    document.querySelectorAll('.faq-content').forEach(item => {
        if (item !== content && item.classList.contains('show')) {
            item.classList.remove('show');
            const otherIcon = item.previousElementSibling.querySelector('.faq-icon');
            if (otherIcon) {
                otherIcon.classList.remove('rotate');
            }
        }
    });

    // Toggle current FAQ with smooth animation
    if (isOpen) {
        content.classList.remove('show');
        icon.classList.remove('rotate');
    } else {
        content.classList.add('show');
        icon.classList.add('rotate');

        // Smooth scroll to the opened FAQ
        setTimeout(() => {
            button.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 200);
    }
}

// Search functionality
function searchFAQs() {
    const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
    const faqCards = document.querySelectorAll('.faq-card');
    let visibleCount = 0;

    faqCards.forEach(card => {
        const question = card.querySelector('.faq-question').textContent.toLowerCase();
        const answer = card.querySelector('.faq-content p').textContent.toLowerCase();

        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            card.style.display = 'block';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
            visibleCount++;
        } else {
            card.style.display = 'none';
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
        }
    });

    // Show/hide category headers based on visible FAQs
    const categories = document.querySelectorAll('.faq-category');
    categories.forEach(category => {
        const categoryCards = category.querySelectorAll('.faq-card');
        const visibleCards = Array.from(categoryCards).filter(card => card.style.display !== 'none');

        if (visibleCards.length > 0 || searchTerm === '') {
            category.style.display = 'block';
        } else {
            category.style.display = 'none';
        }
    });

    // Show no results message if needed
    showNoResultsMessage(visibleCount === 0 && searchTerm !== '');
}

// Scroll to category
function scrollToCategory(categoryId) {
    const category = document.getElementById(categoryId);
    if (category) {
        category.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Add highlight effect
        category.style.transform = 'scale(1.02)';
        setTimeout(() => {
            category.style.transform = 'scale(1)';
        }, 300);
    }
}

// Show/hide no results message
function showNoResultsMessage(show) {
    let noResultsDiv = document.getElementById('noResults');

    if (show && !noResultsDiv) {
        noResultsDiv = document.createElement('div');
        noResultsDiv.id = 'noResults';
        noResultsDiv.className = 'text-center py-16';
        noResultsDiv.innerHTML = `
            <div class="w-24 h-24 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-salon-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-white mb-4">No Results Found</h3>
            <p class="text-gray-300 mb-6">We couldn't find any FAQs matching your search. Try different keywords or browse our categories.</p>
            <button onclick="clearSearch()" class="bg-salon-gold text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-colors">
                Clear Search
            </button>
        `;
        document.querySelector('.max-w-6xl').appendChild(noResultsDiv);
    } else if (!show && noResultsDiv) {
        noResultsDiv.remove();
    }
}

// Clear search
function clearSearch() {
    document.getElementById('faqSearch').value = '';
    searchFAQs();
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to FAQ cards
    const faqCards = document.querySelectorAll('.faq-card');
    faqCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.4s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Add search input event listener
    const searchInput = document.getElementById('faqSearch');
    if (searchInput) {
        searchInput.addEventListener('input', searchFAQs);
    }
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
