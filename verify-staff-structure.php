<?php
/**
 * Verify Staff Structure Script
 * Checks and fixes staff_specialties table structure
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Staff Structure Verification</h1>";

try {
    echo "<h2>Checking staff_specialties Table Structure</h2>";
    
    // Check if table exists
    $tableExists = $database->fetch("SHOW TABLES LIKE 'staff_specialties'");
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ staff_specialties table does not exist!</p>";
        echo "<p>Creating table...</p>";
        
        $database->execute("
            CREATE TABLE staff_specialties (
                id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                service_id VARCHAR(36) NOT NULL,
                proficiency_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT') DEFAULT 'INTERMEDIATE',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_service (user_id, service_id),
                INDEX idx_staff_specialties_user (user_id),
                INDEX idx_staff_specialties_service (service_id)
            )
        ");
        
        echo "<p style='color: green;'>✅ Created staff_specialties table</p>";
    } else {
        echo "<p style='color: green;'>✅ staff_specialties table exists</p>";
    }
    
    // Check table structure
    $columns = $database->fetchAll("DESCRIBE staff_specialties");
    $hasProficiencyLevel = false;
    
    echo "<h3>Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'proficiency_level') {
            $hasProficiencyLevel = true;
        }
    }
    echo "</table>";
    
    if (!$hasProficiencyLevel) {
        echo "<p style='color: red;'>❌ Missing proficiency_level column</p>";
        echo "<p>Adding column...</p>";
        
        $database->execute("
            ALTER TABLE staff_specialties 
            ADD COLUMN proficiency_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT') DEFAULT 'INTERMEDIATE'
        ");
        
        echo "<p style='color: green;'>✅ Added proficiency_level column</p>";
    } else {
        echo "<p style='color: green;'>✅ proficiency_level column exists</p>";
    }
    
    echo "<h2>Staff Specialties Data Check</h2>";
    
    // Check staff and specialties counts
    $staffCount = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF' AND is_active = 1")['count'];
    $specialtiesCount = $database->fetch("SELECT COUNT(*) as count FROM staff_specialties")['count'];
    $staffWithSpecialties = $database->fetch("SELECT COUNT(DISTINCT user_id) as count FROM staff_specialties")['count'];
    
    echo "<p>Active Staff: <strong>$staffCount</strong></p>";
    echo "<p>Total Specialties: <strong>$specialtiesCount</strong></p>";
    echo "<p>Staff with Specialties: <strong>$staffWithSpecialties</strong></p>";
    
    if ($staffWithSpecialties == 0 && $staffCount > 0) {
        echo "<p style='color: orange;'>⚠️ No staff have specialties assigned</p>";
        echo "<p>Would you like to assign default specialties to all staff?</p>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='assign_specialties'>";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Assign Default Specialties</button>";
        echo "</form>";
    }
    
    // Handle specialty assignment
    if (isset($_POST['action']) && $_POST['action'] === 'assign_specialties') {
        echo "<h3>Assigning Default Specialties...</h3>";
        
        try {
            $database->beginTransaction();
            
            // Get all active staff and services
            $staff = $database->fetchAll("SELECT id FROM users WHERE role = 'STAFF' AND is_active = 1");
            $services = $database->fetchAll("SELECT id FROM services WHERE is_active = 1");
            
            $assigned = 0;
            foreach ($staff as $staffMember) {
                foreach ($services as $service) {
                    // Check if specialty already exists
                    $exists = $database->fetch("
                        SELECT id FROM staff_specialties 
                        WHERE user_id = ? AND service_id = ?
                    ", [$staffMember['id'], $service['id']]);
                    
                    if (!$exists) {
                        $specialtyId = generateUUID();
                        $database->execute("
                            INSERT INTO staff_specialties (id, user_id, service_id, proficiency_level, created_at, updated_at)
                            VALUES (?, ?, ?, 'INTERMEDIATE', NOW(), NOW())
                        ", [$specialtyId, $staffMember['id'], $service['id']]);
                        $assigned++;
                    }
                }
            }
            
            $database->commit();
            echo "<p style='color: green;'>✅ Assigned $assigned default specialties</p>";
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>❌ Error assigning specialties: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<h2>Test Staff Suggestions Query</h2>";
    
    // Test the actual query used by the API
    $testService = $database->fetch("SELECT id, name FROM services WHERE is_active = 1 LIMIT 1");
    if ($testService) {
        echo "<p>Testing with service: <strong>" . htmlspecialchars($testService['name']) . "</strong></p>";
        
        try {
            $staff = $database->fetchAll("
                SELECT DISTINCT
                    u.id,
                    u.name,
                    u.email,
                    u.phone,
                    u.image,
                    u.role,
                    u.is_active,
                    u.created_at,
                    u.updated_at,
                    MAX(ss.proficiency_level) as proficiency_level,
                    GROUP_CONCAT(
                        CONCAT(s2.name, ' (', ss2.proficiency_level, ')')
                        SEPARATOR '|'
                    ) as all_specialties,
                    CASE
                        WHEN MAX(ss.proficiency_level) = 'EXPERT' THEN 1
                        WHEN MAX(ss.proficiency_level) = 'ADVANCED' THEN 2
                        WHEN MAX(ss.proficiency_level) = 'INTERMEDIATE' THEN 3
                        WHEN MAX(ss.proficiency_level) = 'BEGINNER' THEN 4
                        ELSE 5
                    END as ranking
                FROM users u
                LEFT JOIN staff_specialties ss ON u.id = ss.user_id AND ss.service_id = ?
                LEFT JOIN staff_specialties ss2 ON u.id = ss2.user_id
                LEFT JOIN services s2 ON ss2.service_id = s2.id
                WHERE u.role = 'STAFF' AND u.is_active = 1
                GROUP BY u.id, u.name, u.email, u.phone, u.image, u.role, u.is_active, u.created_at, u.updated_at
                ORDER BY
                    ranking ASC,
                    u.name ASC
            ", [$testService['id']]);
            
            echo "<p style='color: green;'>✅ Query executed successfully</p>";
            echo "<p>Found " . count($staff) . " staff members</p>";
            
            if (!empty($staff)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>Name</th><th>Proficiency</th><th>Ranking</th><th>All Specialties</th></tr>";
                foreach (array_slice($staff, 0, 3) as $member) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($member['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($member['proficiency_level'] ?? 'None') . "</td>";
                    echo "<td>" . htmlspecialchars($member['ranking']) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($member['all_specialties'] ?? '', 0, 50)) . "...</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Query failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Summary</h2>";
echo "<ul>";
echo "<li>staff_specialties table structure verified</li>";
echo "<li>Staff suggestions query tested</li>";
echo "<li>Default specialties can be assigned if needed</li>";
echo "</ul>";

echo "<p><a href='test-fixes.php'>← Back to Test Fixes</a></p>";
?>
