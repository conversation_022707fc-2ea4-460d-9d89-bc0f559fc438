-- Add OTP fields for forgot password functionality
-- Run this SQL script in php<PERSON>yAdmin or your database management tool

-- Add OTP fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_otp VARCHAR(6) NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_expires_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_attempts INT DEFAULT 0;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_reset_otp ON users(reset_otp);
CREATE INDEX IF NOT EXISTS idx_users_otp_expires ON users(otp_expires_at);

-- Verify the fields were added
SHOW COLUMNS FROM users LIKE 'reset_otp';
SHOW COLUMNS FROM users LIKE 'otp_expires_at';
SHOW COLUMNS FROM users LIKE 'otp_attempts';
