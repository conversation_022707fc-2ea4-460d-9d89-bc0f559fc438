<?php
/**
 * Staff Notifications Bulk Operations API
 * Handles bulk notification operations for staff members
 * Flix Salonce - PHP Version
 */

session_start();
require_once __DIR__ . '/../../config/app.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Set JSON header
header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'PUT':
            handleBulkUpdate();
            break;
        case 'DELETE':
            handleBulkDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleBulkUpdate() {
    global $database;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $action = $input['action'] ?? '';
    $notificationIds = $input['notification_ids'] ?? [];
    $category = $input['category'] ?? null;
    
    if (!$action) {
        http_response_code(400);
        echo json_encode(['error' => 'Action is required']);
        return;
    }
    
    $userId = $_SESSION['user_id'];
    
    switch ($action) {
        case 'mark_read':
            handleMarkAsRead($userId, $notificationIds, $category);
            break;
        case 'mark_unread':
            handleMarkAsUnread($userId, $notificationIds, $category);
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handleMarkAsRead($userId, $notificationIds, $category) {
    global $database;
    
    if (!empty($notificationIds)) {
        // Mark specific notifications as read
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $params = array_merge($notificationIds, [$userId]);
        
        $affected = $database->query(
            "UPDATE notifications 
             SET is_read = 1, updated_at = NOW()
             WHERE id IN ({$placeholders}) AND user_id = ?",
            $params
        );
    } else if ($category) {
        // Mark all notifications in category as read
        $affected = $database->query(
            "UPDATE notifications 
             SET is_read = 1, updated_at = NOW()
             WHERE user_id = ? AND category = ? AND is_read = 0",
            [$userId, $category]
        );
    } else {
        // Mark all notifications as read
        $affected = $database->query(
            "UPDATE notifications 
             SET is_read = 1, updated_at = NOW()
             WHERE user_id = ? AND is_read = 0",
            [$userId]
        );
    }
    
    echo json_encode([
        'success' => true,
        'affected' => $affected ? 1 : 0
    ]);
}

function handleMarkAsUnread($userId, $notificationIds, $category) {
    global $database;
    
    if (!empty($notificationIds)) {
        // Mark specific notifications as unread
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $params = array_merge($notificationIds, [$userId]);
        
        $affected = $database->query(
            "UPDATE notifications 
             SET is_read = 0, updated_at = NOW()
             WHERE id IN ({$placeholders}) AND user_id = ?",
            $params
        );
    } else if ($category) {
        // Mark all notifications in category as unread
        $affected = $database->query(
            "UPDATE notifications 
             SET is_read = 0, updated_at = NOW()
             WHERE user_id = ? AND category = ? AND is_read = 1",
            [$userId, $category]
        );
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Cannot mark all notifications as unread without specific IDs or category']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'affected' => $affected ? 1 : 0
    ]);
}

function handleBulkDelete() {
    global $database;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $notificationIds = $input['notification_ids'] ?? [];
    $category = $input['category'] ?? null;
    
    $userId = $_SESSION['user_id'];
    
    if (!empty($notificationIds)) {
        // Delete specific notifications
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $params = array_merge($notificationIds, [$userId]);
        
        $result = $database->query(
            "DELETE FROM notifications
             WHERE id IN ({$placeholders}) AND user_id = ?",
            $params
        );

        $affected = $result ? count($notificationIds) : 0;
    } else if ($category) {
        // Delete all notifications in category
        $result = $database->query(
            "DELETE FROM notifications
             WHERE user_id = ? AND category = ?",
            [$userId, $category]
        );

        $affected = $result ? 1 : 0;
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Cannot delete all notifications without specific IDs or category']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'affected' => $affected
    ]);
}
