<?php
/**
 * Admin Subcategories API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Set JSON header
header('Content-Type: application/json');

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if present
$basePath = '/flix-php';
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Extract subcategory ID from path
$pathParts = explode('/', trim($path, '/'));
$subcategoryId = null;
if (count($pathParts) >= 4 && $pathParts[3] !== '') {
    $subcategoryId = $pathParts[3];
}

try {
    switch ($method) {
        case 'GET':
            if ($subcategoryId) {
                // Get specific subcategory
                $subcategory = getServiceSubcategoryById($subcategoryId);
                if ($subcategory) {
                    echo json_encode([
                        'success' => true,
                        'subcategory' => $subcategory
                    ]);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Subcategory not found']);
                }
            } else {
                // Get subcategories with optional filters
                $categoryId = $_GET['category_id'] ?? null;
                $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
                $withCount = isset($_GET['with_count']) && $_GET['with_count'] === 'true';
                
                if ($categoryId) {
                    // Get subcategories for specific category
                    if ($withCount) {
                        $subcategories = getSubcategoriesWithCount($categoryId);
                    } else {
                        $subcategories = getSubcategoriesByCategory($categoryId, $activeOnly);
                    }
                } else {
                    // Get all subcategories
                    if ($withCount) {
                        $subcategories = getSubcategoriesWithCount();
                    } else {
                        $subcategories = getAllSubcategoriesWithCategory($activeOnly);
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'subcategories' => $subcategories,
                    'total' => count($subcategories)
                ]);
            }
            break;
            
        case 'POST':
            // Create new subcategory
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON data']);
                break;
            }
            
            if (empty($input['name'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Subcategory name is required']);
                break;
            }
            
            if (empty($input['category_id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Category ID is required']);
                break;
            }
            
            $result = createServiceSubcategory($input);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'message' => 'Subcategory created successfully',
                    'id' => $result['id']
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'PUT':
            // Update subcategory
            if (!$subcategoryId) {
                http_response_code(400);
                echo json_encode(['error' => 'Subcategory ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON data']);
                break;
            }
            
            // Handle special operations
            if (isset($input['operation'])) {
                switch ($input['operation']) {
                    case 'reorder':
                        $result = updateSubcategorySortOrder($subcategoryId, $input['sort_order']);
                        break;
                    case 'toggle_status':
                        $result = toggleServiceSubcategoryStatus($subcategoryId);
                        break;
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Unknown operation']);
                        return;
                }
            } else {
                // Regular update
                if (empty($input['name'])) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Subcategory name is required']);
                    break;
                }
                
                $result = updateServiceSubcategory($subcategoryId, $input);
            }
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Subcategory updated successfully',
                    'data' => $result
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'PATCH':
            // Toggle subcategory status
            if (!$subcategoryId) {
                http_response_code(400);
                echo json_encode(['error' => 'Subcategory ID required']);
                break;
            }
            
            $result = toggleServiceSubcategoryStatus($subcategoryId);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Subcategory status updated successfully',
                    'new_status' => $result['new_status']
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'DELETE':
            // Delete subcategory
            if (!$subcategoryId) {
                http_response_code(400);
                echo json_encode(['error' => 'Subcategory ID required']);
                break;
            }
            
            $result = deleteServiceSubcategory($subcategoryId);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Subcategory deleted successfully'
                ]);
            } else {
                http_response_code(400);
                echo json_encode($result);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Subcategories API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
