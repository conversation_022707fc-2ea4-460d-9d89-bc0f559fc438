# AJAX Reminder System Documentation

## Overview

The AJAX Reminder System provides real-time, browser-based processing of booking reminders as an alternative to traditional cron jobs. This dual-system approach ensures maximum reliability and immediate processing when users are actively using the system.

## Key Features

### ✅ **Dual System Approach**
- **AJAX Processing**: Real-time browser-based processing
- **Cron Job Backup**: Traditional server-side scheduled processing
- **Both systems can run simultaneously** for maximum reliability

### ✅ **Silent Background Operation**
- Runs every 2-3 minutes without user interaction
- No page reloads or visible interruptions
- Graceful handling of network errors
- Continues processing even when page is hidden

### ✅ **Comprehensive Processing**
- Processes pending reminders
- Recovers missed reminders
- Handles overdue reminders (even if time range passed)
- Sends emails to both customers and staff

### ✅ **Admin Status Indicator**
- Real-time status display for admin users
- Processing statistics and last update time
- Visual feedback for system activity
- Optional notifications for successful processing

## Files Created

### Core System Files
```
ajax_process_reminders.php          # AJAX endpoint for reminder processing
assets/js/reminder-processor.js     # JavaScript background processor
test_ajax_reminders.php            # Comprehensive testing interface
AJAX_REMINDER_SYSTEM_README.md     # This documentation
```

### Integration Points
```
includes/admin_header.php          # Admin page integration
includes/customer_header.php       # Customer page integration  
includes/header.php               # Public page integration
```

## How It Works

### 1. **Automatic Initialization**
```javascript
// Auto-detects page type and initializes appropriate settings
document.addEventListener('DOMContentLoaded', function() {
    const isAdmin = /* detection logic */;
    
    window.reminderProcessor = new ReminderProcessor({
        interval: 120000, // 2 minutes
        showNotifications: isAdmin,
        isAdmin: isAdmin,
        debug: false
    });
});
```

### 2. **Background Processing**
- Runs every 2 minutes (configurable)
- Makes AJAX calls to `ajax_process_reminders.php`
- Processes pending, missed, and overdue reminders
- Updates status indicator with results

### 3. **AJAX Endpoint Processing**
```php
// Processes three types of reminders:
1. processPendingReminders()     // Normal scheduled reminders
2. checkMissedReminders()        // Recovery for missed reminders  
3. processOverdueReminders()     // Late reminders for active bookings
```

### 4. **Status Indicator (Admin Only)**
- Fixed position indicator in bottom-right corner
- Shows processing status and statistics
- Updates in real-time with processing results
- Responsive design for mobile devices

## Configuration Options

### JavaScript Processor Options
```javascript
new ReminderProcessor({
    interval: 120000,           // Processing interval (2 minutes)
    endpoint: 'ajax_process_reminders.php',
    showNotifications: false,   // Show success notifications
    isAdmin: false,            // Admin-specific features
    debug: false,              // Debug logging
    maxRetries: 3,             // Max retry attempts
    retryDelay: 5000          // Retry delay (5 seconds)
});
```

### PHP Endpoint Features
- 30-second execution time limit
- JSON response format
- CORS headers for cross-origin requests
- Comprehensive error handling
- Processing time tracking

## Testing with Specified Email

### Test Email Address
All tests use: **<EMAIL>**

### Testing Interface
Access: `http://localhost/flix-php/test_ajax_reminders.php`

#### Test Functions:
1. **Create Test Booking** - Creates booking for test email
2. **Test AJAX Endpoint** - Verifies endpoint functionality
3. **Send Test Reminder** - Manual reminder sending
4. **Live AJAX Testing** - Real-time processor testing

#### Test Controls:
```javascript
// Start/stop processor manually
testProcessor.start();
testProcessor.stop();

// Process reminders immediately
await testProcessor.processReminders();
```

## Integration Details

### Page Detection
The system automatically detects page types:
```javascript
const isAdmin = document.body.classList.contains('admin-page') || 
               window.location.pathname.includes('/admin/') ||
               document.querySelector('.admin-sidebar') !== null;
```

### CSS Classes Added
- `admin-page` - Admin pages
- `customer-page` - Customer pages  
- `public-page` - Public pages

### Status Indicator Styling
```css
#reminder-status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    /* Responsive, translucent design */
}

/* Status-specific styling */
.active { border-color: #3b82f6; }
.processing { border-color: #f59e0b; }
.success { border-color: #10b981; }
.error { border-color: #ef4444; }
```

## API Response Format

### Success Response
```json
{
    "success": true,
    "message": "Processed 5 reminders. Sent: 3, Failed: 0",
    "data": {
        "processed": 5,
        "sent": 3,
        "failed": 0,
        "skipped": 0,
        "missed_recovered": 1,
        "overdue_processed": 2,
        "overdue_sent": 1,
        "processing_time": 245.67
    },
    "timestamp": "2024-01-15 14:30:25",
    "debug": ["Processing pending reminders...", "Checking for missed reminders..."]
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error processing reminders: Database connection failed",
    "data": { /* empty data object */ },
    "timestamp": "2024-01-15 14:30:25",
    "debug": ["Error: Database connection failed"]
}
```

## Security Features

### Basic Security Measures
- Request method validation (GET/POST only)
- IP logging for monitoring
- Execution time limits
- Error message sanitization

### Rate Limiting (Optional)
```php
// Can be added to ajax_process_reminders.php
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
// Implement IP-based rate limiting here
```

## Performance Considerations

### Browser Impact
- Minimal CPU usage (runs every 2 minutes)
- Small network requests (~1-2KB)
- Efficient JSON parsing
- Graceful error handling

### Server Impact
- 30-second execution limit per request
- Processes maximum 20 overdue reminders per call
- Efficient database queries with limits
- Automatic cleanup of old data

## Monitoring and Debugging

### Debug Mode
```javascript
// Enable debug logging
new ReminderProcessor({ debug: true });

// Check processor stats
console.log(window.reminderProcessor.getStats());
```

### Server Logs
```php
// All activities logged to error_log
error_log("AJAX reminder processing started from IP: $clientIP");
error_log("AJAX reminder processing completed: " . json_encode($results));
```

### Admin Interface
- Real-time status indicator
- Processing statistics
- Last update timestamps
- Success/failure counts

## Compatibility

### Browser Requirements
- Modern browsers with Fetch API support
- JavaScript enabled
- Local storage support (for future enhancements)

### Server Requirements
- PHP 7.4+ with JSON extension
- Database access
- File write permissions for logging

## Troubleshooting

### Common Issues

1. **AJAX Endpoint Not Found**
   - Verify `ajax_process_reminders.php` exists in root directory
   - Check file permissions

2. **No Status Indicator**
   - Ensure user is on admin page
   - Check CSS class detection logic

3. **Processing Failures**
   - Check database connection
   - Verify reminder tables exist
   - Review error logs

### Debug Steps
1. Open browser console
2. Enable debug mode: `window.reminderProcessor.options.debug = true`
3. Check network tab for AJAX requests
4. Review server error logs
5. Test endpoint directly: `ajax_process_reminders.php`

## Future Enhancements

### Planned Features
- WebSocket integration for real-time updates
- Advanced rate limiting and security
- Detailed analytics dashboard
- Mobile app integration
- Push notification support

### Configuration Improvements
- Admin panel for AJAX settings
- Dynamic interval adjustment
- Priority-based processing queues
- Advanced retry strategies

## Conclusion

The AJAX Reminder System provides a robust, real-time alternative to traditional cron jobs while maintaining compatibility with existing systems. It ensures reminders are processed immediately when users are active, providing better reliability and user experience.

**Key Benefits:**
- ✅ Real-time processing
- ✅ No server cron job dependencies  
- ✅ Silent background operation
- ✅ Dual-system reliability
- ✅ Comprehensive testing tools
- ✅ Admin monitoring interface

The system is now **fully operational** and ready for production use! 🚀
