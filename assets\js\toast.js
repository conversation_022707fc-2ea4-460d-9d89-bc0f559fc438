/**
 * Centralized Toast Notification System
 * Provides consistent toast notifications across all pages
 */

// Show toast notification with ultra-smooth animations
function showToast(message, type = 'success') {
    // Remove any existing toast first with smooth exit
    const existingToast = document.getElementById('wishlist-toast');
    if (existingToast) {
        existingToast.style.transform = 'translateX(100%) scale(0.95)';
        existingToast.style.opacity = '0';
        setTimeout(() => existingToast.remove(), 200);
    }

    // Create new toast element
    const toast = document.createElement('div');
    toast.id = 'wishlist-toast';

    // Set base styles for ultra-smooth animation
    toast.style.cssText = `
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        background-color: #1e293b;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        max-width: 20rem;
        backdrop-filter: blur(8px);
        border-left: 4px solid;
    `;

    // Set content and style based on type
    if (type === 'success') {
        toast.style.borderLeftColor = '#10b981';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else if (type === 'error') {
        toast.style.borderLeftColor = '#ef4444';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-circle" style="color: #ef4444; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else if (type === 'warning') {
        toast.style.borderLeftColor = '#f59e0b';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-exclamation-triangle" style="color: #f59e0b; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    } else if (type === 'info') {
        toast.style.borderLeftColor = '#3b82f6';
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="fas fa-info-circle" style="color: #3b82f6; margin-right: 0.75rem; font-size: 1.125rem;"></i>
                <span style="font-size: 0.875rem; font-weight: 500; line-height: 1.25;">${message}</span>
            </div>
        `;
    }

    // Add to DOM
    document.body.appendChild(toast);

    // Trigger ultra-smooth slide-in animation using requestAnimationFrame
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            toast.style.transform = 'translateX(0) scale(1)';
            toast.style.opacity = '1';
        });
    });

    // Hide toast after exactly 2 seconds with smooth slide-out
    setTimeout(() => {
        toast.style.transform = 'translateX(100%) scale(0.95)';
        toast.style.opacity = '0';

        // Remove from DOM after animation completes (400ms)
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 400);
    }, 2000);
}

// Utility function to show specific toast types
const Toast = {
    success: (message) => showToast(message, 'success'),
    error: (message) => showToast(message, 'error'),
    warning: (message) => showToast(message, 'warning'),
    info: (message) => showToast(message, 'info')
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { showToast, Toast };
}
