-- Critical System Updates: Points Logic & Booking Expiration
-- Execute this script to update the booking system

USE flix_salonce2;

-- 1. Update booking status enum to include EXPIRED
ALTER TABLE bookings MODIFY COLUMN status 
ENUM('PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'EXPIRED') 
DEFAULT 'PENDING';

-- 2. Add index for efficient expiration checking
CREATE INDEX IF NOT EXISTS idx_bookings_expiration 
ON bookings(status, date, start_time);

-- 3. Add index for points transactions
CREATE INDEX IF NOT EXISTS idx_point_transactions_booking 
ON point_transactions(booking_id) WHERE booking_id IS NOT NULL;

-- 4. Add booking_id column to point_transactions if it doesn't exist
ALTER TABLE point_transactions 
ADD COLUMN IF NOT EXISTS booking_id VARCHAR(36) NULL,
ADD CONSTRAINT fk_point_transactions_booking 
FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL;

-- 5. <PERSON>reate a function to check and expire old bookings
DELIMITER //
CREATE OR REPLACE PROCEDURE ExpireOldBookings()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE booking_id VARCHAR(36);
    DECLARE user_id VARCHAR(36);
    DECLARE points_earned INT;
    
    -- Cursor to find expired bookings
    DECLARE expired_cursor CURSOR FOR
        SELECT id, user_id, points_earned
        FROM bookings 
        WHERE status IN ('PENDING', 'CONFIRMED') 
        AND CONCAT(date, ' ', start_time) < NOW()
        AND date < CURDATE();
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    START TRANSACTION;
    
    -- Update expired bookings
    UPDATE bookings 
    SET status = 'EXPIRED', updated_at = NOW()
    WHERE status IN ('PENDING', 'CONFIRMED') 
    AND CONCAT(date, ' ', start_time) < NOW()
    AND date < CURDATE();
    
    -- Open cursor to handle points for expired bookings
    OPEN expired_cursor;
    
    read_loop: LOOP
        FETCH expired_cursor INTO booking_id, user_id, points_earned;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- If points were awarded during booking creation, remove them
        IF points_earned > 0 THEN
            -- Deduct points from user
            UPDATE users 
            SET points = GREATEST(0, points - points_earned), updated_at = NOW()
            WHERE id = user_id;
            
            -- Record point deduction
            INSERT INTO point_transactions (id, user_id, booking_id, points, type, description, created_at)
            VALUES (UUID(), user_id, booking_id, -points_earned, 'DEDUCTION', 'Points removed due to booking expiration', NOW());
            
            -- Update booking to remove earned points
            UPDATE bookings 
            SET points_earned = 0, updated_at = NOW()
            WHERE id = booking_id;
        END IF;
    END LOOP;
    
    CLOSE expired_cursor;
    COMMIT;
END //
DELIMITER ;

-- 6. Clean up existing point transactions for incomplete bookings
-- This will be handled by the PHP migration script for better control

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_status_date ON bookings(status, date);
CREATE INDEX IF NOT EXISTS idx_users_points ON users(points);

-- 8. Add last_expiration_check column to track when expiration was last checked
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

INSERT INTO system_settings (setting_key, setting_value) 
VALUES ('last_expiration_check', NOW()) 
ON DUPLICATE KEY UPDATE setting_value = NOW();

-- Verify the changes
SELECT 'Booking status enum updated' as status;
SHOW COLUMNS FROM bookings LIKE 'status';
SELECT 'Indexes created' as status;
SHOW INDEX FROM bookings WHERE Key_name LIKE 'idx_%';
