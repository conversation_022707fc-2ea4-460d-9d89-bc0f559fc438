<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

session_start();
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check authentication and staff role
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$staffId = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'GET':
            handleGetDashboard($staffId);
            break;
            
        case 'POST':
            handleStaffAction($staffId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetDashboard($staffId) {
    $action = $_GET['action'] ?? 'dashboard';
    
    switch ($action) {
        case 'dashboard':
            $data = getStaffDashboardData($staffId);
            break;
            
        case 'profile':
            $data = getStaffProfile($staffId);
            break;
            
        case 'today_schedule':
            $data = getTodaySchedule($staffId);
            break;
            
        case 'upcoming_appointments':
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
            $data = getUpcomingAppointments($staffId, $limit);
            break;
            
        case 'today_earnings':
            $data = getTodayEarnings($staffId);
            break;
            
        case 'monthly_stats':
            $data = getMonthlyStats($staffId);
            break;
            
        case 'performance_metrics':
            $data = getPerformanceMetrics($staffId);
            break;
            
        case 'recent_customers':
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 5;
            $data = getRecentCustomers($staffId, $limit);
            break;
            
        default:
            $data = getStaffDashboardData($staffId);
            break;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
}

function handleStaffAction($staffId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        return;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'update_appointment_status':
            handleUpdateAppointmentStatus($staffId, $input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handleUpdateAppointmentStatus($staffId, $input) {
    $requiredFields = ['appointmentId', 'status'];
    
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Field '$field' is required"]);
            return;
        }
    }
    
    $appointmentId = $input['appointmentId'];
    $status = $input['status'];
    $notes = $input['notes'] ?? null;
    
    updateAppointmentStatus($appointmentId, $staffId, $status, $notes);
    
    echo json_encode([
        'success' => true,
        'message' => 'Appointment status updated successfully'
    ]);
}
?>
