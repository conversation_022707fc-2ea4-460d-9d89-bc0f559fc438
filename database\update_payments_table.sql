-- Update payments table for Stripe and Flutterwave integration
-- Execute this script to update the payment system

USE flix_salonce2;

-- Update payments table structure
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS flutterwave_tx_ref VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS flutterwave_tx_id VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS payment_gateway ENUM('STRIPE', 'FLUTTERWAVE') DEFAULT 'STRIPE',
ADD COLUMN IF NOT EXISTS payment_reference VARCHAR(255) NULL,
ADD COLUMN IF NOT EXISTS payment_data JSON NULL,
ADD COLUMN IF NOT EXISTS webhook_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_attempts INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_verification_attempt TIMESTAMP NULL,
MODIFY COLUMN currency VARCHAR(3) DEFAULT 'TZS',
MODIFY COLUMN amount INT NOT NULL COMMENT 'Amount in smallest currency unit (cents for USD, kobo for NGN, etc)';

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_gateway ON payments(payment_gateway);
CREATE INDEX IF NOT EXISTS idx_payments_reference ON payments(payment_reference);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_booking ON payments(booking_id);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_id ON payments(stripe_payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_flutterwave_ref ON payments(flutterwave_tx_ref);

-- Update existing records to have proper currency
UPDATE payments SET currency = 'TZS' WHERE currency = 'USD' OR currency IS NULL;

-- Create payment_logs table for debugging and audit trail
CREATE TABLE IF NOT EXISTS payment_logs (
    id VARCHAR(36) PRIMARY KEY,
    payment_id VARCHAR(36) NOT NULL,
    event_type ENUM('CREATED', 'PROCESSING', 'COMPLETED', 'FAILED', 'WEBHOOK_RECEIVED', 'VERIFIED', 'REFUNDED') NOT NULL,
    gateway ENUM('STRIPE', 'FLUTTERWAVE') NOT NULL,
    event_data JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
);

-- Create payment_webhooks table for webhook management
CREATE TABLE IF NOT EXISTS payment_webhooks (
    id VARCHAR(36) PRIMARY KEY,
    gateway ENUM('STRIPE', 'FLUTTERWAVE') NOT NULL,
    webhook_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    payment_id VARCHAR(36) NULL,
    status ENUM('PENDING', 'PROCESSED', 'FAILED', 'IGNORED') DEFAULT 'PENDING',
    payload JSON NOT NULL,
    signature_verified BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE SET NULL
);

-- Add indexes for webhook tables
CREATE INDEX IF NOT EXISTS idx_payment_logs_payment ON payment_logs(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_event ON payment_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_gateway ON payment_webhooks(gateway);
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_status ON payment_webhooks(status);
CREATE INDEX IF NOT EXISTS idx_payment_webhooks_webhook_id ON payment_webhooks(webhook_id);

-- Insert system settings for payment configuration
INSERT INTO system_settings (setting_key, setting_value) VALUES 
('payment_enabled', 'true'),
('stripe_enabled', 'true'),
('flutterwave_enabled', 'true'),
('payment_timeout_minutes', '30'),
('max_verification_attempts', '3')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;
