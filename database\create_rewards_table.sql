-- Create rewards table for the loyalty system
USE flix_salonce;

CREATE TABLE IF NOT EXISTS rewards (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    points_required INT NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    type ENUM('discount', 'service', 'product', 'bonus') DEFAULT 'discount',
    is_active BOOLEAN DEFAULT TRUE,
    max_usage INT DEFAULT NULL COMMENT 'Maximum number of times this reward can be redeemed',
    usage_count INT DEFAULT 0 COMMENT 'Current number of times this reward has been redeemed',
    image VARCHAR(255) DEFAULT NULL,
    terms_conditions TEXT DEFAULT NULL,
    valid_from DATE DEFAULT NULL,
    valid_to DATE DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample rewards
INSERT INTO rewards (id, name, description, points_required, value, type, is_active) VALUES
('reward-1', 'TSH 5,000 Service Discount', 'Get TSH 5,000 off any service booking', 500, 5000.00, 'discount', TRUE),
('reward-2', 'TSH 10,000 Service Discount', 'Get TSH 10,000 off any service booking', 1000, 10000.00, 'discount', TRUE),
('reward-3', 'Free Basic Manicure', 'Complimentary basic manicure service', 1500, 25000.00, 'service', TRUE),
('reward-4', 'TSH 20,000 Package Discount', 'Get TSH 20,000 off any package booking', 2000, 20000.00, 'discount', TRUE),
('reward-5', 'Free Hair Wash & Blow Dry', 'Complimentary hair wash and blow dry service', 800, 15000.00, 'service', TRUE),
('reward-6', 'VIP Treatment Upgrade', 'Upgrade any service to VIP treatment', 1200, 30000.00, 'bonus', TRUE),
('reward-7', 'Birthday Special Package', 'Special birthday package with multiple services', 3000, 50000.00, 'service', TRUE),
('reward-8', 'TSH 15,000 Service Discount', 'Get TSH 15,000 off any service booking', 1500, 15000.00, 'discount', TRUE);

-- Create reward redemptions table to track customer redemptions
CREATE TABLE IF NOT EXISTS reward_redemptions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    reward_id VARCHAR(36) NOT NULL,
    points_used INT NOT NULL,
    status ENUM('PENDING', 'REDEEMED', 'EXPIRED', 'CANCELLED') DEFAULT 'PENDING',
    redeemed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reward_id) REFERENCES rewards(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_rewards_active ON rewards(is_active);
CREATE INDEX idx_rewards_points ON rewards(points_required);
CREATE INDEX idx_reward_redemptions_user ON reward_redemptions(user_id);
CREATE INDEX idx_reward_redemptions_status ON reward_redemptions(status);
