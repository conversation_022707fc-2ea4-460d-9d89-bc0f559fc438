<?php
/**
 * Our Team Page
 * Flix Salon & SPA - PHP Version
 */

require_once __DIR__ . '/config/app.php';

// Get staff members from the database
$sql = "SELECT id, name, role, image FROM users WHERE role = 'staff'";
$teamMembersData = $database->fetchAll($sql);

$teamMembers = [];
foreach ($teamMembersData as $row) {
    $specialties_sql = "
        SELECT s.name 
        FROM staff_specialties ss
        JOIN services s ON ss.service_id = s.id
        WHERE ss.user_id = ?
    ";
    $specialties = $database->fetchAll($specialties_sql, [$row['id']]);

    $teamMembers[] = [
        'id' => $row['id'],
        'name' => $row['name'],
        'role' => $row['role'],
        'specialties' => array_column($specialties, 'name'),
        'image' => $row['image'] ? getBasePath() . '/uploads/users/' . $row['image'] : ''
    ];
}

$pageTitle = "Our Team";
include __DIR__ . '/includes/header.php';
?>

<style>
/* Enhanced Team Page Styles */
.team-member-card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.9) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.1);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.team-member-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #D4AF37, transparent);
    transition: left 0.6s ease;
}

.team-member-card:hover::before {
    left: 100%;
}

.team-member-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.05) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-member-card:hover::after {
    opacity: 1;
}

.team-member-card:hover {
    transform: translateY(-12px) scale(1.02);
    border-color: rgba(212, 175, 55, 0.4);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5), 0 0 40px rgba(212, 175, 55, 0.15);
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

.specialty-tag {
    transition: all 0.3s ease;
}

.specialty-tag:hover {
    transform: scale(1.05);
    background: rgba(212, 175, 55, 0.2);
}

.social-link {
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: scale(1.2) rotate(10deg);
}

.profile-image {
    position: relative;
    overflow: hidden;
}

.profile-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.1) 0%, transparent 50%, rgba(212, 175, 55, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-member-card:hover .profile-image::before {
    opacity: 1;
}
</style>

<!-- Enhanced Hero Section -->
<section class="relative py-32 bg-gradient-to-br from-salon-black via-secondary-900 to-salon-black overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute top-20 left-10 w-40 h-40 bg-salon-gold/10 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute bottom-20 right-10 w-60 h-60 bg-salon-gold/5 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute inset-0 bg-gradient-to-r from-salon-gold/5 via-transparent to-salon-gold/3"></div>

    <!-- Parallax Background -->
    <div class="absolute inset-0 opacity-10">
        <div class="h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-6 text-center">
        <!-- Luxury Badge -->
        <div class="inline-flex items-center bg-salon-gold/10 text-salon-gold px-6 py-3 rounded-full text-sm font-semibold mb-8 border border-salon-gold/20 animate-on-scroll">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            Meet Our Team
        </div>

        <h1 class="text-6xl md:text-8xl font-bold font-serif text-white mb-8 leading-tight animate-on-scroll" style="--delay: 0.2s;">
            Meet Our
            <span class="block md:inline text-transparent bg-clip-text bg-gradient-to-r from-salon-gold via-yellow-400 to-salon-gold">
                Team
            </span>
        </h1>

        <p class="text-xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed mb-12 animate-on-scroll" style="--delay: 0.4s;">
            Our talented team of beauty professionals is dedicated to making you look and feel your absolute best.
            <span class="block mt-4 text-salon-gold text-lg md:text-xl">Meet the artists behind your transformation</span>
        </p>

        <!-- Team Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto animate-on-scroll" style="--delay: 0.6s;">
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-salon-gold mb-2">15+</div>
                <div class="text-gray-400 text-sm md:text-base">Expert Stylists</div>
            </div>
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-salon-gold mb-2">50+</div>
                <div class="text-gray-400 text-sm md:text-base">Years Combined</div>
            </div>
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-salon-gold mb-2">100%</div>
                <div class="text-gray-400 text-sm md:text-base">Certified</div>
            </div>
            <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-salon-gold mb-2">5K+</div>
                <div class="text-gray-400 text-sm md:text-base">Happy Clients</div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Team Grid -->
<section class="py-32 bg-secondary-900 relative">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #D4AF37 2px, transparent 2px), radial-gradient(circle at 75% 75%, #D4AF37 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 relative">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            <?php foreach ($teamMembers as $index => $member): ?>
                <div class="animate-on-scroll" style="--delay: <?= ($index * 0.1) ?>s;">
                    <div class="team-member-card rounded-3xl overflow-hidden relative">
                        <!-- Enhanced Member Photo -->
                        <div class="profile-image relative h-80 bg-gradient-to-br from-salon-gold/20 via-secondary-700 to-salon-gold/10 overflow-hidden">
                            <?php if ($member['image']): ?>
                                <img src="<?= htmlspecialchars($member['image']) ?>" alt="<?= htmlspecialchars($member['name']) ?>" class="w-full h-full object-cover transition-transform duration-500">
                            <?php else: ?>
                                <div class="w-full h-full flex items-center justify-center relative">
                                    <!-- Background Pattern -->
                                    <div class="absolute inset-0 opacity-20">
                                        <div class="w-full h-full" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'); background-size: cover; background-position: center;"></div>
                                    </div>

                                    <div class="text-center relative z-10">
                                        <div class="w-32 h-32 bg-salon-gold/30 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm border-2 border-salon-gold/30">
                                            <span class="text-salon-gold font-bold text-3xl">
                                                <?= strtoupper(substr($member['name'], 0, 1)) . strtoupper(substr(explode(' ', $member['name'])[1] ?? '', 0, 1)) ?>
                                            </span>
                                        </div>
                                        <div class="text-white text-lg font-semibold"><?= htmlspecialchars($member['role']) ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            
                            <!-- Professional Badge -->
                            <div class="absolute top-4 right-4 w-12 h-12 bg-salon-gold rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-black" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Enhanced Member Info -->
                        <div class="p-8">
                            <div class="text-center mb-6">
                                <h3 class="text-2xl font-bold text-white mb-2"><?= htmlspecialchars($member['name']) ?></h3>
                                <p class="text-salon-gold font-semibold text-lg mb-3"><?= htmlspecialchars($member['role']) ?></p>

                                                            </div>

                            <!-- Specialties -->
                            <div class="mb-6">
                                <h4 class="text-white font-semibold mb-3 text-center">Specialties</h4>
                                <div class="flex flex-wrap gap-2 justify-center">
                                    <?php foreach ($member['specialties'] as $specialty): ?>
                                        <span class="specialty-tag bg-salon-gold/15 text-salon-gold px-3 py-1 rounded-full text-sm font-medium border border-salon-gold/20">
                                            <?= htmlspecialchars($specialty) ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            
                            
                            <!-- Enhanced Book Button -->
                            <a href="<?= getBasePath() ?>/customer/book" class="w-full bg-gradient-to-r from-salon-gold to-yellow-500 hover:from-yellow-500 hover:to-salon-gold text-black py-3 px-6 rounded-xl font-bold transition-all hover:scale-105 shadow-lg text-center inline-block">
                                <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Book with <?= explode(' ', $member['name'])[0] ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Team Stats -->
<section class="py-16 bg-salon-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-4">Our Team by the Numbers</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-4xl font-bold text-salon-gold mb-2">6</div>
                <div class="text-gray-300">Team Members</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-salon-gold mb-2">45+</div>
                <div class="text-gray-300">Years Combined Experience</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-salon-gold mb-2">20+</div>
                <div class="text-gray-300">Professional Certifications</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-salon-gold mb-2">5K+</div>
                <div class="text-gray-300">Happy Clients Served</div>
            </div>
        </div>
    </div>
</section>

<!-- Team Philosophy -->
<section class="py-20 bg-secondary-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-white mb-8">Our Team Philosophy</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-heart text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-3">Passion for Beauty</h3>
                <p class="text-gray-300 text-sm">We're passionate about helping every client discover their unique beauty and express their personal style.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-graduation-cap text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-3">Continuous Learning</h3>
                <p class="text-gray-300 text-sm">Our team stays current with the latest techniques, trends, and products through ongoing education and training.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-salon-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-salon-gold text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-3">Collaborative Spirit</h3>
                <p class="text-gray-300 text-sm">We work together as a team, sharing knowledge and supporting each other to provide the best possible service.</p>
            </div>
        </div>
    </div>
</section>

<!-- Join Our Team CTA -->
<section class="py-20 bg-salon-black">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-secondary-800 rounded-lg p-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">Want to Join Our Team?</h2>
            <p class="text-gray-300 mb-8 max-w-2xl mx-auto">
                We're always looking for talented, passionate beauty professionals to join our growing team. 
                If you share our commitment to excellence and client satisfaction, we'd love to hear from you.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?= getBasePath() ?>/careers.php" class="bg-salon-gold hover:bg-yellow-500 text-black px-8 py-3 rounded-lg font-semibold transition-colors">
                    View Career Opportunities
                </a>
                <a href="<?= getBasePath() ?>/contact.php" class="bg-secondary-700 hover:bg-secondary-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors border border-secondary-600">
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

<script>
// Enhanced Team Page Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Scroll animations
    function handleScrollAnimations() {
        const elements = document.querySelectorAll('.animate-on-scroll');

        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate');
            }
        });
    }

    // Intersection Observer for better performance
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animated elements
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });

    // Enhanced hover effects for team cards
    document.querySelectorAll('.team-member-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Specialty tag hover effects
    document.querySelectorAll('.specialty-tag').forEach(tag => {
        tag.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.background = 'rgba(212, 175, 55, 0.2)';
        });

        tag.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.background = 'rgba(212, 175, 55, 0.15)';
        });
    });

    // Social link hover effects
    document.querySelectorAll('.social-link').forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.2) rotate(10deg)';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Initial check for elements already in view
    handleScrollAnimations();

    // Add loading animation to page
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>
