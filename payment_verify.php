<?php
// Include session configuration before starting session
include("session_config.php");
session_start();

// Include necessary files
include("../admin/inc/config.php");
include("../admin/inc/functions.php");
include("auto_cleanup.php");
// Fetch settings
$statement = $pdo->prepare("SELECT * FROM tbl_settings WHERE id=1");
$statement->execute();
$settings = $statement->fetch(PDO::FETCH_ASSOC);
$footer_copyright = isset($settings['footer_copyright']) ? $settings['footer_copyright'] : "© 2025 SMART LIFE. All rights reserved.";


// Retrieve the transaction reference from query parameters
if (!isset($_GET['tx_ref'])) {
    die("Transaction reference is missing.");
}
$tx_ref = $_GET['tx_ref'];

// Set up cURL to verify the transaction
$curl = curl_init();
curl_setopt_array($curl, array(
  CURLOPT_URL => "https://api.flutterwave.com/v3/transactions/verify_by_reference?tx_ref=" . $tx_ref,
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_CUSTOMREQUEST => "GET",
  CURLOPT_HTTPHEADER => array(
    "Content-Type: application/json",
    "Authorization: Bearer YOUR_SECRET_KEY"  // Replace with your Flutterwave secret key
  ),
));

$response = curl_exec($curl);
$err = curl_error($curl);
curl_close($curl);

if ($err) {
    die("cURL Error #: " . $err);
}

$result = json_decode($response, true);

// Check if the transaction was successful
if (isset($result['data']) && $result['data']['status'] === 'successful') {
    $payment_status = 'success';
} else {
    $payment_status = 'failed';
}

// Update the payment status in the orders table
$stmt = $pdo->prepare("UPDATE orders SET payment_status = ? WHERE tx_ref = ?");
$stmt->execute([$payment_status, $tx_ref]);

if ($payment_status === 'success') {
    // Clear the cart session if payment is successful
    unset($_SESSION['cart']);
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <title>Payment Successful</title>
      <link rel="stylesheet" href="css/style.css">
    </head>
    <body>

      <div class="container">
        <div class="success-message">
          <h1>Payment Successful</h1>
          <p>Thank you for your order!</p>
        </div>
      </div>

    </body>
    </html>
    <?php
} else {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <!-- The page will auto-redirect to cart.php after 10 seconds -->
      <meta http-equiv="refresh" content="5;url=cart.php">
      <title>Payment Verification Failed</title>
      <link rel="stylesheet" href="css/style.css">
      <style>
         /* Additional style to match your site's look */
         .error-container {
             max-width: 600px;
             margin: 100px auto;
             padding: 20px;
             text-align: center;
         }
         .error-container h1 {
             color: #e74c3c;
         }
         .error-container p {
             font-size: 16px;
         }
         .redirect {
             margin-top: 20px;
             font-style: italic;
             color: #555;
         }
      </style>
    </head>
    <body>
        <header>
             <div class="container">
                <a href="index.php" class="logo">SMART LIFE<span>.</span></a>
                <div class="nav-links">
                 <a href="index.php">Home</a>
                 <a href="index.php#about">About</a>
                 <a href="index.php#products">Products</a>
                 <a href="index.php#gallery">Gallery</a>
                 <a href="index.php#contact">Contact</a>
                 <div class="cart-icon">
                    <a href="cart.php"><i class="fas fa-shopping-cart"></i> <span class="cart-count">0</span></a>
                 </div>
                </div>
             </div>
      <div class="container">
         <div class="error-container">
           <h1>Payment Verification Failed</h1>
           <p>Your transaction was not successful. Please try again.</p>
           <p class="redirect">You will be redirected to the cart page in 10 seconds.</p>
         </div>
      </div>
      <footer>
    <div class="container">
      <div class="footer-bottom">
        <div class="copyright"><?php echo htmlspecialchars($footer_copyright); ?></div>
      </div>
    </div>
  </footer>
    </body>
    </html>
    <?php
}
?>
