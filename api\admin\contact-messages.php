<?php
/**
 * Admin Contact Messages API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/contact_functions.php';
require_once __DIR__ . '/../../includes/email_functions.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'GET') {
        $action = $_GET['action'] ?? 'list';
        
        switch ($action) {
            case 'get':
                $id = $_GET['id'] ?? '';
                if (empty($id)) {
                    throw new Exception('Message ID is required');
                }
                
                $message = getContactMessage($id);
                if (!$message) {
                    throw new Exception('Message not found');
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => $message
                ]);
                break;
                
            case 'list':
                $filters = [
                    'status' => $_GET['status'] ?? '',
                    'search' => $_GET['search'] ?? '',
                    'date_from' => $_GET['date_from'] ?? '',
                    'date_to' => $_GET['date_to'] ?? '',
                    'subject' => $_GET['subject'] ?? ''
                ];
                
                $page = (int)($_GET['page'] ?? 1);
                $limit = (int)($_GET['limit'] ?? 20);
                
                $result = getContactMessages($filters, $page, $limit);
                
                echo json_encode([
                    'success' => true,
                    'data' => $result
                ]);
                break;
                
            case 'stats':
                $stats = getContactMessageStats();
                
                echo json_encode([
                    'success' => true,
                    'stats' => $stats
                ]);
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } elseif ($method === 'POST') {
        // Get JSON input
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('Invalid JSON input');
        }
        
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'update_status':
                $id = $input['id'] ?? '';
                $status = $input['status'] ?? '';
                
                if (empty($id) || empty($status)) {
                    throw new Exception('Message ID and status are required');
                }
                
                $result = updateContactMessageStatus($id, $status);
                
                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Message status updated successfully'
                    ]);
                } else {
                    throw new Exception('Failed to update message status');
                }
                break;
                
            case 'delete':
                $id = $input['id'] ?? '';
                
                if (empty($id)) {
                    throw new Exception('Message ID is required');
                }
                
                $result = deleteContactMessage($id);
                
                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Message deleted successfully'
                    ]);
                } else {
                    throw new Exception('Failed to delete message');
                }
                break;
                
            case 'reply':
                $id = $input['id'] ?? '';
                $replyMessage = $input['reply_message'] ?? '';
                
                if (empty($id) || empty($replyMessage)) {
                    throw new Exception('Message ID and reply message are required');
                }
                
                // Get the original message
                $originalMessage = getContactMessage($id);
                if (!$originalMessage) {
                    throw new Exception('Original message not found');
                }
                
                // Send email reply to customer
                $emailSent = sendContactReplyEmail($id, $replyMessage);
                
                if (!$emailSent) {
                    throw new Exception('Failed to send email reply to customer');
                }
                
                // Mark the message as replied
                $result = markContactMessageReplied($id, $replyMessage);
                
                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Reply sent successfully to ' . $originalMessage['email']
                    ]);
                } else {
                    throw new Exception('Email sent but failed to update message status');
                }
                break;
                
            case 'bulk_update':
                $ids = $input['ids'] ?? [];
                $bulkAction = $input['bulk_action'] ?? '';
                
                if (empty($ids) || empty($bulkAction)) {
                    throw new Exception('Message IDs and bulk action are required');
                }
                
                $result = bulkUpdateContactMessages($ids, $bulkAction);
                
                if ($result) {
                    $actionText = [
                        'mark_read' => 'marked as read',
                        'archive' => 'archived',
                        'delete' => 'deleted'
                    ];
                    
                    echo json_encode([
                        'success' => true,
                        'message' => count($ids) . ' message(s) ' . ($actionText[$bulkAction] ?? 'updated') . ' successfully'
                    ]);
                } else {
                    throw new Exception('Failed to perform bulk action');
                }
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

?>
