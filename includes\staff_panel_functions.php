<?php
require_once __DIR__ . '/../config/database.php';

/**
 * Staff Panel Functions
 * Handles staff-specific operations including dashboard, schedule, appointments, and earnings
 */

/**
 * Get comprehensive staff dashboard data
 */
function getStaffDashboardData($staffId) {
    return [
        'profile' => getStaffProfile($staffId),
        'todaySchedule' => getTodaySchedule($staffId),
        'upcomingAppointments' => getUpcomingAppointments($staffId, 5),
        'todayEarnings' => getTodayEarnings($staffId),
        'monthlyStats' => getMonthlyStats($staffId),
        'recentCustomers' => getRecentCustomers($staffId, 5),
        'performanceMetrics' => getPerformanceMetrics($staffId)
    ];
}

/**
 * Get staff profile information
 */
function getStaffProfile($staffId) {
    global $database;

    // Get user information
    $profile = $database->fetch("
        SELECT
            u.id, u.name, u.email, u.phone, u.role, u.image as avatar, u.is_active, u.created_at
        FROM users u
        WHERE u.id = ? AND u.role = 'STAFF'
    ", [$staffId]);

    if (!$profile) {
        throw new Exception("Staff member not found");
    }

    // Get staff schedule info (role, hourly_rate, bio, experience)
    $staffInfo = $database->fetch("
        SELECT role as staff_role, hourly_rate, bio, experience
        FROM staff_schedules
        WHERE user_id = ?
        LIMIT 1
    ", [$staffId]);

    // Merge staff info with profile
    if ($staffInfo) {
        $profile = array_merge($profile, $staffInfo);
    }

    // Get specialties from staff_specialties table
    $specialties = $database->fetchAll("
        SELECT s.name
        FROM staff_specialties sp
        JOIN services s ON sp.service_id = s.id
        WHERE sp.user_id = ?
    ", [$staffId]);

    $profile['specialties'] = array_column($specialties, 'name');
    $profile['hourly_rate'] = $profile['hourly_rate'] ?? 50.00;
    $profile['bio'] = $profile['bio'] ?? '';
    $profile['experience'] = $profile['experience'] ?? 0;
    $profile['staff_role'] = $profile['staff_role'] ?? 'Staff Member';

    return $profile;
}

/**
 * Get today's schedule for staff member
 */
function getTodaySchedule($staffId) {
    global $database;

    $today = date('Y-m-d');

    // Get working hours for today
    $dayOfWeek = strtolower(date('l'));
    $workingHours = $database->fetch("
        SELECT start_time, end_time, is_working
        FROM staff_schedules
        WHERE user_id = ? AND day_of_week = ?
    ", [$staffId, $dayOfWeek]);

    // If no specific day schedule found, try to get from JSON schedule
    if (!$workingHours) {
        $scheduleData = $database->fetch("
            SELECT schedule
            FROM staff_schedules
            WHERE user_id = ? AND schedule IS NOT NULL
            LIMIT 1
        ", [$staffId]);

        if ($scheduleData && $scheduleData['schedule']) {
            $schedule = json_decode($scheduleData['schedule'], true);
            if (isset($schedule[$dayOfWeek])) {
                $daySchedule = $schedule[$dayOfWeek];
                $workingHours = [
                    'start_time' => $daySchedule['start_time'] ?? $daySchedule['start'] ?? '09:00',
                    'end_time' => $daySchedule['end_time'] ?? $daySchedule['end'] ?? '17:00',
                    'is_working' => $daySchedule['is_working'] ?? $daySchedule['available'] ?? true
                ];
            }
        }
    }

    // Get today's appointments
    $appointments = $database->fetchAll("
        SELECT
            b.*,
            u.name as customer_name,
            u.phone as customer_phone,
            s.name as service_name,
            s.duration as service_duration
        FROM bookings b
        LEFT JOIN users u ON b.user_id = u.id
        LEFT JOIN services s ON b.service_id = s.id
        WHERE b.staff_id = ? AND DATE(b.date) = ?
        ORDER BY b.start_time ASC
    ", [$staffId, $today]);

    return [
        'date' => $today,
        'dayOfWeek' => $dayOfWeek,
        'workingHours' => $workingHours,
        'appointments' => $appointments,
        'totalAppointments' => count($appointments),
        'isWorkingDay' => $workingHours ? ($workingHours['is_working'] ?? false) : false
    ];
}

/**
 * Get upcoming appointments for staff member
 */
function getUpcomingAppointments($staffId, $limit = 10) {
    global $database;
    
    $appointments = $database->fetchAll("
        SELECT 
            b.*,
            u.name as customer_name,
            u.phone as customer_phone,
            u.email as customer_email,
            s.name as service_name,
            s.duration as service_duration,
            s.price as service_price
        FROM bookings b
        LEFT JOIN users u ON b.user_id = u.id
        LEFT JOIN services s ON b.service_id = s.id
        WHERE b.staff_id = ? 
        AND b.date >= CURDATE()
        AND b.status NOT IN ('CANCELLED', 'NO_SHOW')
        ORDER BY b.date ASC, b.start_time ASC
        LIMIT ?
    ", [$staffId, $limit]);
    
    return $appointments;
}

/**
 * Get today's earnings for staff member
 */
function getTodayEarnings($staffId) {
    global $database;
    
    $today = date('Y-m-d');
    
    $earnings = $database->fetch("
        SELECT 
            COUNT(*) as completed_appointments,
            COALESCE(SUM(total_amount), 0) as total_revenue,
            COALESCE(SUM(total_amount * 0.6), 0) as commission_earned
        FROM bookings 
        WHERE staff_id = ? 
        AND DATE(date) = ?
        AND status = 'COMPLETED'
    ", [$staffId, $today]);
    
    return [
        'completedAppointments' => intval($earnings['completed_appointments']),
        'totalRevenue' => floatval($earnings['total_revenue']),
        'commissionEarned' => floatval($earnings['commission_earned']),
        'commissionRate' => 60 // 60% commission rate
    ];
}

/**
 * Get monthly statistics for staff member
 */
function getMonthlyStats($staffId) {
    global $database;
    
    $currentMonth = date('Y-m-01');
    $nextMonth = date('Y-m-01', strtotime('+1 month'));
    
    $stats = $database->fetch("
        SELECT 
            COUNT(*) as total_appointments,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_appointments,
            COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_appointments,
            COUNT(CASE WHEN status = 'NO_SHOW' THEN 1 END) as no_show_appointments,
            COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_revenue,
            COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount * 0.6 ELSE 0 END), 0) as commission_earned
        FROM bookings 
        WHERE staff_id = ? 
        AND date >= ? 
        AND date < ?
    ", [$staffId, $currentMonth, $nextMonth]);
    
    // Calculate completion rate
    $completionRate = $stats['total_appointments'] > 0 
        ? ($stats['completed_appointments'] / $stats['total_appointments']) * 100 
        : 0;
    
    return [
        'totalAppointments' => intval($stats['total_appointments']),
        'completedAppointments' => intval($stats['completed_appointments']),
        'cancelledAppointments' => intval($stats['cancelled_appointments']),
        'noShowAppointments' => intval($stats['no_show_appointments']),
        'totalRevenue' => floatval($stats['total_revenue']),
        'commissionEarned' => floatval($stats['commission_earned']),
        'completionRate' => round($completionRate, 1),
        'period' => date('F Y')
    ];
}

/**
 * Get recent customers for staff member
 */
function getRecentCustomers($staffId, $limit = 5) {
    global $database;
    
    $customers = $database->fetchAll("
        SELECT DISTINCT
            u.id,
            u.name,
            u.email,
            u.phone,
            u.points,
            COUNT(b.id) as total_visits,
            MAX(b.date) as last_visit,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as total_spent
        FROM users u
        JOIN bookings b ON u.id = b.user_id
        WHERE b.staff_id = ?
        AND u.role = 'CUSTOMER'
        GROUP BY u.id, u.name, u.email, u.phone, u.points
        ORDER BY MAX(b.date) DESC
        LIMIT ?
    ", [$staffId, $limit]);
    
    return $customers;
}

/**
 * Get performance metrics for staff member
 */
function getPerformanceMetrics($staffId) {
    global $database;
    
    // Get overall stats
    $overallStats = $database->fetch("
        SELECT 
            COUNT(*) as total_bookings,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_bookings,
            COALESCE(AVG(CASE WHEN status = 'COMPLETED' THEN total_amount END), 0) as avg_service_value,
            COALESCE(SUM(CASE WHEN status = 'COMPLETED' THEN total_amount ELSE 0 END), 0) as total_revenue
        FROM bookings 
        WHERE staff_id = ?
    ", [$staffId]);
    
    // Calculate metrics
    $completionRate = $overallStats['total_bookings'] > 0 
        ? ($overallStats['completed_bookings'] / $overallStats['total_bookings']) * 100 
        : 0;
    
    // Get this month vs last month comparison
    $thisMonth = date('Y-m-01');
    $lastMonth = date('Y-m-01', strtotime('-1 month'));
    
    $thisMonthStats = $database->fetch("
        SELECT COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed
        FROM bookings 
        WHERE staff_id = ? AND date >= ?
    ", [$staffId, $thisMonth]);
    
    $lastMonthStats = $database->fetch("
        SELECT COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed
        FROM bookings 
        WHERE staff_id = ? AND date >= ? AND date < ?
    ", [$staffId, $lastMonth, $thisMonth]);
    
    $monthlyGrowth = $lastMonthStats['completed'] > 0 
        ? (($thisMonthStats['completed'] - $lastMonthStats['completed']) / $lastMonthStats['completed']) * 100 
        : 0;
    
    return [
        'totalBookings' => intval($overallStats['total_bookings']),
        'completedBookings' => intval($overallStats['completed_bookings']),
        'completionRate' => round($completionRate, 1),
        'avgServiceValue' => floatval($overallStats['avg_service_value']),
        'totalRevenue' => floatval($overallStats['total_revenue']),
        'monthlyGrowth' => round($monthlyGrowth, 1),
        'thisMonthCompleted' => intval($thisMonthStats['completed']),
        'lastMonthCompleted' => intval($lastMonthStats['completed'])
    ];
}

/**
 * Update appointment status by staff
 */
function updateAppointmentStatus($appointmentId, $staffId, $status, $notes = null) {
    global $database;
    
    // Verify appointment belongs to staff
    $appointment = $database->fetch("
        SELECT id, staff_id, status 
        FROM bookings 
        WHERE id = ? AND staff_id = ?
    ", [$appointmentId, $staffId]);
    
    if (!$appointment) {
        throw new Exception("Appointment not found or access denied");
    }
    
    // Valid status transitions
    $validStatuses = ['CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW'];
    if (!in_array($status, $validStatuses)) {
        throw new Exception("Invalid status");
    }
    
    $updateData = [
        'status' => $status,
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    if ($notes !== null) {
        $updateData['notes'] = $notes;
    }
    
    $setClause = implode(', ', array_map(function($key) {
        return "$key = ?";
    }, array_keys($updateData)));
    
    $params = array_values($updateData);
    $params[] = $appointmentId;
    
    $database->execute("
        UPDATE bookings 
        SET $setClause
        WHERE id = ?
    ", $params);
    
    return true;
}

/**
 * Get staff schedule for a specific week
 */
function getStaffWeeklySchedule($staffId, $weekStart = null) {
    global $database;

    if (!$weekStart) {
        $weekStart = date('Y-m-d', strtotime('monday this week'));
    }

    $schedule = [];
    $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    // Get working hours for each day from individual day records
    $workingHours = $database->fetchAll("
        SELECT day_of_week, start_time, end_time, is_working
        FROM staff_schedules
        WHERE user_id = ? AND day_of_week IS NOT NULL
    ", [$staffId]);

    $workingHoursMap = [];
    foreach ($workingHours as $hours) {
        $workingHoursMap[$hours['day_of_week']] = $hours;
    }

    // If no individual day records, try to get from JSON schedule
    if (empty($workingHoursMap)) {
        $scheduleData = $database->fetch("
            SELECT schedule
            FROM staff_schedules
            WHERE user_id = ? AND schedule IS NOT NULL
            LIMIT 1
        ", [$staffId]);

        if ($scheduleData && $scheduleData['schedule']) {
            $jsonSchedule = json_decode($scheduleData['schedule'], true);
            foreach ($daysOfWeek as $day) {
                if (isset($jsonSchedule[$day])) {
                    $dayData = $jsonSchedule[$day];
                    $workingHoursMap[$day] = [
                        'day_of_week' => $day,
                        'start_time' => $dayData['start_time'] ?? $dayData['start'] ?? '09:00',
                        'end_time' => $dayData['end_time'] ?? $dayData['end'] ?? '17:00',
                        'is_working' => $dayData['is_working'] ?? $dayData['available'] ?? true
                    ];
                }
            }
        }
    }

    // Build schedule for the week
    for ($i = 0; $i < 7; $i++) {
        $date = date('Y-m-d', strtotime($weekStart . " +$i days"));
        $dayName = $daysOfWeek[$i];

        $daySchedule = [
            'date' => $date,
            'dayName' => ucfirst($dayName),
            'workingHours' => $workingHoursMap[$dayName] ?? null,
            'appointments' => []
        ];

        // Get appointments for this day if working
        $isWorking = isset($workingHoursMap[$dayName]) && ($workingHoursMap[$dayName]['is_working'] ?? false);
        if ($isWorking) {
            $appointments = $database->fetchAll("
                SELECT
                    b.*,
                    u.name as customer_name,
                    s.name as service_name
                FROM bookings b
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN services s ON b.service_id = s.id
                WHERE b.staff_id = ? AND DATE(b.date) = ?
                ORDER BY b.start_time ASC
            ", [$staffId, $date]);

            $daySchedule['appointments'] = $appointments;
        }

        $schedule[] = $daySchedule;
    }

    return $schedule;
}

/**
 * Update staff working hours
 */
function updateStaffWorkingHours($staffId, $schedule) {
    global $database;

    try {
        $database->beginTransaction();

        // Get existing staff profile data to preserve role and hourly_rate
        $existingProfile = $database->fetch("
            SELECT role, hourly_rate, bio, experience, specialties, is_active
            FROM staff_schedules
            WHERE user_id = ? AND day_of_week IS NULL
            LIMIT 1
        ", [$staffId]);

        // Delete ALL existing schedule records for this staff member
        $database->execute("DELETE FROM staff_schedules WHERE user_id = ?", [$staffId]);

        // Create the main profile record with preserved data
        $mainScheduleId = generateUUID();
        $database->execute("
            INSERT INTO staff_schedules (id, user_id, role, hourly_rate, bio, experience, specialties, is_active, schedule, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ", [
            $mainScheduleId,
            $staffId,
            $existingProfile['role'] ?? 'Staff Member',
            $existingProfile['hourly_rate'] ?? 500000,
            $existingProfile['bio'] ?? null,
            $existingProfile['experience'] ?? 0,
            $existingProfile['specialties'] ?? null,
            $existingProfile['is_active'] ?? 1,
            json_encode($schedule)
        ]);

        // Create individual day records for working days only
        foreach ($schedule as $day => $hours) {
            if (isset($hours['is_working']) && $hours['is_working']) {
                $dayScheduleId = generateUUID();
                $database->execute("
                    INSERT INTO staff_schedules (id, user_id, day_of_week, start_time, end_time, is_working, role, hourly_rate, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, 1, ?, ?, NOW(), NOW())
                ", [
                    $dayScheduleId,
                    $staffId,
                    $day,
                    $hours['start_time'],
                    $hours['end_time'],
                    $existingProfile['role'] ?? 'Staff Member',
                    $existingProfile['hourly_rate'] ?? 500000
                ]);
            }
        }

        $database->commit();
        return true;

    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

/**
 * Get staff earnings for a specific period
 */
function getStaffEarnings($staffId, $startDate, $endDate) {
    global $database;
    
    $earnings = $database->fetchAll("
        SELECT 
            DATE(b.date) as date,
            COUNT(*) as appointments,
            COUNT(CASE WHEN b.status = 'COMPLETED' THEN 1 END) as completed,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount ELSE 0 END), 0) as revenue,
            COALESCE(SUM(CASE WHEN b.status = 'COMPLETED' THEN b.total_amount * 0.6 ELSE 0 END), 0) as commission
        FROM bookings b
        WHERE b.staff_id = ? 
        AND DATE(b.date) BETWEEN ? AND ?
        GROUP BY DATE(b.date)
        ORDER BY DATE(b.date) ASC
    ", [$staffId, $startDate, $endDate]);
    
    return $earnings;
}

/**
 * Get staff notifications with filters and pagination
 */
function getStaffNotifications($staffId, $options = []) {
    global $database;
    
    $limit = $options['limit'] ?? 20;
    $offset = $options['offset'] ?? 0;
    $category = $options['category'] ?? 'all';
    $status = $options['status'] ?? 'all';
    $search = $options['search'] ?? '';
    $sort = $options['sort'] ?? 'newest';
    
    $conditions = ["n.user_id = ?"];
    $params = [$staffId];
    
    if ($category !== 'all') {
        $conditions[] = "n.category = ?";
        $params[] = $category;
    }
    
    if ($status !== 'all') {
        $conditions[] = "n.is_read = ?";
        $params[] = $status === 'read' ? 1 : 0;
    }
    
    if ($search) {
        $conditions[] = "(n.title LIKE ? OR n.message LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $conditions);
    
    $orderBy = match($sort) {
        'oldest' => 'n.created_at ASC',
        'priority' => 'n.priority DESC, n.created_at DESC',
        default => 'n.created_at DESC'
    };
    
    $notifications = $database->fetchAll("
        SELECT 
            n.*,
            TIMESTAMPDIFF(MINUTE, n.created_at, NOW()) as minutes_ago,
            CASE 
                WHEN TIMESTAMPDIFF(MINUTE, n.created_at, NOW()) < 60 
                    THEN CONCAT(TIMESTAMPDIFF(MINUTE, n.created_at, NOW()), ' minutes ago')
                WHEN TIMESTAMPDIFF(HOUR, n.created_at, NOW()) < 24 
                    THEN CONCAT(TIMESTAMPDIFF(HOUR, n.created_at, NOW()), ' hours ago')
                WHEN TIMESTAMPDIFF(DAY, n.created_at, NOW()) < 7 
                    THEN CONCAT(TIMESTAMPDIFF(DAY, n.created_at, NOW()), ' days ago')
                ELSE DATE_FORMAT(n.created_at, '%M %d, %Y')
            END as time_ago
        FROM notifications n
        $whereClause
        ORDER BY $orderBy
        LIMIT ? OFFSET ?
    ", [...$params, $limit, $offset]);
    
    return $notifications;
}

/**
 * Get total count of staff notifications with filters
 */
function getStaffNotificationsCount($staffId, $options = []) {
    global $database;
    
    $category = $options['category'] ?? 'all';
    $status = $options['status'] ?? 'all';
    $search = $options['search'] ?? '';
    
    $conditions = ["n.user_id = ?"];
    $params = [$staffId];
    
    if ($category !== 'all') {
        $conditions[] = "n.category = ?";
        $params[] = $category;
    }
    
    if ($status !== 'all') {
        $conditions[] = "n.is_read = ?";
        $params[] = $status === 'read' ? 1 : 0;
    }
    
    if ($search) {
        $conditions[] = "(n.title LIKE ? OR n.message LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $conditions);
    
    $result = $database->fetch("
        SELECT COUNT(*) as count
        FROM notifications n
        $whereClause
    ", $params);
    
    return $result['count'];
}

/**
 * Get simple unread notification count for a user
 */
function getUnreadNotificationCount($userId) {
    global $database;

    try {
        $result = $database->fetch("
            SELECT COUNT(*) as count
            FROM notifications
            WHERE user_id = ? AND is_read = 0
        ", [$userId]);

        return (int)($result['count'] ?? 0);

    } catch (Exception $e) {
        error_log("Error getting unread notification count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get notification category counts for staff
 */
function getNotificationCategoryCounts($staffId) {
    global $database;
    
    try {
        // Get total counts
        $counts = $database->fetch("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read
            FROM notifications
            WHERE user_id = ?
        ", [$staffId]);

        // Get category counts
        $categories = $database->fetchAll("
            SELECT 
                category,
                COUNT(*) as total,
                SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread
            FROM notifications
            WHERE user_id = ?
            GROUP BY category
        ", [$staffId]);

        // Format category counts
        $categoryCounts = [];
        foreach ($categories as $category) {
            $categoryCounts[$category['category']] = [
                'total' => (int)$category['total'],
                'unread' => (int)$category['unread']
            ];
        }

        return [
            'total' => (int)($counts['total'] ?? 0),
            'unread' => (int)($counts['unread'] ?? 0),
            'read' => (int)($counts['read'] ?? 0),
            'categories' => $categoryCounts
        ];
    } catch (Exception $e) {
        error_log("Error in getNotificationCategoryCounts: " . $e->getMessage());
        return [
            'total' => 0,
            'unread' => 0,
            'read' => 0,
            'categories' => []
        ];
    }
}

/**
 * Mark notification as read
 */
function markNotificationAsRead($notificationId, $staffId) {
    global $database;
    
    return $database->execute("
        UPDATE notifications 
        SET is_read = 1, updated_at = NOW()
        WHERE id = ? AND user_id = ?
    ", [$notificationId, $staffId]);
}

/**
 * Mark all notifications as read
 */
function markAllNotificationsAsRead($staffId) {
    global $database;
    
    return $database->execute("
        UPDATE notifications 
        SET is_read = 1, updated_at = NOW()
        WHERE user_id = ? AND is_read = 0
    ", [$staffId]);
}

/**
 * Delete notification
 */
function deleteNotification($notificationId, $staffId) {
    global $database;
    
    return $database->execute("
        DELETE FROM notifications 
        WHERE id = ? AND user_id = ?
    ", [$notificationId, $staffId]);
}

/**
 * Delete all notifications
 */
function deleteAllNotifications($staffId) {
    global $database;
    
    return $database->execute("
        DELETE FROM notifications 
        WHERE user_id = ?
    ", [$staffId]);
}
