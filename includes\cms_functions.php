<?php
require_once __DIR__ . '/../config/database.php';

/**
 * CMS and Gallery Management Functions
 * Handles content management, gallery operations, and website content updates
 */

/**
 * Get comprehensive CMS data for dashboard
 */
function getCMSData() {
    return [
        'heroContent' => getHeroContent(),
        'contentSections' => getContentSections(),
        'galleryImages' => getGalleryImages(),
        'galleryCategories' => getGalleryCategories(),
        'contentTypes' => getContentTypes(),
        'stats' => getCMSStatistics()
    ];
}

/**
 * Get hero section content
 */
function getHeroContent() {
    global $database;
    
    $hero = $database->fetch("
        SELECT * FROM cms_content
        WHERE section = 'hero' AND is_active = 1
        ORDER BY created_at DESC
        LIMIT 1
    ");
    
    if (!$hero) {
        // Return default hero content
        return [
            'id' => null,
            'section' => 'hero',
            'title' => 'Welcome to Flix Salonce',
            'content' => 'Experience luxury beauty services in our premium salon',
            'image' => '/images/hero-bg.jpg',
            'display_order' => 1,
            'is_active' => 1
        ];
    }
    
    return $hero;
}

/**
 * Get all content sections
 */
function getContentSections($section = null) {
    global $database;
    
    $conditions = [];
    $params = [];
    
    if ($section) {
        $conditions[] = "section = ?";
        $params[] = $section;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $sections = $database->fetchAll("
        SELECT * FROM cms_content
        $whereClause
        ORDER BY section ASC, created_at ASC
    ", $params);
    
    return $sections;
}

/**
 * Get gallery images with optional filtering
 */
function getGalleryImages($category = null, $isActive = null) {
    global $database;
    
    $conditions = [];
    $params = [];
    
    if ($category) {
        $conditions[] = "category = ?";
        $params[] = $category;
    }
    
    if ($isActive !== null) {
        $conditions[] = "is_active = ?";
        $params[] = $isActive ? 1 : 0;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    $images = $database->fetchAll("
        SELECT * FROM gallery
        $whereClause
        ORDER BY category ASC, created_at DESC
    ", $params);
    
    return $images;
}

/**
 * Get gallery categories
 */
function getGalleryCategories() {
    return [
        'Hair Styling',
        'Nail Art',
        'Skincare',
        'Bridal',
        'Before & After',
        'Salon Interior'
    ];
}

/**
 * Get content section types
 */
function getContentTypes() {
    return [
        'hero' => 'Hero Section',
        'about' => 'About Us',
        'services-intro' => 'Services Introduction',
        'testimonials' => 'Testimonials',
        'why-choose-us' => 'Why Choose Us',
        'contact-info' => 'Contact Information',
        'footer' => 'Footer Content'
    ];
}

/**
 * Create or update content section
 */
function saveContentSection($data) {
    global $database;
    
    // Validate required fields
    $required = ['section', 'title', 'content'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }
    
    if (isset($data['id']) && !empty($data['id'])) {
        // Update existing content
        return updateContentSection($data['id'], $data);
    } else {
        // Create new content
        return createContentSection($data);
    }
}

/**
 * Create new content section
 */
function createContentSection($data) {
    global $database;
    
    $id = generateUUID();
    
    $sql = "INSERT INTO cms_content (
                id, section, title, content, image, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";

    $params = [
        $id,
        $data['section'],
        $data['title'],
        $data['content'],
        $data['image'] ?? null,
        $data['is_active'] ?? 1
    ];
    
    $database->execute($sql, $params);
    
    return getContentSectionById($id);
}

/**
 * Update existing content section
 */
function updateContentSection($id, $data) {
    global $database;
    
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['section', 'title', 'content', 'image', 'is_active'];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $id;
    
    $sql = "UPDATE cms_content SET " . implode(', ', $updateFields) . " WHERE id = ?";
    
    $database->execute($sql, $params);
    
    return getContentSectionById($id);
}

/**
 * Get content section by ID
 */
function getContentSectionById($id) {
    global $database;
    
    return $database->fetch("SELECT * FROM cms_content WHERE id = ?", [$id]);
}

/**
 * Delete content section
 */
function deleteContentSection($id) {
    global $database;
    
    $content = getContentSectionById($id);
    if (!$content) {
        throw new Exception("Content section not found");
    }
    
    $database->execute("DELETE FROM cms_content WHERE id = ?", [$id]);
    
    return true;
}

/**
 * Add gallery image
 */
function addGalleryImage($data) {
    global $database;
    
    // Validate required fields
    $required = ['title', 'image_url', 'category'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }
    
    // Validate category
    $validCategories = getGalleryCategories();
    if (!in_array($data['category'], $validCategories)) {
        throw new Exception("Invalid category");
    }
    
    $id = generateUUID();
    
    $sql = "INSERT INTO gallery (
                id, title, description, image_url, category, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";

    $params = [
        $id,
        $data['title'],
        $data['description'] ?? '',
        $data['image_url'],
        $data['category'],
        $data['is_active'] ?? 1
    ];
    
    $database->execute($sql, $params);
    
    return getGalleryImageById($id);
}

/**
 * Update gallery image
 */
function updateGalleryImage($id, $data) {
    global $database;
    
    $image = getGalleryImageById($id);
    if (!$image) {
        throw new Exception("Gallery image not found");
    }
    
    // Validate category if provided
    if (isset($data['category'])) {
        $validCategories = getGalleryCategories();
        if (!in_array($data['category'], $validCategories)) {
            throw new Exception("Invalid category");
        }
    }
    
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['title', 'description', 'image_url', 'category', 'is_active'];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception("No fields to update");
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $id;
    
    $sql = "UPDATE gallery SET " . implode(', ', $updateFields) . " WHERE id = ?";
    
    $database->execute($sql, $params);
    
    return getGalleryImageById($id);
}

/**
 * Get gallery image by ID
 */
function getGalleryImageById($id) {
    global $database;
    
    return $database->fetch("SELECT * FROM gallery WHERE id = ?", [$id]);
}

/**
 * Delete gallery image
 */
function deleteGalleryImage($id) {
    global $database;
    
    $image = getGalleryImageById($id);
    if (!$image) {
        throw new Exception("Gallery image not found");
    }
    
    $database->execute("DELETE FROM gallery WHERE id = ?", [$id]);
    
    return true;
}

/**
 * Get CMS statistics
 */
function getCMSStatistics() {
    global $database;
    
    $stats = [];
    
    // Total content sections
    $stats['totalContent'] = $database->fetch("SELECT COUNT(*) as count FROM cms_content")['count'];
    
    // Active content sections
    $stats['activeContent'] = $database->fetch("SELECT COUNT(*) as count FROM cms_content WHERE is_active = 1")['count'];
    
    // Total gallery images
    $stats['totalImages'] = $database->fetch("SELECT COUNT(*) as count FROM gallery")['count'];
    
    // Active gallery images
    $stats['activeImages'] = $database->fetch("SELECT COUNT(*) as count FROM gallery WHERE is_active = 1")['count'];
    
    // Images by category
    $categoryStats = $database->fetchAll("
        SELECT category, COUNT(*) as count 
        FROM gallery 
        WHERE is_active = 1 
        GROUP BY category 
        ORDER BY count DESC
    ");
    
    $stats['imagesByCategory'] = $categoryStats;
    
    // Recent updates
    $stats['recentUpdates'] = $database->fetchAll("
        SELECT 'content' as type, section as name, updated_at 
        FROM cms_content 
        WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        UNION ALL
        SELECT 'gallery' as type, title as name, updated_at 
        FROM gallery 
        WHERE updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY updated_at DESC 
        LIMIT 5
    ");
    
    return $stats;
}

/**
 * Get public gallery for frontend
 */
function getPublicGallery($category = null, $limit = null) {
    global $database;
    
    $conditions = ["is_active = 1"];
    $params = [];
    
    if ($category && $category !== 'All') {
        $conditions[] = "category = ?";
        $params[] = $category;
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $conditions);
    $limitClause = $limit ? "LIMIT $limit" : '';
    
    $images = $database->fetchAll("
        SELECT id, title, description, image_url, category
        FROM gallery
        $whereClause
        ORDER BY created_at DESC
        $limitClause
    ", $params);
    
    return $images;
}

/**
 * Get public content for frontend
 */
function getPublicContent($section = null) {
    global $database;
    
    $conditions = ["is_active = 1"];
    $params = [];
    
    if ($section) {
        $conditions[] = "section = ?";
        $params[] = $section;
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $conditions);
    
    $content = $database->fetchAll("
        SELECT section, title, content, image
        FROM cms_content
        $whereClause
        ORDER BY section ASC, created_at ASC
    ", $params);
    
    return $content;
}
