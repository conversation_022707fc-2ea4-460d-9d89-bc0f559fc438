<?php
/**
 * Admin Booking Management Functions
 * Handles booking status changes and points management
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/booking_expiration.php';
require_once __DIR__ . '/booking_functions.php';

// All booking status and points functions moved to booking_functions.php to avoid duplication

/**
 * Get booking statistics with new status
 */
function getBookingStatistics() {
    global $database;
    
    $stats = [];
    
    // Count by status
    $statusCounts = $database->fetchAll("
        SELECT status, COUNT(*) as count
        FROM bookings
        GROUP BY status
    ");
    
    foreach ($statusCounts as $row) {
        $stats['by_status'][$row['status']] = $row['count'];
    }
    
    // Total bookings
    $stats['total'] = array_sum($stats['by_status']);
    
    // Revenue from completed bookings only
    $stats['total_revenue'] = $database->fetch("
        SELECT COALESCE(SUM(total_amount), 0) as revenue
        FROM bookings
        WHERE status = 'COMPLETED'
    ")['revenue'];
    
    // Points awarded (only from completed bookings)
    $stats['points_awarded'] = $database->fetch("
        SELECT COALESCE(SUM(points_earned), 0) as points
        FROM bookings
        WHERE status = 'COMPLETED'
    ")['points'];
    
    return $stats;
}
?>
