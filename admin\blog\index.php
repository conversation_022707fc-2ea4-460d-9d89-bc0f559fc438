<?php
/**
 * Admin Blog Post Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/blog_functions.php'; // We'll create this next

// Require admin authentication
$auth->requireRole('ADMIN');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    // CSRF Token Validation (implement if not already globally handled)
    // if (!verifyCsrfToken($_POST['csrf_token'] ?? '')) {
    //     $_SESSION['error'] = 'Invalid request. Please try again.';
    //     redirect('/admin/blog');
    // }

    switch ($action) {
        case 'create':
            $result = createBlogPost($_POST, $_FILES['image_url'] ?? null);
            if ($result['success']) {
                $_SESSION['success'] = 'Blog post created successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
                // Store form data to repopulate if needed
                $_SESSION['form_data'] = $_POST;
            }
            break;
            
        case 'update':
            $result = updateBlogPost($_POST['id'], $_POST, $_FILES['image_url'] ?? null);
            if ($result['success']) {
                $_SESSION['success'] = 'Blog post updated successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
                 // Store form data to repopulate if needed
                $_SESSION['form_data'] = $_POST;
            }
            break;
            
        case 'delete':
            $result = deleteBlogPost($_POST['id']);
            if ($result['success']) {
                $_SESSION['success'] = 'Blog post deleted successfully!';
            } else {
                $_SESSION['error'] = $result['error'];
            }
            break;
    }
    
    redirect('/admin/blog');
}

// Get all blog posts with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 10; // Number of posts per page
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$statusFilter = sanitize($_GET['status'] ?? ''); // Filter by status

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (title LIKE ? OR summary LIKE ? OR full_content LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($statusFilter) {
    $whereClause .= " AND status = ?";
    $params[] = $statusFilter;
}

$blogPosts = $database->fetchAll(
    "SELECT bp.*, u.name as author_name
     FROM blog_posts bp
     LEFT JOIN users u ON bp.author_id = u.id
     $whereClause
     ORDER BY bp.publish_date DESC, bp.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalBlogPosts = $database->fetch(
    "SELECT COUNT(*) as count FROM blog_posts $whereClause",
    $params
)['count'];

$totalPages = ceil($totalBlogPosts / $limit);

$pageTitle = "Blog Post Management";
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                <!-- Header -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-white">Blog Post Management</h1>
                            <p class="mt-1 text-sm text-gray-300">Manage your blog posts, content, and publication status.</p>
                        </div>
                        <div class="mt-4 sm:mt-0">
                            <button onclick="openCreateModal()" class="bg-salon-gold text-black px-4 py-2 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Blog Post
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Search posts by title, summary, content..." 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="status" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Statuses</option>
                                <option value="draft" <?= $statusFilter === 'draft' ? 'selected' : '' ?>>Draft</option>
                                <option value="published" <?= $statusFilter === 'published' ? 'selected' : '' ?>>Published</option>
                                <option value="archived" <?= $statusFilter === 'archived' ? 'selected' : '' ?>>Archived</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if ($search || $statusFilter): ?>
                            <a href="<?= getBasePath() ?>/admin/blog" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Blog Posts Table -->
                <div class="bg-secondary-800 shadow rounded-lg overflow-x-auto">
                    <table class="min-w-full divide-y divide-secondary-700">
                        <thead class="bg-secondary-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Title</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Summary</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Author</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Publish Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                            <?php if (empty($blogPosts)): ?>
                                <tr>
                                    <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-400 text-center">No blog posts found.</td>
                                </tr>
                            <?php endif; ?>
                            <?php foreach ($blogPosts as $post): ?>
                                <tr class="hover:bg-secondary-700/50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                                        <?= htmlspecialchars($post['title']) ?>
                                        <?php if ($post['image_url']): ?>
                                            <img src="<?= htmlspecialchars(getBlogImageUrl($post['image_url'])) ?>" alt="<?= htmlspecialchars($post['title']) ?>" class="mt-2 h-10 w-16 object-cover rounded">
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-normal text-sm text-gray-300 max-w-xs truncate"><?= htmlspecialchars($post['summary']) ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300"><?= htmlspecialchars($post['author_name'] ?? 'N/A') ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        <?= $post['publish_date'] ? date('M j, Y, g:i a', strtotime($post['publish_date'])) : 'Not Set' ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?= $post['status'] === 'published' ? 'bg-green-100 text-green-800' : ($post['status'] === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') ?>">
                                            <?= ucfirst(htmlspecialchars($post['status'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="editBlogPost('<?= $post['id'] ?>')" class="text-blue-400 hover:text-blue-300 mr-3">Edit</button>
                                        <button onclick="deleteBlogPostConfirm('<?= $post['id'] ?>', '<?= htmlspecialchars(addslashes($post['title'])) ?>')" class="text-red-400 hover:text-red-300">Delete</button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-b-lg mt-0">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-300">
                                    Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalBlogPosts) ?></span> of <span class="font-medium"><?= $totalBlogPosts ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($statusFilter) ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Create/Edit Blog Post Modal -->
<div id="blogPostModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-3xl mx-auto max-h-full overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 id="modalTitle" class="text-xl font-bold text-white">Add Blog Post</h2>
            <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="blogPostForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="create">
            <input type="hidden" name="id" id="postId">
            <input type="hidden" name="csrf_token" value="<?= generateCsrfToken() ?>">


            <div class="mb-4">
                <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title *</label>
                <input type="text" name="title" id="title" required 
                       class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
            </div>

            <div class="mb-4">
                <label for="summary" class="block text-sm font-medium text-gray-300 mb-2">Summary</label>
                <textarea name="summary" id="summary" rows="3"
                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
            </div>

            <div class="mb-4">
                <label for="full_content" class="block text-sm font-medium text-gray-300 mb-2">Full Content *</label>
                <textarea name="full_content" id="full_content" rows="10" required
                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"></textarea>
                <p class="text-xs text-gray-400 mt-1">You can use basic HTML tags for formatting.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="image_url_option" class="block text-sm font-medium text-gray-300 mb-2">Image</label>
                     <select name="image_url_option" id="image_url_option" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold mb-2">
                        <option value="url">Enter Image URL</option>
                        <option value="upload">Upload Image</option>
                    </select>

                    <div id="image_url_field">
                        <input type="url" name="image_url_input" id="image_url_input" placeholder="https://example.com/image.jpg"
                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                    </div>
                    <div id="image_upload_field" class="hidden">
                        <input type="file" name="image_url" id="image_upload_input" accept="image/*"
                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-salon-gold file:text-black hover:file:bg-gold-light">
                        <p class="text-xs text-gray-400 mt-1">Max 5MB. Recommended: JPG, PNG, WebP.</p>
                    </div>
                     <input type="hidden" name="existing_image_url" id="existing_image_url">
                     <div id="currentImagePreview" class="mt-2"></div>
                </div>

                <div>
                    <label for="publish_date" class="block text-sm font-medium text-gray-300 mb-2">Publish Date</label>
                    <input type="datetime-local" name="publish_date" id="publish_date"
                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                 <div>
                    <label for="author_id" class="block text-sm font-medium text-gray-300 mb-2">Author</label>
                    <select name="author_id" id="author_id" class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="">Select Author (Optional)</option>
                        <?php
                        $staffMembers = $database->fetchAll("SELECT id, name FROM users WHERE role IN ('ADMIN', 'STAFF') AND is_active = 1 ORDER BY name ASC");
                        foreach ($staffMembers as $staff): ?>
                            <option value="<?= $staff['id'] ?>"><?= htmlspecialchars($staff['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Status *</label>
                    <select name="status" id="status" required
                            class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        <option value="draft">Draft</option>
                        <option value="published">Published</option>
                        <option value="archived">Archived</option>
                    </select>
                </div>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 bg-salon-gold text-black py-2 px-4 rounded-lg font-semibold hover:bg-gold-light transition-colors">
                    Save Blog Post
                </button>
                <button type="button" onclick="closeModal()" class="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal (standard, can be reused) -->
<div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-lg mx-4 border border-red-600">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0 mr-3">
                <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 id="deleteModalTitle" class="text-lg font-semibold text-white">Delete Blog Post</h3>
        </div>
        <div id="deleteMessage" class="text-gray-300 mb-6"></div>
        <div class="flex gap-3 justify-end">
            <button id="deleteCancel" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                Cancel
            </button>
            <button id="deleteConfirm" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold">
                Delete
            </button>
        </div>
    </div>
</div>


<script>
// Store form data from session if available (e.g., after a failed validation)
const formErrorData = <?= isset($_SESSION['form_data']) ? json_encode($_SESSION['form_data']) : 'null' ?>;
<?php unset($_SESSION['form_data']); ?>

function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Add Blog Post';
    document.getElementById('formAction').value = 'create';
    document.getElementById('blogPostForm').reset();
    document.getElementById('postId').value = '';
    document.getElementById('status').value = 'draft'; // Default status
    document.getElementById('currentImagePreview').innerHTML = '';
    document.getElementById('existing_image_url').value = '';
    
    // Set author to current logged-in admin if possible, or leave blank
    // This requires knowing the current admin's ID, which might be in $_SESSION['user']['id']
    // For now, let's default to blank or a specific admin if known.
    // document.getElementById('author_id').value = '<?= $_SESSION['user']['id'] ?? '' ?>'; 

    // Repopulate form if there was an error
    if (formErrorData && formErrorData.action === 'create') {
        for (const key in formErrorData) {
            const field = document.getElementById(key);
            if (field) {
                field.value = formErrorData[key];
            }
        }
    }
    
    // Reset image input type
    document.getElementById('image_url_option').value = 'url';
    toggleImageInput();

    document.getElementById('blogPostModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('blogPostModal').classList.add('hidden');
}

function editBlogPost(postId) {
    fetch(`<?= getBasePath() ?>/api/admin/blog.php?action=get&id=${postId}`) // We'll create this API endpoint
        .then(response => response.json())
        .then(data => {
            if (data.success && data.post) {
                const post = data.post;
                document.getElementById('modalTitle').textContent = 'Edit Blog Post';
                document.getElementById('formAction').value = 'update';
                document.getElementById('postId').value = post.id;
                document.getElementById('title').value = post.title;
                document.getElementById('summary').value = post.summary || '';
                document.getElementById('full_content').value = post.full_content;
                
                // Handle image URL or uploaded file
                document.getElementById('existing_image_url').value = post.image_url || '';
                const imagePreview = document.getElementById('currentImagePreview');
                imagePreview.innerHTML = '';
                if (post.image_url) {
                    const imgTag = document.createElement('img');
                    imgTag.src = post.image_url.startsWith('http') ? post.image_url : `<?= getBasePath() ?>/uploads/blog/${post.image_url}`;
                    imgTag.alt = 'Current Image';
                    imgTag.className = 'w-32 h-32 object-cover rounded-lg mt-2';
                    imagePreview.appendChild(imgTag);

                    // Determine if it's a URL or an uploaded file to set the select option
                    if (post.image_url.startsWith('http')) {
                        document.getElementById('image_url_option').value = 'url';
                        document.getElementById('image_url_input').value = post.image_url;
                    } else {
                         // If it's an uploaded file, we don't re-populate the file input for security.
                         // User will have to re-upload if they want to change it.
                         // We could show the filename, but for simplicity, just show preview.
                        document.getElementById('image_url_option').value = 'upload'; // Or keep as URL and let them switch
                    }
                } else {
                     document.getElementById('image_url_option').value = 'url'; // Default if no image
                }
                toggleImageInput();


                document.getElementById('publish_date').value = post.publish_date ? post.publish_date.replace(' ', 'T') : '';
                document.getElementById('author_id').value = post.author_id || '';
                document.getElementById('status').value = post.status;

                // Repopulate form if there was an error during update attempt
                if (formErrorData && formErrorData.action === 'update' && formErrorData.id === postId) {
                    for (const key in formErrorData) {
                        const field = document.getElementById(key);
                        if (field) {
                            field.value = formErrorData[key];
                        }
                    }
                }

                document.getElementById('blogPostModal').classList.remove('hidden');
            } else {
                alert('Error fetching blog post details: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error fetching blog post:', error);
            alert('Failed to load blog post data.');
        });
}

function deleteBlogPostConfirm(postId, postTitle) {
    const deleteModal = document.getElementById('deleteModal');
    document.getElementById('deleteModalTitle').textContent = 'Delete Blog Post';
    document.getElementById('deleteMessage').innerHTML = `<p>Are you sure you want to delete the blog post titled "<strong>${postTitle}</strong>"?</p><p class="text-sm text-gray-400 mt-2">This action cannot be undone.</p>`;
    
    const confirmBtn = document.getElementById('deleteConfirm');
    confirmBtn.onclick = () => {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= getBasePath() ?>/admin/blog/'; // Post to current page
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'delete';
        form.appendChild(actionInput);
        
        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        idInput.value = postId;
        form.appendChild(idInput);

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= generateCsrfToken() ?>'; // Ensure CSRF token is included
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
        deleteModal.classList.add('hidden');
    };
    
    document.getElementById('deleteCancel').onclick = () => {
        deleteModal.classList.add('hidden');
    };
    
    deleteModal.classList.remove('hidden');
}

// Image input switcher
document.getElementById('image_url_option').addEventListener('change', toggleImageInput);

function toggleImageInput() {
    const option = document.getElementById('image_url_option').value;
    const urlField = document.getElementById('image_url_field');
    const uploadField = document.getElementById('image_upload_field');
    const urlInput = document.getElementById('image_url_input');
    const uploadInput = document.getElementById('image_upload_input');

    if (option === 'url') {
        urlField.classList.remove('hidden');
        uploadField.classList.add('hidden');
        uploadInput.value = ''; // Clear file input if switching
    } else {
        urlField.classList.add('hidden');
        uploadField.classList.remove('hidden');
        urlInput.value = ''; // Clear URL input if switching
    }
}

// Initialize image input state on load
document.addEventListener('DOMContentLoaded', function() {
    toggleImageInput();

    // Add rich text editor if available (e.g., TinyMCE)
    // if (typeof tinymce !== 'undefined') {
    //     tinymce.init({
    //         selector: '#full_content',
    //         plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak media table paste code help wordcount',
    //         toolbar: 'undo redo | formatselect | bold italic backcolor | \
    //                   alignleft aligncenter alignright alignjustify | \
    //                   bullist numlist outdent indent | removeformat | help | code | table | media | image link',
    //         skin: (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'oxide-dark' : 'oxide'),
    //         content_css: (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'default'),
    //         height: 400,
    //         // Add image upload handler if needed
    //     });
    // }
});


// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
        const deleteModal = document.getElementById('deleteModal');
        if (!deleteModal.classList.contains('hidden')) {
            deleteModal.classList.add('hidden');
        }
    }
});

// Close modal on backdrop click
document.getElementById('blogPostModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
    }
});

</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>