<?php
/**
 * Search Services API
 * Flix Salonce - PHP Version
 * Handles service and package search with category and subcategory filters
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is logged in as customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    // Get search parameters
    $searchTerm = $_GET['search'] ?? '';
    $selectedCategory = $_GET['category'] ?? '';
    $selectedSubcategory = $_GET['subcategory'] ?? '';

    // Build query for services
    $whereClause = "WHERE s.is_active = 1";
    $params = [];
    $joins = "LEFT JOIN service_categories sc ON s.category_id = sc.id LEFT JOIN service_subcategories ss ON s.subcategory_id = ss.id";

    if ($searchTerm) {
        $whereClause .= " AND (s.name LIKE ? OR s.description LIKE ?)";
        $params[] = "%$searchTerm%";
        $params[] = "%$searchTerm%";
    }

    if ($selectedCategory) {
        $whereClause .= " AND sc.name = ?";
        $params[] = $selectedCategory;
    }

    if ($selectedSubcategory) {
        $whereClause .= " AND ss.name = ?";
        $params[] = $selectedSubcategory;
    }

    // Get services with variations data using robust subquery approach
    $services = $database->fetchAll("
        SELECT s.*,
               sc.name as category_name,
               ss.name as subcategory_name,
               COALESCE(v.variation_count, 0) as variation_count,
               v.variations_data
        FROM services s
        $joins
        LEFT JOIN (
            SELECT service_id,
                   COUNT(*) as variation_count,
                   GROUP_CONCAT(
                       CONCAT(id, ':', name, ':', price, ':', duration, ':', is_active)
                       ORDER BY sort_order ASC, name ASC
                       SEPARATOR '|'
                   ) as variations_data
            FROM service_variations
            WHERE is_active = 1
            GROUP BY service_id
        ) v ON s.id = v.service_id
        $whereClause
        ORDER BY s.name ASC
    ", $params);

    // Parse variations data for each service and ensure no duplicates
    $uniqueServices = [];
    $seenServiceIds = [];

    foreach ($services as $service) {
        // Skip if we've already seen this service ID (extra safety measure)
        if (isset($seenServiceIds[$service['id']])) {
            continue;
        }
        $seenServiceIds[$service['id']] = true;

        $service['variations'] = [];
        if ($service['variations_data']) {
            $variationsData = explode('|', $service['variations_data']);
            foreach ($variationsData as $varData) {
                $parts = explode(':', $varData);
                if (count($parts) >= 5) {
                    $service['variations'][] = [
                        'id' => $parts[0],
                        'name' => $parts[1],
                        'price' => floatval($parts[2]),
                        'duration' => intval($parts[3]),
                        'is_active' => (bool)$parts[4]
                    ];
                }
            }
        }
        unset($service['variations_data']);

        $uniqueServices[] = $service;
    }

    // Replace the original services array with the deduplicated one
    $services = $uniqueServices;

    // Get packages (no category/subcategory filtering for now)
    $packageWhereClause = "WHERE p.is_active = 1";
    $packageParams = [];

    if ($searchTerm) {
        $packageWhereClause .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $packageParams[] = "%$searchTerm%";
        $packageParams[] = "%$searchTerm%";
    }

    $packages = $database->fetchAll("
        SELECT p.*
        FROM packages p
        $packageWhereClause
        ORDER BY p.name ASC
    ", $packageParams);

    // Get package services for each package
    foreach ($packages as $index => $package) {
        $packages[$index]['services'] = $database->fetchAll("
            SELECT s.* FROM services s
            INNER JOIN package_services ps ON s.id = ps.service_id
            WHERE ps.package_id = ?
            ORDER BY s.name
        ", [$package['id']]);

        // Calculate total duration for package
        $packages[$index]['total_duration'] = array_sum(array_column($packages[$index]['services'], 'duration'));

        // Calculate savings
        $originalPrice = array_sum(array_column($packages[$index]['services'], 'price'));
        $packages[$index]['savings'] = max(0, $originalPrice - $package['price']);
    }

    echo json_encode([
        'success' => true,
        'services' => $services,
        'packages' => $packages
    ]);

} catch (Exception $e) {
    error_log("Search services error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
} 