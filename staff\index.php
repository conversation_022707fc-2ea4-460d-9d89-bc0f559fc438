<?php
session_start();
require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/staff_panel_functions.php';

// Check if user is staff
if (!isLoggedIn() || $_SESSION['user_role'] !== 'STAFF') {
    redirect('/auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_appointment_status':
                    $appointmentId = $_POST['appointment_id'];
                    $status = $_POST['status'];
                    $notes = $_POST['notes'] ?? null;

                    updateAppointmentStatus($appointmentId, $_SESSION['user_id'], $status, $notes);
                    $message = 'Appointment status updated successfully!';
                    $messageType = 'success';
                    break;
            }
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get staff dashboard data
try {
    $staffId = $_SESSION['user_id'];
    $dashboardData = getStaffDashboardData($staffId);
} catch (Exception $e) {
    $message = 'Error loading dashboard data: ' . $e->getMessage();
    $messageType = 'error';
    $dashboardData = [
        'profile' => ['name' => $_SESSION['user_name'] ?? 'Staff Member'],
        'todaySchedule' => ['totalAppointments' => 0, 'appointments' => [], 'isWorkingDay' => false],
        'upcomingAppointments' => [],
        'todayEarnings' => ['commissionEarned' => 0],
        'monthlyStats' => ['completedAppointments' => 0],
        'performanceMetrics' => ['completionRate' => 0, 'totalBookings' => 0, 'completedBookings' => 0, 'avgServiceValue' => 0, 'totalRevenue' => 0, 'monthlyGrowth' => 0],
        'recentCustomers' => []
    ];
}

$pageTitle = "Staff Dashboard";
include __DIR__ . '/../includes/staff_header.php';
?>

<!-- Message Display -->
<?php if ($message): ?>
    <div class="mb-8 p-6 rounded-xl <?= $messageType === 'success' ? 'bg-salon-gold/10 border border-salon-gold text-salon-gold' : 'bg-red-500/10 border border-red-500 text-red-400' ?> backdrop-blur-sm">
        <div class="flex items-center">
            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?> mr-3 text-xl"></i>
            <?= htmlspecialchars($message) ?>
        </div>
    </div>
<?php endif; ?>

<!-- Page Header -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 p-8 hover-lift">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-white font-serif">Staff <span class="text-salon-gold">Dashboard</span></h1>
            <p class="mt-2 text-lg text-gray-300">Welcome back, <?= htmlspecialchars($dashboardData['profile']['name']) ?>!</p>
        </div>
        <div class="mt-6 sm:mt-0">
            <div class="text-right">
                <p class="text-sm text-gray-400"><?= date('l, F j, Y') ?></p>
                <p class="text-xl font-semibold text-salon-gold" data-time><?= date('g:i A') ?></p>
            </div>
        </div>
    </div>
</div>


<!-- Stats Overview -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <!-- Today's Appointments -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Today's Appointments</dt>
                        <dd class="text-2xl font-bold text-white" id="today-appointments"><?= $dashboardData['todaySchedule']['totalAppointments'] ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Earnings -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Today's Commission</dt>
                        <dd class="text-2xl font-bold text-salon-gold" id="today-earnings"><?= formatCurrency($dashboardData['todayEarnings']['commissionEarned']) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Completed -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Monthly Completed</dt>
                        <dd class="text-2xl font-bold text-white" id="monthly-bookings"><?= number_format($dashboardData['monthlyStats']['completedAppointments']) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Completion Rate -->
    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
        <div class="p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-salon-gold/20 rounded-full flex items-center justify-center">
                        <svg class="h-6 w-6 text-salon-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">Completion Rate</dt>
                        <dd class="text-2xl font-bold text-salon-gold" id="completion-rate"><?= $dashboardData['performanceMetrics']['completionRate'] ?>%</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Schedule -->
<div class="bg-secondary-800 shadow overflow-hidden sm:rounded-md mb-8">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-white">Today's Schedule</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-300">Your appointments and schedule for today</p>
    </div>
    <?php if (!$dashboardData['todaySchedule']['isWorkingDay']): ?>
        <div class="text-center py-8">
            <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h4 class="text-lg font-semibold text-white mb-2">Day Off</h4>
            <p class="text-gray-300">You're not scheduled to work today</p>
        </div>
    <?php elseif (empty($dashboardData['todaySchedule']['appointments'])): ?>
        <div class="text-center py-8">
            <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h4 class="text-lg font-semibold text-white mb-2">No Appointments</h4>
            <p class="text-gray-300">No appointments scheduled for today</p>
            <p class="text-sm text-gray-400 mt-2">
                Working hours: <?= $dashboardData['todaySchedule']['workingHours']['start_time'] ?? 'N/A' ?> -
                <?= $dashboardData['todaySchedule']['workingHours']['end_time'] ?? 'N/A' ?>
            </p>
        </div>
    <?php else: ?>
        <ul class="divide-y divide-secondary-700">
            <?php foreach ($dashboardData['todaySchedule']['appointments'] as $appointment): ?>
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-salon-gold flex items-center justify-center">
                                        <span class="text-sm font-medium text-black">
                                            <?= strtoupper(substr($appointment['customer_name'], 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-white">
                                        <?= htmlspecialchars($appointment['customer_name']) ?>
                                    </div>
                                    <div class="text-sm text-gray-300">
                                        <?= htmlspecialchars($appointment['service_name']) ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <div class="text-sm text-white">
                                        <?= date('g:i A', strtotime($appointment['start_time'])) ?>
                                    </div>
                                    <div class="text-sm text-salon-gold">
                                        <?= formatCurrency($appointment['total_amount'] ?? 0) ?>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getStatusBadgeClass($appointment['status']) ?>">
                                    <?= ucfirst(strtolower($appointment['status'])) ?>
                                </span>
                            </div>
                        </div>

                        <?php if ($appointment['status'] === 'CONFIRMED'): ?>
                            <div class="mt-3 flex items-center space-x-2">
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="update_appointment_status">
                                    <input type="hidden" name="appointment_id" value="<?= $appointment['id'] ?>">
                                    <input type="hidden" name="status" value="IN_PROGRESS">
                                    <button type="submit" class="text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors">
                                        Start Service
                                    </button>
                                </form>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="update_appointment_status">
                                    <input type="hidden" name="appointment_id" value="<?= $appointment['id'] ?>">
                                    <input type="hidden" name="status" value="COMPLETED">
                                    <button type="submit" class="text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors">
                                        Complete
                                    </button>
                                </form>
                            </div>
                        <?php elseif ($appointment['status'] === 'IN_PROGRESS'): ?>
                            <div class="mt-3">
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="update_appointment_status">
                                    <input type="hidden" name="appointment_id" value="<?= $appointment['id'] ?>">
                                    <input type="hidden" name="status" value="COMPLETED">
                                    <button type="submit" class="text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded transition-colors">
                                        Mark Complete
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </li>
            <?php endforeach; ?>
        </ul>
    <?php endif; ?>
    <div class="bg-secondary-700 px-4 py-3 sm:px-6">
        <div class="flex justify-between">
            <div></div>
            <a href="<?= getBasePath() ?>/staff/appointments" class="text-sm font-medium text-salon-gold hover:text-gold-light transition-colors">
                View all appointments →
            </a>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
    <a href="<?= getBasePath() ?>/staff/appointments" class="bg-secondary-800 p-6 rounded-lg hover:bg-secondary-700 transition-colors group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-white group-hover:text-gray-100">Manage Appointments</h3>
                <p class="text-sm text-gray-300">View and update appointment statuses</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/schedule" class="bg-secondary-800 p-6 rounded-lg hover:bg-secondary-700 transition-colors group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-white group-hover:text-gray-100">My Schedule</h3>
                <p class="text-sm text-gray-300">View and manage working hours</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/notifications" class="bg-secondary-800 p-6 rounded-lg hover:bg-secondary-700 transition-colors group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-white group-hover:text-gray-100">Notifications</h3>
                <p class="text-sm text-gray-300">View and manage your notifications</p>
            </div>
        </div>
    </a>

    <a href="<?= getBasePath() ?>/staff/earnings" class="bg-secondary-800 p-6 rounded-lg hover:bg-secondary-700 transition-colors group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-salon-gold group-hover:text-gold-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-white group-hover:text-gray-100">Track Earnings</h3>
                <p class="text-sm text-gray-300">View commission and performance</p>
            </div>
        </div>
    </a>
</div>
<?php include __DIR__ . '/../includes/staff_footer.php'; ?>
