<?php
/**
 * Fix Staff Specialties Script
 * This script ensures all staff members have at least some specialties
 * Run this if staff suggestions are not working due to missing specialties data
 */

require_once __DIR__ . '/config/app.php';

echo "<h1>Staff Specialties Fix Script</h1>";

try {
    // Check current state
    $totalStaff = $database->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'STAFF'")['count'];
    $totalSpecialties = $database->fetch("SELECT COUNT(*) as count FROM staff_specialties")['count'];
    $activeServices = $database->fetch("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'];
    
    echo "<h2>Current State</h2>";
    echo "<p>Total Staff Members: " . $totalStaff . "</p>";
    echo "<p>Total Staff Specialties: " . $totalSpecialties . "</p>";
    echo "<p>Active Services: " . $activeServices . "</p>";
    
    // Check which staff members have no specialties
    $staffWithoutSpecialties = $database->fetchAll("
        SELECT u.id, u.name 
        FROM users u 
        LEFT JOIN staff_specialties ss ON u.id = ss.user_id 
        WHERE u.role = 'STAFF' AND u.is_active = 1 AND ss.user_id IS NULL
    ");
    
    echo "<h2>Staff Members Without Specialties</h2>";
    if (!empty($staffWithoutSpecialties)) {
        echo "<p style='color: orange;'>Found " . count($staffWithoutSpecialties) . " staff members without specialties:</p>";
        echo "<ul>";
        foreach ($staffWithoutSpecialties as $staff) {
            echo "<li>" . htmlspecialchars($staff['name']) . " (ID: " . htmlspecialchars($staff['id']) . ")</li>";
        }
        echo "</ul>";
        
        // Ask for confirmation before proceeding
        echo "<h2>Fix Action</h2>";
        echo "<p>This script will assign all active services as 'INTERMEDIATE' level specialties to staff members who have no specialties.</p>";
        echo "<p><strong>Click the button below to proceed with the fix:</strong></p>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='fix_specialties'>";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fix Staff Specialties</button>";
        echo "</form>";
        
    } else {
        echo "<p style='color: green;'>✓ All active staff members have specialties assigned.</p>";
    }
    
    // Handle the fix action
    if (isset($_POST['action']) && $_POST['action'] === 'fix_specialties') {
        echo "<h2>Fixing Staff Specialties...</h2>";
        
        $database->beginTransaction();
        
        try {
            $fixedCount = 0;
            $specialtiesAdded = 0;
            
            // Get all active services
            $activeServicesList = $database->fetchAll("SELECT id, name FROM services WHERE is_active = 1");
            
            foreach ($staffWithoutSpecialties as $staff) {
                echo "<p>Processing " . htmlspecialchars($staff['name']) . "...</p>";
                
                foreach ($activeServicesList as $service) {
                    // Generate unique ID for specialty
                    $specialtyId = 'specialty-' . $staff['id'] . '-' . $service['id'];
                    
                    // Insert specialty record
                    $database->query("
                        INSERT IGNORE INTO staff_specialties (id, user_id, service_id, proficiency_level, created_at, updated_at)
                        VALUES (?, ?, ?, 'INTERMEDIATE', NOW(), NOW())
                    ", [$specialtyId, $staff['id'], $service['id']]);
                    
                    $specialtiesAdded++;
                }
                
                $fixedCount++;
            }
            
            $database->commit();
            
            echo "<p style='color: green;'>✓ Successfully fixed specialties for " . $fixedCount . " staff members.</p>";
            echo "<p style='color: green;'>✓ Added " . $specialtiesAdded . " specialty records.</p>";
            
            // Verify the fix
            $newTotalSpecialties = $database->fetch("SELECT COUNT(*) as count FROM staff_specialties")['count'];
            echo "<p>New total specialties count: " . $newTotalSpecialties . "</p>";
            
        } catch (Exception $e) {
            $database->rollback();
            echo "<p style='color: red;'>✗ Error fixing specialties: " . $e->getMessage() . "</p>";
        }
    }
    
    // Show current specialties summary
    echo "<h2>Current Specialties Summary</h2>";
    $specialtiesSummary = $database->fetchAll("
        SELECT 
            u.name as staff_name,
            COUNT(ss.id) as specialty_count,
            GROUP_CONCAT(s.name SEPARATOR ', ') as services
        FROM users u
        LEFT JOIN staff_specialties ss ON u.id = ss.user_id
        LEFT JOIN services s ON ss.service_id = s.id
        WHERE u.role = 'STAFF' AND u.is_active = 1
        GROUP BY u.id, u.name
        ORDER BY specialty_count DESC, u.name ASC
    ");
    
    if (!empty($specialtiesSummary)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Staff Name</th><th>Specialties Count</th><th>Services</th></tr>";
        foreach ($specialtiesSummary as $summary) {
            $color = $summary['specialty_count'] > 0 ? 'green' : 'red';
            echo "<tr>";
            echo "<td>" . htmlspecialchars($summary['staff_name']) . "</td>";
            echo "<td style='color: $color;'>" . $summary['specialty_count'] . "</td>";
            echo "<td>" . htmlspecialchars($summary['services'] ?? 'None') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>Test the staff suggestions API after running this fix</li>";
echo "<li>Check the admin panel to ensure staff status is displaying correctly</li>";
echo "<li>Remove this script file after confirming everything works</li>";
echo "</ul>";

echo "<p><a href='debug-staff-issues.php'>← Back to Diagnostic Script</a></p>";
?>
