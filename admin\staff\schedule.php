<?php
/**
 * Staff Schedule Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/staff_panel_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Get staff ID from URL
$staffId = $_GET['id'] ?? '';
if (empty($staffId)) {
    $_SESSION['error'] = 'Staff ID is required. Please select a staff member from the staff list.';
    redirect('/admin/staff');
}

// Get staff member details
$staffUser = $database->fetch(
    "SELECT * FROM users WHERE id = ? AND role = 'STAFF'",
    [$staffId]
);

if (!$staffUser) {
    $_SESSION['error'] = 'Staff member not found. Please select a valid staff member from the staff list.';
    redirect('/admin/staff');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'update_schedule') {
            $schedule = [];
            $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

            foreach ($daysOfWeek as $day) {
                if (isset($_POST[$day . '_working']) && $_POST[$day . '_working'] === '1') {
                    $schedule[$day] = [
                        'is_working' => true,
                        'start_time' => $_POST[$day . '_start'],
                        'end_time' => $_POST[$day . '_end']
                    ];
                } else {
                    $schedule[$day] = [
                        'is_working' => false,
                        'start_time' => null,
                        'end_time' => null
                    ];
                }
            }

            updateStaffWorkingHours($staffId, $schedule);
            $message = 'Schedule updated successfully!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get current week start (Monday)
$weekStart = $_GET['week'] ?? date('Y-m-d', strtotime('monday this week'));

// Get staff schedule data
$profile = getStaffProfile($staffId);
$weeklySchedule = getStaffWeeklySchedule($staffId, $weekStart);

$pageTitle = "Schedule - " . $staffUser['name'];
include __DIR__ . '/../../includes/admin_header.php';
?>

<div class="min-h-screen bg-salon-black pt-20">
    <div class="py-10">
        <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-full lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="hidden lg:block lg:col-span-3 xl:col-span-2">
                <?php include __DIR__ . '/../../includes/admin_sidebar.php'; ?>
            </div>

            <main class="lg:col-span-9 xl:col-span-10">
                <div class="px-4 sm:px-6 lg:px-8">
                    <!-- Header -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-12 w-12">
                                    <div class="h-12 w-12 rounded-full bg-salon-gold flex items-center justify-center">
                                        <span class="text-lg font-medium text-black">
                                            <?= strtoupper(substr($staffUser['name'], 0, 2)) ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h1 class="text-2xl font-bold text-white">Schedule Management</h1>
                                    <p class="mt-1 text-sm text-gray-300"><?= htmlspecialchars($staffUser['name']) ?> - <?= htmlspecialchars($staffUser['email']) ?></p>
                                </div>
                            </div>
                            <div class="mt-4 sm:mt-0 flex gap-3">
                                <button onclick="openScheduleModal()" class="bg-salon-gold hover:bg-yellow-500 text-black px-6 py-3 rounded-lg font-semibold transition-colors">
                                    <i class="fas fa-clock mr-2"></i>Update Working Hours
                                </button>
                                <a href="<?= getBasePath() ?>/admin/staff"
                                   class="bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    Back to Staff
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Message Display -->
                    <?php if ($message): ?>
                        <div class="mb-6 p-4 rounded-lg <?= $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700' ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Week Navigation -->
                    <div class="bg-secondary-800 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <a href="?id=<?= $staffId ?>&week=<?= date('Y-m-d', strtotime($weekStart . ' -1 week')) ?>"
                                   class="text-gray-400 hover:text-white transition-colors">
                                    <i class="fas fa-chevron-left"></i> Previous Week
                                </a>
                                <h2 class="text-xl font-semibold text-white">
                                    Week of <?= date('F j, Y', strtotime($weekStart)) ?>
                                </h2>
                                <a href="?id=<?= $staffId ?>&week=<?= date('Y-m-d', strtotime($weekStart . ' +1 week')) ?>"
                                   class="text-gray-400 hover:text-white transition-colors">
                                    Next Week <i class="fas fa-chevron-right"></i>
                                </a>
                            </div>
                            <div>
                                <a href="?id=<?= $staffId ?>&week=<?= date('Y-m-d', strtotime('monday this week')) ?>"
                                   class="text-salon-gold hover:text-yellow-400 transition-colors">
                                    <i class="fas fa-calendar-day mr-1"></i>This Week
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Weekly Schedule Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-7 gap-4 mb-6">
                        <?php foreach ($weeklySchedule as $daySchedule): ?>
                            <div class="bg-secondary-800 rounded-lg p-4">
                                <div class="text-center mb-4">
                                    <h3 class="font-semibold text-white"><?= $daySchedule['dayName'] ?></h3>
                                    <p class="text-sm text-gray-400"><?= date('M j', strtotime($daySchedule['date'])) ?></p>

                                    <?php if ($daySchedule['workingHours'] && $daySchedule['workingHours']['is_working']): ?>
                                        <p class="text-xs text-salon-gold mt-1">
                                            <?= date('g:i A', strtotime($daySchedule['workingHours']['start_time'])) ?> -
                                            <?= date('g:i A', strtotime($daySchedule['workingHours']['end_time'])) ?>
                                        </p>
                                    <?php else: ?>
                                        <p class="text-xs text-gray-500 mt-1">Day Off</p>
                                    <?php endif; ?>
                                </div>

                                <div class="space-y-2">
                                    <?php if (!$daySchedule['workingHours'] || !$daySchedule['workingHours']['is_working']): ?>
                                        <div class="text-center py-8">
                                            <i class="fas fa-bed text-2xl text-gray-600 mb-2"></i>
                                            <p class="text-xs text-gray-500">Not Working</p>
                                        </div>
                                    <?php elseif (empty($daySchedule['appointments'])): ?>
                                        <div class="text-center py-8">
                                            <i class="fas fa-calendar-plus text-2xl text-gray-600 mb-2"></i>
                                            <p class="text-xs text-gray-500">No Appointments</p>
                                        </div>
                                    <?php else: ?>
                                        <?php foreach ($daySchedule['appointments'] as $appointment): ?>
                                            <div class="bg-secondary-700 rounded p-2">
                                                <div class="text-xs font-medium text-white">
                                                    <?= date('g:i A', strtotime($appointment['start_time'])) ?>
                                                </div>
                                                <div class="text-xs text-gray-300 truncate">
                                                    <?= htmlspecialchars($appointment['customer_name']) ?>
                                                </div>
                                                <div class="text-xs text-gray-400 truncate">
                                                    <?= htmlspecialchars($appointment['service_name']) ?>
                                                </div>
                                                <div class="mt-1">
                                                    <span class="inline-block px-1.5 py-0.5 rounded text-xs font-medium
                                                        <?php
                                                        switch($appointment['status']) {
                                                            case 'CONFIRMED': echo 'bg-blue-100 text-blue-800'; break;
                                                            case 'IN_PROGRESS': echo 'bg-yellow-100 text-yellow-800'; break;
                                                            case 'COMPLETED': echo 'bg-green-100 text-green-800'; break;
                                                            case 'CANCELLED': echo 'bg-red-100 text-red-800'; break;
                                                            default: echo 'bg-gray-100 text-gray-800';
                                                        }
                                                        ?>">
                                                        <?= $appointment['status'] ?>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Current Bookings -->
                    <div class="bg-secondary-800 shadow rounded-lg p-6">
                        <h2 class="text-lg font-semibold text-white mb-4">Upcoming Bookings</h2>

                        <?php
                        $upcomingBookings = $database->fetchAll(
                            "SELECT b.*, u.name as customer_name, s.name as service_name
                             FROM bookings b
                             LEFT JOIN users u ON b.user_id = u.id
                             LEFT JOIN services s ON b.service_id = s.id
                             WHERE b.staff_id = ? AND b.date >= CURDATE() AND b.status IN ('PENDING', 'CONFIRMED')
                             ORDER BY b.date ASC, b.start_time ASC
                             LIMIT 10",
                            [$staffId]
                        );
                        ?>

                        <?php if (empty($upcomingBookings)): ?>
                            <p class="text-gray-400">No upcoming bookings</p>
                        <?php else: ?>
                            <div class="space-y-3">
                                <?php foreach ($upcomingBookings as $booking): ?>
                                    <div class="bg-secondary-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h4 class="text-white font-medium"><?= htmlspecialchars($booking['service_name']) ?></h4>
                                                <p class="text-gray-300 text-sm">Customer: <?= htmlspecialchars($booking['customer_name']) ?></p>
                                                <p class="text-gray-400 text-sm">
                                                    <?= date('M j, Y', strtotime($booking['date'])) ?> at
                                                    <?= date('g:i A', strtotime($booking['start_time'])) ?> -
                                                    <?= date('g:i A', strtotime($booking['end_time'])) ?>
                                                </p>
                                            </div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                <?= $booking['status'] === 'CONFIRMED' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800' ?>">
                                                <?= ucfirst(strtolower($booking['status'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<!-- Schedule Modal -->
<div id="scheduleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="bg-secondary-800 rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-white">Update Working Hours</h3>
                    <button onclick="closeScheduleModal()" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="update_schedule">

                    <?php
                    $daysOfWeek = [
                        'monday' => 'Monday',
                        'tuesday' => 'Tuesday',
                        'wednesday' => 'Wednesday',
                        'thursday' => 'Thursday',
                        'friday' => 'Friday',
                        'saturday' => 'Saturday',
                        'sunday' => 'Sunday'
                    ];

                    // Get current working hours
                    $currentSchedule = [];
                    foreach ($weeklySchedule as $daySchedule) {
                        $dayKey = strtolower($daySchedule['dayName']);
                        $currentSchedule[$dayKey] = $daySchedule['workingHours'];
                    }
                    ?>

                    <?php foreach ($daysOfWeek as $dayKey => $dayName): ?>
                        <div class="border border-secondary-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-medium text-white"><?= $dayName ?></h4>
                                <label class="flex items-center">
                                    <input type="checkbox" name="<?= $dayKey ?>_working" value="1"
                                           <?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey] && $currentSchedule[$dayKey]['is_working']) ? 'checked' : '' ?>
                                           onchange="toggleDaySchedule('<?= $dayKey ?>')"
                                           class="rounded border-secondary-600 text-salon-gold focus:ring-salon-gold focus:ring-offset-0">
                                    <span class="ml-2 text-sm text-gray-300">Working Day</span>
                                </label>
                            </div>

                            <div id="<?= $dayKey ?>_schedule" class="grid grid-cols-2 gap-4 <?= (!isset($currentSchedule[$dayKey]) || !$currentSchedule[$dayKey] || !$currentSchedule[$dayKey]['is_working']) ? 'hidden' : '' ?>">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Start Time</label>
                                    <input type="time" name="<?= $dayKey ?>_start"
                                           value="<?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey]) ? $currentSchedule[$dayKey]['start_time'] : '09:00' ?>"
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">End Time</label>
                                    <input type="time" name="<?= $dayKey ?>_end"
                                           value="<?= (isset($currentSchedule[$dayKey]) && $currentSchedule[$dayKey]) ? $currentSchedule[$dayKey]['end_time'] : '17:00' ?>"
                                           class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-secondary-700">
                        <button type="button" onclick="closeScheduleModal()"
                                class="px-6 py-2 bg-secondary-700 hover:bg-secondary-600 text-white rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-2 bg-salon-gold hover:bg-yellow-500 text-black rounded-lg font-semibold transition-colors">
                            Update Schedule
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function openScheduleModal() {
        document.getElementById('scheduleModal').classList.remove('hidden');
    }

    function closeScheduleModal() {
        document.getElementById('scheduleModal').classList.add('hidden');
    }

    function toggleDaySchedule(day) {
        const checkbox = document.querySelector(`input[name="${day}_working"]`);
        const scheduleDiv = document.getElementById(`${day}_schedule`);

        if (checkbox.checked) {
            scheduleDiv.classList.remove('hidden');
        } else {
            scheduleDiv.classList.add('hidden');
        }
    }

    // Close modal when clicking outside
    document.getElementById('scheduleModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeScheduleModal();
        }
    });
</script>

<?php include __DIR__ . '/../../includes/admin_footer.php'; ?>
