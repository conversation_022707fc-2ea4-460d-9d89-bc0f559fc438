<?php
/**
 * Admin Service Categories API
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../../config/app.php';

// Set JSON header
header('Content-Type: application/json');

// Require admin authentication
if (!isLoggedIn() || !hasRole('ADMIN')) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Remove base path if present
$basePath = '/flix-php';
if (strpos($path, $basePath) === 0) {
    $path = substr($path, strlen($basePath));
}

// Extract category ID from path if present
$pathParts = explode('/', trim($path, '/'));
$categoryId = null;

// Look for category ID in the path
if (count($pathParts) >= 4 && $pathParts[3] !== '') {
    $categoryId = $pathParts[3];
} elseif (isset($_GET['id'])) {
    $categoryId = $_GET['id'];
}

try {
    switch ($method) {
        case 'GET':
            if ($categoryId) {
                // Get single category
                $category = getServiceCategoryById($categoryId);
                if ($category) {
                    echo json_encode($category);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Category not found']);
                }
            } else {
                // Get all categories with optional filters
                $activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === 'true';
                $withCount = isset($_GET['with_count']) && $_GET['with_count'] === 'true';
                
                if ($withCount) {
                    $categories = getServiceCategoriesWithCount();
                } else {
                    $categories = getAllServiceCategories($activeOnly);
                }
                
                echo json_encode([
                    'success' => true,
                    'categories' => $categories,
                    'total' => count($categories)
                ]);
            }
            break;
            
        case 'POST':
            // Create new category
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                // Try to get data from form submission
                $input = $_POST;
            }
            
            if (empty($input['name'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Category name is required']);
                break;
            }
            
            $result = createServiceCategory($input);
            
            if ($result['success']) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'message' => 'Category created successfully',
                    'id' => $result['id']
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'PUT':
            // Update category
            if (!$categoryId) {
                http_response_code(400);
                echo json_encode(['error' => 'Category ID required']);
                break;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON data']);
                break;
            }
            
            $result = updateServiceCategory($categoryId, $input);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Category updated successfully'
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'PATCH':
            // Toggle category status
            if (!$categoryId) {
                http_response_code(400);
                echo json_encode(['error' => 'Category ID required']);
                break;
            }
            
            $result = toggleServiceCategoryStatus($categoryId);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Category status updated successfully',
                    'new_status' => $result['new_status']
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => $result['error']]);
            }
            break;
            
        case 'DELETE':
            // Delete category
            if (!$categoryId) {
                http_response_code(400);
                echo json_encode(['error' => 'Category ID required']);
                break;
            }
            
            $result = deleteServiceCategory($categoryId);
            
            if ($result['success']) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Category deleted successfully'
                ]);
            } else {
                http_response_code(400);
                echo json_encode($result); // Return full error details for better UX
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Service Categories API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
