-- Create Customer Tiers Table
-- This table stores customer tier definitions for the loyalty program

CREATE TABLE IF NOT EXISTS customer_tiers (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    min_spent INT NOT NULL DEFAULT 0 COMMENT 'Minimum amount spent in TSH to qualify for this tier',
    points_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.00 COMMENT 'Points multiplier for this tier',
    benefits TEXT COMMENT 'Comma-separated list of benefits',
    color VARCHAR(50) NOT NULL DEFAULT 'text-gray-600' COMMENT 'Tailwind CSS text color class',
    bg_color VARCHAR(50) NOT NULL DEFAULT 'bg-gray-100' COMMENT 'Tailwind CSS background color class',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0 COMMENT 'Order for displaying tiers',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_customer_tiers_active_sort ON customer_tiers(is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_customer_tiers_min_spent ON customer_tiers(min_spent);

-- Insert default customer tiers if table is empty
INSERT INTO customer_tiers (id, name, min_spent, points_multiplier, benefits, color, bg_color, sort_order)
SELECT * FROM (
    SELECT 
        'tier-bronze-001' as id,
        'Bronze' as name,
        0 as min_spent,
        1.0 as points_multiplier,
        'Basic rewards,Birthday bonus' as benefits,
        'text-orange-600' as color,
        'bg-orange-100' as bg_color,
        1 as sort_order
    UNION ALL
    SELECT 
        'tier-silver-002' as id,
        'Silver' as name,
        200000 as min_spent,
        1.2 as points_multiplier,
        '20% bonus points,Priority booking,Birthday bonus' as benefits,
        'text-gray-600' as color,
        'bg-gray-100' as bg_color,
        2 as sort_order
    UNION ALL
    SELECT 
        'tier-gold-003' as id,
        'Gold' as name,
        500000 as min_spent,
        1.5 as points_multiplier,
        '50% bonus points,Priority booking,Exclusive offers,Birthday bonus' as benefits,
        'text-yellow-600' as color,
        'bg-yellow-100' as bg_color,
        3 as sort_order
    UNION ALL
    SELECT 
        'tier-vip-004' as id,
        'VIP' as name,
        1000000 as min_spent,
        2.0 as points_multiplier,
        'Double points,VIP treatment,Exclusive services,Personal stylist,Birthday bonus' as benefits,
        'text-purple-600' as color,
        'bg-purple-100' as bg_color,
        4 as sort_order
) AS default_tiers
WHERE NOT EXISTS (SELECT 1 FROM customer_tiers LIMIT 1);
